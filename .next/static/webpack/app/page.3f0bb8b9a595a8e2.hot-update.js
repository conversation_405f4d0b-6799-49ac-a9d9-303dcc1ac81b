"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/main/home/<USER>/HomeMainHero.tsx":
/*!********************************************************!*\
  !*** ./src/components/main/home/<USER>/HomeMainHero.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n// Style constants\nconst LOGO_SIZE = '40px';\nconst DRAG_SENSITIVITY = 0.4;\nconst TEXT_WIDTH_MULTIPLIER = 10;\nconst MIN_TEXT_WIDTH = 60;\nconst TEXT_PADDING = 20;\nconst LOGO_GAP = 0;\nconst logoStyle = {\n    width: LOGO_SIZE,\n    height: LOGO_SIZE,\n    borderRadius: '50%',\n    backgroundSize: 'cover',\n    backgroundRepeat: 'no-repeat',\n    backgroundPosition: 'center',\n    backgroundColor: 'transparent',\n    overflow: 'hidden',\n    border: '1px solid transparent',\n    background: 'linear-gradient(var(--bg-color), var(--bg-color)) padding-box, linear-gradient(to left, var(--accent-color), var(--accent-secondary-color)) border-box'\n};\nconst clientItemStyle = (textLength)=>{\n    const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n    const totalWidth = parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n    return {\n        display: 'flex',\n        alignItems: 'center',\n        gap: \"\".concat(LOGO_GAP, \"px\"),\n        width: \"\".concat(totalWidth, \"px\"),\n        flexShrink: 0,\n        marginRight: '0px',\n        overflow: 'hidden'\n    };\n};\nconst companyNameStyle = {\n    color: 'white',\n    fontSize: '18px',\n    fontWeight: 'normal',\n    fontFamily: 'sans-serif',\n    whiteSpace: 'nowrap',\n    overflow: 'hidden',\n    textOverflow: 'ellipsis',\n    flex: 1\n};\nconst containerStyle = {\n    overflow: 'hidden',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '0px' // No gap between blocks\n};\nconst trackStyle = (isDragging, scrollLeft)=>({\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0px',\n        transition: isDragging ? 'none' : 'transform 2s ease-in-out',\n        transform: \"translateX(-\".concat(scrollLeft, \"px)\")\n    });\n// Logo component\nconst ClientLogo = (param)=>{\n    let { logourl } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            width: LOGO_SIZE,\n            height: LOGO_SIZE,\n            borderRadius: '50%',\n            backgroundImage: \"url(\".concat(logourl || \"/images/icon-testimonial-logo.svg\", \")\"),\n            backgroundSize: '80%',\n            backgroundRepeat: 'no-repeat',\n            backgroundPosition: 'center',\n            backgroundColor: 'transparent',\n            overflow: 'hidden',\n            border: '3px solid',\n            borderImage: 'linear-gradient(45deg, var(--accent-color), var(--accent-secondary-color)) 1',\n            boxShadow: '0 0 10px rgba(0,0,0,0.1)',\n            transition: 'all 0.3s ease'\n        }\n    }, void 0, false, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n        lineNumber: 75,\n        columnNumber: 3\n    }, undefined);\n};\n_c = ClientLogo;\n// Client item component\nconst ClientItem = (param)=>{\n    let { client, index } = param;\n    const textLength = client.companyname.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: clientItemStyle(textLength),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {\n                logourl: client.logourl\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: companyNameStyle,\n                children: client.companyname\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, client.id || index, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = ClientItem;\n// Helper function to calculate client width\nconst calculateClientWidth = (textLength)=>{\n    const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n    return parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n};\n// Helper function to calculate total width\nconst calculateTotalWidth = (clients)=>{\n    return clients.reduce((sum, client)=>{\n        return sum + calculateClientWidth(client.companyname.length);\n    }, 0);\n};\nconst HomeMainHero = ()=>{\n    _s();\n    const [clients, setClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startX, setStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [scrollLeft, setScrollLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(160); // Start with first client fully hidden\n    const [autoScrollInterval, setAutoScrollInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            const fetchClients = {\n                \"HomeMainHero.useEffect.fetchClients\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const response = await fetch('/api/clients');\n                        const data = await response.json();\n                        if (data.success) {\n                            setClients(data.clients || []);\n                            // Set initial scroll position to hide first client\n                            if (data.clients && data.clients.length > 0) {\n                                const firstClientWidth = calculateClientWidth(data.clients[0].companyname.length);\n                                setScrollLeft(firstClientWidth);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error fetching clients:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"HomeMainHero.useEffect.fetchClients\"];\n            fetchClients();\n        }\n    }[\"HomeMainHero.useEffect\"], []);\n    // Auto-scroll function\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            if (clients.length === 0 || isDragging) return;\n            const interval = setInterval({\n                \"HomeMainHero.useEffect.interval\": ()=>{\n                    setScrollLeft({\n                        \"HomeMainHero.useEffect.interval\": (prevScrollLeft)=>{\n                            const totalWidth = calculateTotalWidth(clients);\n                            const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                            let newScrollLeft = prevScrollLeft + firstClientWidth;\n                            if (newScrollLeft >= totalWidth) {\n                                newScrollLeft = firstClientWidth;\n                            }\n                            return newScrollLeft;\n                        }\n                    }[\"HomeMainHero.useEffect.interval\"]);\n                }\n            }[\"HomeMainHero.useEffect.interval\"], 5000);\n            setAutoScrollInterval(interval);\n            return ({\n                \"HomeMainHero.useEffect\": ()=>{\n                    if (interval) {\n                        clearInterval(interval);\n                    }\n                }\n            })[\"HomeMainHero.useEffect\"];\n        }\n    }[\"HomeMainHero.useEffect\"], [\n        clients.length,\n        isDragging\n    ]);\n    // Cleanup interval on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            return ({\n                \"HomeMainHero.useEffect\": ()=>{\n                    if (autoScrollInterval) {\n                        clearInterval(autoScrollInterval);\n                    }\n                }\n            })[\"HomeMainHero.useEffect\"];\n        }\n    }[\"HomeMainHero.useEffect\"], [\n        autoScrollInterval\n    ]);\n    const handleMouseDown = (e)=>{\n        setIsDragging(true);\n        setStartX(e.pageX);\n        // Stop auto-scroll when user starts dragging\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops dragging\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    const totalWidth = calculateTotalWidth(clients);\n                    const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                    let newScrollLeft = prevScrollLeft + firstClientWidth;\n                    if (newScrollLeft >= totalWidth) {\n                        newScrollLeft = firstClientWidth;\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const handleMouseLeave = ()=>{\n        setIsDragging(false);\n    };\n    const handleTouchStart = (e)=>{\n        setIsDragging(true);\n        setStartX(e.touches[0].pageX);\n        // Stop auto-scroll when user starts touching\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleTouchMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.touches[0].pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleTouchEnd = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops touching\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    const totalWidth = calculateTotalWidth(clients);\n                    const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                    let newScrollLeft = prevScrollLeft + firstClientWidth;\n                    if (newScrollLeft >= totalWidth) {\n                        newScrollLeft = firstClientWidth;\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const renderClientItems = ()=>{\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: clientItemStyle(7),\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            ...companyNameStyle,\n                            fontSize: '26px',\n                            fontWeight: 'bold'\n                        },\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 303,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (clients.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: clientItemStyle(12),\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            ...companyNameStyle,\n                            fontSize: '26px',\n                            fontWeight: 'bold'\n                        },\n                        children: \"No clients yet\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 314,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, client.id || index, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 11\n                    }, undefined)),\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, \"duplicate-\".concat(client.id || index), false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 11\n                    }, undefined))\n            ]\n        }, void 0, true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"hero\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid-lines\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-1\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-2\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-3\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-4\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-5\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                    lineNumber: 341,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row align-items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-content\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"section-title section-title-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"wow fadeInUp\",\n                                                    children: \"Enterprise-grade software & web development solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.2s\",\n                                                    \"data-cursor\": \"-opaque\",\n                                                    children: [\n                                                        \"Advanced digital solutions that\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"accelerate your growth\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.4s\",\n                                                    children: \"Transform your vision into scalable applications and sophisticated web platforms. Professional development services that deliver measurable results.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hero-btn wow fadeInUp\",\n                                            \"data-wow-delay\": \"0.6s\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/contact\",\n                                                    className: \"btn-default btn-highlighted\",\n                                                    children: \"Get Free Assessment\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/services\",\n                                                    className: \"btn-default\",\n                                                    children: \"View our services\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-company-slider\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"We're Trusted by more than \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"counter\",\n                                                    children: clients.length\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 46\n                                                }, undefined),\n                                                \"+ companies\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                ...containerStyle,\n                                                cursor: isDragging ? 'grabbing' : 'grab'\n                                            },\n                                            onMouseDown: handleMouseDown,\n                                            onMouseMove: handleMouseMove,\n                                            onMouseUp: handleMouseUp,\n                                            onMouseLeave: handleMouseLeave,\n                                            onTouchStart: handleTouchStart,\n                                            onTouchMove: handleTouchMove,\n                                            onTouchEnd: handleTouchEnd,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: trackStyle(isDragging, scrollLeft),\n                                                children: renderClientItems()\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 387,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n            lineNumber: 339,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(HomeMainHero, \"+cK3ts9BkIzmNmquLDoGqVEeSrY=\");\n_c2 = HomeMainHero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeMainHero);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ClientLogo\");\n$RefreshReg$(_c1, \"ClientItem\");\n$RefreshReg$(_c2, \"HomeMainHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/home/<USER>/HomeMainHero.tsx\n"));

/***/ })

});