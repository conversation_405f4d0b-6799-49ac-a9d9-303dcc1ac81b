"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/main/shared/ScrollingTicker.tsx":
/*!********************************************************!*\
  !*** ./src/components/main/shared/ScrollingTicker.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScrollingTicker)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n// Fallback industries in case API fails or no featured services\nconst fallbackIndustries = [\n    'Healthcare',\n    'Finance and Banking',\n    'Legal and Law Firms',\n    'Government and Public Sector',\n    'Technology and Software'\n];\nfunction ScrollingTicker() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"our-scrolling-ticker\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"scrolling-ticker-box\",\n                style: {\n                    overflow: 'hidden'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"scrolling-content\",\n                        children: [\n                            industries.map((industry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: \"/images/star-icon.svg\",\n                                            alt: \"Star\",\n                                            width: 20,\n                                            height: 20,\n                                            style: {\n                                                display: 'inline-block',\n                                                verticalAlign: 'middle',\n                                                marginRight: '8px'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 17\n                                        }, this),\n                                        industry\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 15\n                                }, this)),\n                            industries.map((industry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: \"/images/star-icon.svg\",\n                                            alt: \"Star\",\n                                            width: 20,\n                                            height: 20,\n                                            style: {\n                                                display: 'inline-block',\n                                                verticalAlign: 'middle',\n                                                marginRight: '8px'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 17\n                                        }, this),\n                                        industry\n                                    ]\n                                }, \"duplicate-\".concat(index), true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"scrolling-content\",\n                        children: [\n                            industries.map((industry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: \"/images/star-icon.svg\",\n                                            alt: \"Star\",\n                                            width: 20,\n                                            height: 20,\n                                            style: {\n                                                display: 'inline-block',\n                                                verticalAlign: 'middle',\n                                                marginRight: '8px'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 17\n                                        }, this),\n                                        industry\n                                    ]\n                                }, \"second-\".concat(index), true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, this)),\n                            industries.map((industry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: \"/images/star-icon.svg\",\n                                            alt: \"Star\",\n                                            width: 20,\n                                            height: 20,\n                                            style: {\n                                                display: 'inline-block',\n                                                verticalAlign: 'middle',\n                                                marginRight: '8px'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 17\n                                        }, this),\n                                        industry\n                                    ]\n                                }, \"second-duplicate-\".concat(index), true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_c = ScrollingTicker;\nvar _c;\n$RefreshReg$(_c, \"ScrollingTicker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/shared/ScrollingTicker.tsx\n"));

/***/ })

});