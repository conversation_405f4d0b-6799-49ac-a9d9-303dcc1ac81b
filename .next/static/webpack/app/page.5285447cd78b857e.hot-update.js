"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/main/shared/ScrollingTicker.tsx":
/*!********************************************************!*\
  !*** ./src/components/main/shared/ScrollingTicker.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScrollingTicker)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// Fallback industries in case API fails or no featured services\nconst fallbackIndustries = [\n    'Healthcare',\n    'Finance and Banking',\n    'Legal and Law Firms',\n    'Government and Public Sector',\n    'Technology and Software'\n];\nfunction ScrollingTicker() {\n    _s();\n    const [displayItems, setDisplayItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(fallbackIndustries);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollingTicker.useEffect\": ()=>{\n            const fetchFeaturedServices = {\n                \"ScrollingTicker.useEffect.fetchFeaturedServices\": async ()=>{\n                    try {\n                        const response = await fetch('/api/services/featured');\n                        const data = await response.json();\n                        if (data.success && data.services && data.services.length > 0) {\n                            // Extract service names for display\n                            const serviceNames = data.services.map({\n                                \"ScrollingTicker.useEffect.fetchFeaturedServices.serviceNames\": (service)=>service.name\n                            }[\"ScrollingTicker.useEffect.fetchFeaturedServices.serviceNames\"]);\n                            setDisplayItems(serviceNames);\n                        } else {\n                            // Use fallback if no featured services\n                            setDisplayItems(fallbackIndustries);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching featured services:', error);\n                        // Use fallback on error\n                        setDisplayItems(fallbackIndustries);\n                    }\n                }\n            }[\"ScrollingTicker.useEffect.fetchFeaturedServices\"];\n            fetchFeaturedServices();\n        }\n    }[\"ScrollingTicker.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"our-scrolling-ticker\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"scrolling-ticker-box\",\n                style: {\n                    overflow: 'hidden'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"scrolling-content\",\n                        children: [\n                            displayItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: \"/images/star-icon.svg\",\n                                            alt: \"Star\",\n                                            width: 20,\n                                            height: 20,\n                                            style: {\n                                                display: 'inline-block',\n                                                verticalAlign: 'middle',\n                                                marginRight: '8px'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 17\n                                        }, this),\n                                        item\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 15\n                                }, this)),\n                            displayItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: \"/images/star-icon.svg\",\n                                            alt: \"Star\",\n                                            width: 20,\n                                            height: 20,\n                                            style: {\n                                                display: 'inline-block',\n                                                verticalAlign: 'middle',\n                                                marginRight: '8px'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 17\n                                        }, this),\n                                        item\n                                    ]\n                                }, \"duplicate-\".concat(index), true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"scrolling-content\",\n                        children: [\n                            displayItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: \"/images/star-icon.svg\",\n                                            alt: \"Star\",\n                                            width: 20,\n                                            height: 20,\n                                            style: {\n                                                display: 'inline-block',\n                                                verticalAlign: 'middle',\n                                                marginRight: '8px'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this),\n                                        item\n                                    ]\n                                }, \"second-\".concat(index), true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 15\n                                }, this)),\n                            displayItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: \"/images/star-icon.svg\",\n                                            alt: \"Star\",\n                                            width: 20,\n                                            height: 20,\n                                            style: {\n                                                display: 'inline-block',\n                                                verticalAlign: 'middle',\n                                                marginRight: '8px'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 17\n                                        }, this),\n                                        item\n                                    ]\n                                }, \"second-duplicate-\".concat(index), true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                lineNumber: 60,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_s(ScrollingTicker, \"kTlnL9DiYV8sQo5L7QbxKPmrV4U=\");\n_c = ScrollingTicker;\nvar _c;\n$RefreshReg$(_c, \"ScrollingTicker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/shared/ScrollingTicker.tsx\n"));

/***/ })

});