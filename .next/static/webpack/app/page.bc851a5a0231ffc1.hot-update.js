"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/main/home/<USER>/HomeImageHero.tsx":
/*!*********************************************************!*\
  !*** ./src/components/main/home/<USER>/HomeImageHero.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst HomeImageHero = ()=>{\n    _s();\n    const [bgImage, setBgImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('/images/hero-bg.jpg'); // Default fallback\n    const [clients, setClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [clientsLoading, setClientsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startX, setStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentClientIndex, setCurrentClientIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // Track which client is currently visible\n    const [scrollLeft, setScrollLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // Start at beginning\n    const [autoScrollInterval, setAutoScrollInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Style constants\n    const LOGO_SIZE = '60px'; // Increased from 40px to 60px\n    const DRAG_SENSITIVITY = 0.4;\n    const TEXT_WIDTH_MULTIPLIER = 10;\n    const MIN_TEXT_WIDTH = 60;\n    const TEXT_PADDING = 5; // Reduced from 20 to 5\n    const LOGO_GAP = 0; // No gap between logo and client name\n    // Helper function to calculate client width\n    const calculateClientWidth = (textLength)=>{\n        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n        return parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n    };\n    // Helper function to calculate total width\n    const calculateTotalWidth = (clients)=>{\n        return clients.reduce((sum, client)=>{\n            return sum + calculateClientWidth(client.companyname.length);\n        }, 0);\n    };\n    // Helper function to calculate scroll position for a specific client index\n    const calculateScrollPositionForClient = (clients, targetIndex)=>{\n        let scrollPosition = 0;\n        for(let i = 0; i < targetIndex && i < clients.length; i++){\n            scrollPosition += calculateClientWidth(clients[i].companyname.length);\n        }\n        return scrollPosition;\n    };\n    const logoContainerStyle = {\n        width: LOGO_SIZE,\n        height: LOGO_SIZE,\n        borderRadius: '50%',\n        background: 'linear-gradient(var(--bg-color), var(--bg-color)) padding-box, linear-gradient(to left, var(--accent-color), var(--accent-secondary-color)) border-box',\n        border: '1px solid transparent',\n        padding: '2px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n    };\n    const logoStyle = {\n        width: 'calc(100% - 4px)',\n        height: 'calc(100% - 4px)',\n        borderRadius: '50%',\n        backgroundSize: 'cover',\n        backgroundRepeat: 'no-repeat',\n        backgroundPosition: 'center',\n        backgroundColor: 'transparent',\n        overflow: 'hidden'\n    };\n    // Logo component\n    const ClientLogo = (param)=>{\n        let { logourl } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: logoContainerStyle,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    ...logoStyle,\n                    backgroundImage: \"url(\".concat(logourl || \"/images/icon-testimonial-logo.svg\", \")\")\n                }\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n            lineNumber: 74,\n            columnNumber: 5\n        }, undefined);\n    };\n    const clientItemStyle = (textLength)=>{\n        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n        const totalWidth = parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n        return {\n            display: 'flex',\n            alignItems: 'center',\n            gap: \"\".concat(LOGO_GAP, \"px\"),\n            width: \"\".concat(totalWidth, \"px\"),\n            flexShrink: 0,\n            marginRight: '0px',\n            overflow: 'hidden'\n        };\n    };\n    const companyNameStyle = {\n        color: 'white',\n        fontSize: '18px',\n        fontWeight: 'normal',\n        fontFamily: 'sans-serif',\n        whiteSpace: 'nowrap',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n        flex: 1\n    };\n    const containerStyle = {\n        overflow: 'hidden',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '20px' // Consistent gap between all client blocks\n    };\n    // Client item component\n    const ClientItem = (param)=>{\n        let { client, index } = param;\n        const textLength = client.companyname.length;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: clientItemStyle(textLength),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {\n                    logourl: client.logourl\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: companyNameStyle,\n                    children: client.companyname\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, client.id || index, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, undefined);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeImageHero.useEffect\": ()=>{\n            const fetchHeroImage = {\n                \"HomeImageHero.useEffect.fetchHeroImage\": async ()=>{\n                    try {\n                        const response = await fetch('/api/admin/settings/hero-media');\n                        if (response.ok) {\n                            var _data_media;\n                            const data = await response.json();\n                            if (data.success && ((_data_media = data.media) === null || _data_media === void 0 ? void 0 : _data_media.hero_main_bg_image)) {\n                                setBgImage(data.media.hero_main_bg_image);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error fetching hero image:', error);\n                    // Keep default image\n                    }\n                }\n            }[\"HomeImageHero.useEffect.fetchHeroImage\"];\n            fetchHeroImage();\n        }\n    }[\"HomeImageHero.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeImageHero.useEffect\": ()=>{\n            const fetchClients = {\n                \"HomeImageHero.useEffect.fetchClients\": async ()=>{\n                    try {\n                        setClientsLoading(true);\n                        // Fetch all active clients without limit, sorted by name\n                        const response = await fetch('/api/clients?limit=0');\n                        const data = await response.json();\n                        if (data.success) {\n                            // Sort clients by company name to ensure alphabetical order\n                            const sortedClients = (data.clients || []).sort({\n                                \"HomeImageHero.useEffect.fetchClients.sortedClients\": (a, b)=>a.companyname.localeCompare(b.companyname)\n                            }[\"HomeImageHero.useEffect.fetchClients.sortedClients\"]);\n                            setClients(sortedClients);\n                            // Start with the first client fully visible\n                            setCurrentClientIndex(0);\n                            setScrollLeft(0);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching clients:', error);\n                    } finally{\n                        setClientsLoading(false);\n                    }\n                }\n            }[\"HomeImageHero.useEffect.fetchClients\"];\n            fetchClients();\n        }\n    }[\"HomeImageHero.useEffect\"], []);\n    // Auto-scroll function - moves one client at a time as complete blocks\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeImageHero.useEffect\": ()=>{\n            if (clients.length === 0 || isDragging) return;\n            const interval = setInterval({\n                \"HomeImageHero.useEffect.interval\": ()=>{\n                    setCurrentClientIndex({\n                        \"HomeImageHero.useEffect.interval\": (prevIndex)=>{\n                            const nextIndex = (prevIndex + 1) % clients.length;\n                            // Calculate exact scroll position for the next client\n                            const nextScrollPosition = calculateScrollPositionForClient(clients, nextIndex);\n                            setScrollLeft(nextScrollPosition);\n                            return nextIndex;\n                        }\n                    }[\"HomeImageHero.useEffect.interval\"]);\n                }\n            }[\"HomeImageHero.useEffect.interval\"], 5000);\n            setAutoScrollInterval(interval);\n            return ({\n                \"HomeImageHero.useEffect\": ()=>{\n                    if (interval) {\n                        clearInterval(interval);\n                    }\n                }\n            })[\"HomeImageHero.useEffect\"];\n        }\n    }[\"HomeImageHero.useEffect\"], [\n        clients.length,\n        isDragging\n    ]);\n    // Cleanup interval on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeImageHero.useEffect\": ()=>{\n            return ({\n                \"HomeImageHero.useEffect\": ()=>{\n                    if (autoScrollInterval) {\n                        clearInterval(autoScrollInterval);\n                    }\n                }\n            })[\"HomeImageHero.useEffect\"];\n        }\n    }[\"HomeImageHero.useEffect\"], [\n        autoScrollInterval\n    ]);\n    const handleMouseDown = (e)=>{\n        setIsDragging(true);\n        setStartX(e.pageX);\n        // Stop auto-scroll when user starts dragging\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops dragging\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setCurrentClientIndex((prevIndex)=>{\n                    const nextIndex = (prevIndex + 1) % clients.length;\n                    // Calculate exact scroll position for the next client\n                    const nextScrollPosition = calculateScrollPositionForClient(clients, nextIndex);\n                    setScrollLeft(nextScrollPosition);\n                    return nextIndex;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const handleMouseLeave = ()=>{\n        setIsDragging(false);\n    };\n    const handleTouchStart = (e)=>{\n        setIsDragging(true);\n        setStartX(e.touches[0].pageX);\n        // Stop auto-scroll when user starts touching\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleTouchMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.touches[0].pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleTouchEnd = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops touching\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    const totalWidth = calculateTotalWidth(clients);\n                    const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                    let newScrollLeft = prevScrollLeft + firstClientWidth;\n                    if (newScrollLeft >= totalWidth) {\n                        newScrollLeft = firstClientWidth;\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const renderClientItems = ()=>{\n        if (clientsLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0px',\n                    width: '140px',\n                    flexShrink: 0,\n                    marginRight: '0px',\n                    overflow: 'hidden'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            fontSize: '26px',\n                            fontWeight: 'bold',\n                            fontFamily: 'sans-serif',\n                            whiteSpace: 'nowrap',\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            flex: 1\n                        },\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                lineNumber: 321,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (clients.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0px',\n                    width: '200px',\n                    flexShrink: 0,\n                    marginRight: '0px',\n                    overflow: 'hidden'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            fontSize: '26px',\n                            fontWeight: 'bold',\n                            fontFamily: 'sans-serif',\n                            whiteSpace: 'nowrap',\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            flex: 1\n                        },\n                        children: \"No clients yet\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                lineNumber: 349,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, client.id || index, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 11\n                    }, undefined)),\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, \"duplicate-\".concat(client.id || index), false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 11\n                    }, undefined))\n            ]\n        }, void 0, true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"hero hero-bg-image parallaxie\",\n            style: {\n                backgroundImage: \"url(\".concat(bgImage, \")\"),\n                backgroundSize: 'cover',\n                backgroundPosition: 'center',\n                backgroundRepeat: 'no-repeat'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row align-items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-lg-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hero-content\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"section-title section-title-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"wow fadeInUp\",\n                                                children: \"Enterprise-grade software & web development solutions\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"wow fadeInUp\",\n                                                \"data-wow-delay\": \"0.2s\",\n                                                \"data-cursor\": \"-opaque\",\n                                                children: [\n                                                    \"Advanced digital solutions that\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"accelerate your growth\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"wow fadeInUp\",\n                                                \"data-wow-delay\": \"0.4s\",\n                                                children: \"Transform your vision into scalable applications and sophisticated web platforms. Professional development services that deliver measurable results.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hero-btn wow fadeInUp\",\n                                        \"data-wow-delay\": \"0.6s\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/main/contact\",\n                                                className: \"btn-default btn-highlighted\",\n                                                children: \"Get Free Assessment\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/main/services\",\n                                                className: \"btn-default\",\n                                                children: \"View our services\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-lg-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hero-company-slider\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"We're Trusted by more than \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"counter\",\n                                                children: clients.length\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 46\n                                            }, undefined),\n                                            \"+ companies\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            overflow: 'hidden',\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0px',\n                                            position: 'relative',\n                                            cursor: isDragging ? 'grabbing' : 'grab'\n                                        },\n                                        onMouseDown: handleMouseDown,\n                                        onMouseMove: handleMouseMove,\n                                        onMouseUp: handleMouseUp,\n                                        onMouseLeave: handleMouseLeave,\n                                        onTouchStart: handleTouchStart,\n                                        onTouchMove: handleTouchMove,\n                                        onTouchEnd: handleTouchEnd,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0px',\n                                                transition: isDragging ? 'none' : 'transform 2s ease-in-out',\n                                                transform: \"translateX(-\".concat(scrollLeft, \"px)\")\n                                            },\n                                            children: renderClientItems()\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                            lineNumber: 439,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 438,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                lineNumber: 400,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n            lineNumber: 391,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(HomeImageHero, \"+2TM6P9b2hp+5LFvgcsIf+koDe8=\");\n_c = HomeImageHero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeImageHero);\nvar _c;\n$RefreshReg$(_c, \"HomeImageHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/home/<USER>/HomeImageHero.tsx\n"));

/***/ })

});