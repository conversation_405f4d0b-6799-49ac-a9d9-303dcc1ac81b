"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/main/home/<USER>/HomeMainHero.tsx":
/*!********************************************************!*\
  !*** ./src/components/main/home/<USER>/HomeMainHero.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n// Style constants\nconst LOGO_SIZE = '60px'; // Increased from 40px to 60px\nconst DRAG_SENSITIVITY = 0.4;\nconst TEXT_WIDTH_MULTIPLIER = 10;\nconst MIN_TEXT_WIDTH = 60;\nconst TEXT_PADDING = 5; // Reduced from 20 to 5\nconst LOGO_GAP = 0; // No gap between logo and client name\nconst logoContainerStyle = {\n    width: LOGO_SIZE,\n    height: LOGO_SIZE,\n    borderRadius: '50%',\n    background: 'linear-gradient(var(--bg-color), var(--bg-color)) padding-box, linear-gradient(to left, var(--accent-color), var(--accent-secondary-color)) border-box',\n    border: '1px solid transparent',\n    padding: '2px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center'\n};\nconst logoStyle = {\n    width: 'calc(100% - 4px)',\n    height: 'calc(100% - 4px)',\n    borderRadius: '50%',\n    backgroundSize: 'cover',\n    backgroundRepeat: 'no-repeat',\n    backgroundPosition: 'center',\n    backgroundColor: 'transparent',\n    overflow: 'hidden'\n};\nconst clientItemStyle = (textLength)=>{\n    const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n    const totalWidth = parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n    return {\n        display: 'flex',\n        alignItems: 'center',\n        gap: \"\".concat(LOGO_GAP, \"px\"),\n        width: \"\".concat(totalWidth, \"px\"),\n        flexShrink: 0,\n        marginRight: '0px',\n        overflow: 'hidden'\n    };\n};\nconst companyNameStyle = {\n    color: 'white',\n    fontSize: '18px',\n    fontWeight: 'normal',\n    fontFamily: 'sans-serif',\n    whiteSpace: 'nowrap',\n    overflow: 'hidden',\n    textOverflow: 'ellipsis',\n    flex: 1\n};\nconst containerStyle = {\n    overflow: 'hidden',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '20px' // Consistent gap between all client blocks\n};\nconst trackStyle = (isDragging, scrollLeft)=>({\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0px',\n        transition: isDragging ? 'none' : 'transform 2s ease-in-out',\n        transform: \"translateX(-\".concat(scrollLeft, \"px)\")\n    });\n// Logo component\nconst ClientLogo = (param)=>{\n    let { logourl } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: logoContainerStyle,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                ...logoStyle,\n                backgroundImage: \"url(\".concat(logourl || \"/images/icon-testimonial-logo.svg\", \")\")\n            }\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n            lineNumber: 86,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n        lineNumber: 85,\n        columnNumber: 3\n    }, undefined);\n};\n_c = ClientLogo;\n// Client item component\nconst ClientItem = (param)=>{\n    let { client, index } = param;\n    const textLength = client.companyname.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: clientItemStyle(textLength),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {\n                logourl: client.logourl\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: companyNameStyle,\n                children: client.companyname\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, client.id || index, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = ClientItem;\n// Helper function to calculate client width\nconst calculateClientWidth = (textLength)=>{\n    const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n    return parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n};\n// Helper function to calculate total width\nconst calculateTotalWidth = (clients)=>{\n    return clients.reduce((sum, client)=>{\n        return sum + calculateClientWidth(client.companyname.length);\n    }, 0);\n};\n// Helper function to calculate scroll position for a specific client index\nconst calculateScrollPositionForClient = (clients, targetIndex)=>{\n    let scrollPosition = 0;\n    for(let i = 0; i < targetIndex && i < clients.length; i++){\n        scrollPosition += calculateClientWidth(clients[i].companyname.length);\n    }\n    return scrollPosition;\n};\nconst HomeMainHero = ()=>{\n    _s();\n    const [clients, setClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startX, setStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentClientIndex, setCurrentClientIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // Track which client is currently visible\n    const [scrollLeft, setScrollLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // Start at beginning\n    const [autoScrollInterval, setAutoScrollInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            const fetchClients = {\n                \"HomeMainHero.useEffect.fetchClients\": async ()=>{\n                    try {\n                        setLoading(true);\n                        // Fetch all active clients without limit, sorted by name\n                        const response = await fetch('/api/clients?limit=0');\n                        const data = await response.json();\n                        if (data.success) {\n                            // Sort clients by company name to ensure alphabetical order\n                            const sortedClients = (data.clients || []).sort({\n                                \"HomeMainHero.useEffect.fetchClients.sortedClients\": (a, b)=>a.companyname.localeCompare(b.companyname)\n                            }[\"HomeMainHero.useEffect.fetchClients.sortedClients\"]);\n                            setClients(sortedClients);\n                            // Set initial scroll position to hide first client\n                            if (sortedClients.length > 0) {\n                                const firstClientWidth = calculateClientWidth(sortedClients[0].companyname.length);\n                                setScrollLeft(firstClientWidth);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error fetching clients:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"HomeMainHero.useEffect.fetchClients\"];\n            fetchClients();\n        }\n    }[\"HomeMainHero.useEffect\"], []);\n    // Auto-scroll function - moves one client at a time\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            if (clients.length === 0 || isDragging) return;\n            let currentClientIndex = 0;\n            const interval = setInterval({\n                \"HomeMainHero.useEffect.interval\": ()=>{\n                    setScrollLeft({\n                        \"HomeMainHero.useEffect.interval\": (prevScrollLeft)=>{\n                            var _clients_currentClientIndex;\n                            const totalWidth = calculateTotalWidth(clients);\n                            // Calculate the width of the current client to move\n                            const currentClientWidth = calculateClientWidth(((_clients_currentClientIndex = clients[currentClientIndex]) === null || _clients_currentClientIndex === void 0 ? void 0 : _clients_currentClientIndex.companyname.length) || 0);\n                            let newScrollLeft = prevScrollLeft + currentClientWidth;\n                            // Move to next client\n                            currentClientIndex = (currentClientIndex + 1) % clients.length;\n                            // Reset to beginning when we've scrolled through all clients\n                            if (newScrollLeft >= totalWidth) {\n                                var _clients_;\n                                newScrollLeft = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n                                currentClientIndex = 1; // Start from second client next time\n                            }\n                            return newScrollLeft;\n                        }\n                    }[\"HomeMainHero.useEffect.interval\"]);\n                }\n            }[\"HomeMainHero.useEffect.interval\"], 5000);\n            setAutoScrollInterval(interval);\n            return ({\n                \"HomeMainHero.useEffect\": ()=>{\n                    if (interval) {\n                        clearInterval(interval);\n                    }\n                }\n            })[\"HomeMainHero.useEffect\"];\n        }\n    }[\"HomeMainHero.useEffect\"], [\n        clients.length,\n        isDragging\n    ]);\n    // Cleanup interval on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            return ({\n                \"HomeMainHero.useEffect\": ()=>{\n                    if (autoScrollInterval) {\n                        clearInterval(autoScrollInterval);\n                    }\n                }\n            })[\"HomeMainHero.useEffect\"];\n        }\n    }[\"HomeMainHero.useEffect\"], [\n        autoScrollInterval\n    ]);\n    const handleMouseDown = (e)=>{\n        setIsDragging(true);\n        setStartX(e.pageX);\n        // Stop auto-scroll when user starts dragging\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops dragging\n        if (clients.length > 0) {\n            let currentClientIndex = 0;\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    var _clients_currentClientIndex;\n                    const totalWidth = calculateTotalWidth(clients);\n                    // Calculate the width of the current client to move\n                    const currentClientWidth = calculateClientWidth(((_clients_currentClientIndex = clients[currentClientIndex]) === null || _clients_currentClientIndex === void 0 ? void 0 : _clients_currentClientIndex.companyname.length) || 0);\n                    let newScrollLeft = prevScrollLeft + currentClientWidth;\n                    // Move to next client\n                    currentClientIndex = (currentClientIndex + 1) % clients.length;\n                    // Reset to beginning when we've scrolled through all clients\n                    if (newScrollLeft >= totalWidth) {\n                        var _clients_;\n                        newScrollLeft = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n                        currentClientIndex = 1; // Start from second client next time\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const handleMouseLeave = ()=>{\n        setIsDragging(false);\n    };\n    const handleTouchStart = (e)=>{\n        setIsDragging(true);\n        setStartX(e.touches[0].pageX);\n        // Stop auto-scroll when user starts touching\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleTouchMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.touches[0].pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleTouchEnd = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops touching\n        if (clients.length > 0) {\n            let currentClientIndex = 0;\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    var _clients_currentClientIndex;\n                    const totalWidth = calculateTotalWidth(clients);\n                    // Calculate the width of the current client to move\n                    const currentClientWidth = calculateClientWidth(((_clients_currentClientIndex = clients[currentClientIndex]) === null || _clients_currentClientIndex === void 0 ? void 0 : _clients_currentClientIndex.companyname.length) || 0);\n                    let newScrollLeft = prevScrollLeft + currentClientWidth;\n                    // Move to next client\n                    currentClientIndex = (currentClientIndex + 1) % clients.length;\n                    // Reset to beginning when we've scrolled through all clients\n                    if (newScrollLeft >= totalWidth) {\n                        var _clients_;\n                        newScrollLeft = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n                        currentClientIndex = 1; // Start from second client next time\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const renderClientItems = ()=>{\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: clientItemStyle(7),\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            ...companyNameStyle,\n                            fontSize: '26px',\n                            fontWeight: 'bold'\n                        },\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 346,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (clients.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: clientItemStyle(12),\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            ...companyNameStyle,\n                            fontSize: '26px',\n                            fontWeight: 'bold'\n                        },\n                        children: \"No clients yet\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 357,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, client.id || index, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 11\n                    }, undefined)),\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, \"duplicate-\".concat(client.id || index), false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 11\n                    }, undefined))\n            ]\n        }, void 0, true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"hero\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid-lines\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-1\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-2\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-3\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 387,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-4\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 388,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-5\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 389,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                    lineNumber: 384,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row align-items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-content\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"section-title section-title-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"wow fadeInUp\",\n                                                    children: \"Enterprise-grade software & web development solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.2s\",\n                                                    \"data-cursor\": \"-opaque\",\n                                                    children: [\n                                                        \"Advanced digital solutions that\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"accelerate your growth\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.4s\",\n                                                    children: \"Transform your vision into scalable applications and sophisticated web platforms. Professional development services that deliver measurable results.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hero-btn wow fadeInUp\",\n                                            \"data-wow-delay\": \"0.6s\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/contact\",\n                                                    className: \"btn-default btn-highlighted\",\n                                                    children: \"Get Free Assessment\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/services\",\n                                                    className: \"btn-default\",\n                                                    children: \"View our services\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 393,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-company-slider\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"We're Trusted by more than \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"counter\",\n                                                    children: clients.length\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 46\n                                                }, undefined),\n                                                \"+ companies\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                ...containerStyle,\n                                                cursor: isDragging ? 'grabbing' : 'grab'\n                                            },\n                                            onMouseDown: handleMouseDown,\n                                            onMouseMove: handleMouseMove,\n                                            onMouseUp: handleMouseUp,\n                                            onMouseLeave: handleMouseLeave,\n                                            onTouchStart: handleTouchStart,\n                                            onTouchMove: handleTouchMove,\n                                            onTouchEnd: handleTouchEnd,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: trackStyle(isDragging, scrollLeft),\n                                                children: renderClientItems()\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                    lineNumber: 392,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n            lineNumber: 382,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(HomeMainHero, \"A4pwco2drNfheYBR/Lc5tdC5vc0=\");\n_c2 = HomeMainHero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeMainHero);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ClientLogo\");\n$RefreshReg$(_c1, \"ClientItem\");\n$RefreshReg$(_c2, \"HomeMainHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/home/<USER>/HomeMainHero.tsx\n"));

/***/ })

});