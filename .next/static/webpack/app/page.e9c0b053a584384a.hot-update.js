"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/main/home/<USER>/HomeSliderHero.tsx":
/*!**********************************************************!*\
  !*** ./src/components/main/home/<USER>/HomeSliderHero.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst HomeSliderHero = ()=>{\n    _s();\n    const [sliderImages, setSliderImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [clients, setClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [clientsLoading, setClientsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startX, setStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentClientIndex, setCurrentClientIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // Track which client is currently visible\n    const [scrollLeft, setScrollLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // Start at beginning\n    const [autoScrollInterval, setAutoScrollInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Style constants\n    const LOGO_SIZE = '60px'; // Increased from 40px to 60px\n    const DRAG_SENSITIVITY = 0.4;\n    const TEXT_WIDTH_MULTIPLIER = 10;\n    const MIN_TEXT_WIDTH = 60;\n    const TEXT_PADDING = 5; // Reduced from 20 to 5\n    const LOGO_GAP = 0; // No gap between logo and client name\n    // Helper function to calculate client width\n    const calculateClientWidth = (textLength)=>{\n        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n        return parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n    };\n    // Helper function to calculate total width\n    const calculateTotalWidth = (clients)=>{\n        return clients.reduce((sum, client)=>{\n            return sum + calculateClientWidth(client.companyname.length);\n        }, 0);\n    };\n    // Helper function to calculate scroll position for a specific client index\n    const calculateScrollPositionForClient = (clients, targetIndex)=>{\n        let scrollPosition = 0;\n        for(let i = 0; i < targetIndex && i < clients.length; i++){\n            scrollPosition += calculateClientWidth(clients[i].companyname.length);\n        }\n        return scrollPosition;\n    };\n    const logoContainerStyle = {\n        width: LOGO_SIZE,\n        height: LOGO_SIZE,\n        borderRadius: '50%',\n        background: 'linear-gradient(var(--bg-color), var(--bg-color)) padding-box, linear-gradient(to left, var(--accent-color), var(--accent-secondary-color)) border-box',\n        border: '1px solid transparent',\n        padding: '2px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n    };\n    const logoStyle = {\n        width: 'calc(100% - 4px)',\n        height: 'calc(100% - 4px)',\n        borderRadius: '50%',\n        backgroundSize: 'cover',\n        backgroundRepeat: 'no-repeat',\n        backgroundPosition: 'center',\n        backgroundColor: 'transparent',\n        overflow: 'hidden'\n    };\n    // Logo component\n    const ClientLogo = (param)=>{\n        let { logourl } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: logoContainerStyle,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    ...logoStyle,\n                    backgroundImage: \"url(\".concat(logourl || \"/images/icon-testimonial-logo.svg\", \")\")\n                }\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n            lineNumber: 75,\n            columnNumber: 5\n        }, undefined);\n    };\n    const clientItemStyle = (textLength)=>{\n        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n        const totalWidth = parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n        return {\n            display: 'flex',\n            alignItems: 'center',\n            gap: \"\".concat(LOGO_GAP, \"px\"),\n            width: \"\".concat(totalWidth, \"px\"),\n            flexShrink: 0,\n            marginRight: '0px',\n            overflow: 'hidden'\n        };\n    };\n    const companyNameStyle = {\n        color: 'white',\n        fontSize: '18px',\n        fontWeight: 'normal',\n        fontFamily: 'sans-serif',\n        whiteSpace: 'nowrap',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n        flex: 1\n    };\n    const containerStyle = {\n        overflow: 'hidden',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '20px' // Consistent gap between all client blocks\n    };\n    // Client item component\n    const ClientItem = (param)=>{\n        let { client, index } = param;\n        const textLength = client.companyname.length;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: clientItemStyle(textLength),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {\n                    logourl: client.logourl\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: companyNameStyle,\n                    children: client.companyname\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, client.id || index, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n            lineNumber: 120,\n            columnNumber: 7\n        }, undefined);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeSliderHero.useEffect\": ()=>{\n            const fetchSliderImages = {\n                \"HomeSliderHero.useEffect.fetchSliderImages\": async ()=>{\n                    try {\n                        const response = await fetch('/api/admin/settings/hero-media');\n                        if (response.ok) {\n                            var _data_media;\n                            const data = await response.json();\n                            if (data.success && ((_data_media = data.media) === null || _data_media === void 0 ? void 0 : _data_media.hero_slider_images)) {\n                                // Parse the slider images (comma-separated URLs)\n                                const images = data.media.hero_slider_images.split(',').map({\n                                    \"HomeSliderHero.useEffect.fetchSliderImages.images\": (url)=>url.trim()\n                                }[\"HomeSliderHero.useEffect.fetchSliderImages.images\"]).filter({\n                                    \"HomeSliderHero.useEffect.fetchSliderImages.images\": (url)=>url\n                                }[\"HomeSliderHero.useEffect.fetchSliderImages.images\"]);\n                                setSliderImages(images);\n                            } else {\n                                setSliderImages([\n                                    '/images/hero-bg.jpg',\n                                    '/images/hero-bg-2.jpg'\n                                ]);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error fetching slider images:', error);\n                        setSliderImages([\n                            '/images/hero-bg.jpg',\n                            '/images/hero-bg-2.jpg'\n                        ]);\n                    }\n                }\n            }[\"HomeSliderHero.useEffect.fetchSliderImages\"];\n            fetchSliderImages();\n        }\n    }[\"HomeSliderHero.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeSliderHero.useEffect\": ()=>{\n            const fetchClients = {\n                \"HomeSliderHero.useEffect.fetchClients\": async ()=>{\n                    try {\n                        setClientsLoading(true);\n                        const response = await fetch('/api/clients');\n                        const data = await response.json();\n                        if (data.success) {\n                            setClients(data.clients || []);\n                            // Set initial scroll position to hide first client\n                            if (data.clients && data.clients.length > 0) {\n                                const firstClientWidth = calculateClientWidth(data.clients[0].companyname.length);\n                                setScrollLeft(firstClientWidth);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error fetching clients:', error);\n                    } finally{\n                        setClientsLoading(false);\n                    }\n                }\n            }[\"HomeSliderHero.useEffect.fetchClients\"];\n            fetchClients();\n        }\n    }[\"HomeSliderHero.useEffect\"], []);\n    // Auto-scroll function\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeSliderHero.useEffect\": ()=>{\n            if (clients.length === 0 || isDragging) return;\n            const interval = setInterval({\n                \"HomeSliderHero.useEffect.interval\": ()=>{\n                    setScrollLeft({\n                        \"HomeSliderHero.useEffect.interval\": (prevScrollLeft)=>{\n                            const totalWidth = calculateTotalWidth(clients);\n                            const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                            let newScrollLeft = prevScrollLeft + firstClientWidth;\n                            if (newScrollLeft >= totalWidth) {\n                                newScrollLeft = firstClientWidth;\n                            }\n                            return newScrollLeft;\n                        }\n                    }[\"HomeSliderHero.useEffect.interval\"]);\n                }\n            }[\"HomeSliderHero.useEffect.interval\"], 5000);\n            setAutoScrollInterval(interval);\n            return ({\n                \"HomeSliderHero.useEffect\": ()=>{\n                    if (interval) {\n                        clearInterval(interval);\n                    }\n                }\n            })[\"HomeSliderHero.useEffect\"];\n        }\n    }[\"HomeSliderHero.useEffect\"], [\n        clients.length,\n        isDragging\n    ]);\n    // Cleanup interval on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeSliderHero.useEffect\": ()=>{\n            return ({\n                \"HomeSliderHero.useEffect\": ()=>{\n                    if (autoScrollInterval) {\n                        clearInterval(autoScrollInterval);\n                    }\n                }\n            })[\"HomeSliderHero.useEffect\"];\n        }\n    }[\"HomeSliderHero.useEffect\"], [\n        autoScrollInterval\n    ]);\n    const handleMouseDown = (e)=>{\n        setIsDragging(true);\n        setStartX(e.pageX);\n        // Stop auto-scroll when user starts dragging\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops dragging\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    const totalWidth = calculateTotalWidth(clients);\n                    const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                    let newScrollLeft = prevScrollLeft + firstClientWidth;\n                    if (newScrollLeft >= totalWidth) {\n                        newScrollLeft = firstClientWidth;\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const handleMouseLeave = ()=>{\n        setIsDragging(false);\n    };\n    const handleTouchStart = (e)=>{\n        setIsDragging(true);\n        setStartX(e.touches[0].pageX);\n        // Stop auto-scroll when user starts touching\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleTouchMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.touches[0].pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleTouchEnd = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops touching\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    const totalWidth = calculateTotalWidth(clients);\n                    const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                    let newScrollLeft = prevScrollLeft + firstClientWidth;\n                    if (newScrollLeft >= totalWidth) {\n                        newScrollLeft = firstClientWidth;\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const renderClientItems = ()=>{\n        if (clientsLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0px',\n                    width: '140px',\n                    flexShrink: 0,\n                    marginRight: '0px',\n                    overflow: 'hidden'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            fontSize: '26px',\n                            fontWeight: 'bold',\n                            fontFamily: 'sans-serif',\n                            whiteSpace: 'nowrap',\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            flex: 1\n                        },\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                lineNumber: 329,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (clients.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0px',\n                    width: '200px',\n                    flexShrink: 0,\n                    marginRight: '0px',\n                    overflow: 'hidden'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            fontSize: '26px',\n                            fontWeight: 'bold',\n                            fontFamily: 'sans-serif',\n                            whiteSpace: 'nowrap',\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            flex: 1\n                        },\n                        children: \"No clients yet\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                lineNumber: 357,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, client.id || index, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 11\n                    }, undefined)),\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, \"duplicate-\".concat(client.id || index), false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 11\n                    }, undefined))\n            ]\n        }, void 0, true);\n    };\n    // Default slides if no images are configured\n    const defaultSlides = [\n        '/images/hero-bg.jpg',\n        '/images/hero-bg-2.jpg'\n    ];\n    const slidesToShow = sliderImages.length > 0 ? sliderImages : defaultSlides;\n    // Handle dot navigation\n    const goToSlide = (index)=>{\n        setCurrentSlide(index);\n    };\n    // Auto-advance slides\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeSliderHero.useEffect\": ()=>{\n            if (slidesToShow.length > 1) {\n                const timer = setInterval({\n                    \"HomeSliderHero.useEffect.timer\": ()=>{\n                        setCurrentSlide({\n                            \"HomeSliderHero.useEffect.timer\": (prev)=>(prev + 1) % slidesToShow.length\n                        }[\"HomeSliderHero.useEffect.timer\"]);\n                    }\n                }[\"HomeSliderHero.useEffect.timer\"], 5000);\n                return ({\n                    \"HomeSliderHero.useEffect\": ()=>clearInterval(timer)\n                })[\"HomeSliderHero.useEffect\"];\n            }\n        }\n    }[\"HomeSliderHero.useEffect\"], [\n        slidesToShow.length\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"hero hero-bg-image hero-slider-layout\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"swiper hero-swiper\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"swiper-wrapper\",\n                        children: slidesToShow.map((imageUrl, index)=>{\n                            const isActive = index === currentSlide;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"swiper-slide\",\n                                style: {\n                                    display: isActive ? 'block' : 'none'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-slide\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hero-slider-image\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: imageUrl,\n                                                alt: \"Hero slide \".concat(index + 1)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"container\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"row align-items-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-lg-12\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"hero-content\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"section-title section-title-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"wow fadeInUp\",\n                                                                            children: \"Enterprise-grade software & web development solutions\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                            lineNumber: 448,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                            className: \"wow fadeInUp\",\n                                                                            \"data-wow-delay\": \"0.2s\",\n                                                                            \"data-cursor\": \"-opaque\",\n                                                                            children: [\n                                                                                \"Advanced digital solutions that\",\n                                                                                \" \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"accelerate your growth\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                                    lineNumber: 457,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                            lineNumber: 451,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"wow fadeInUp\",\n                                                                            \"data-wow-delay\": \"0.4s\",\n                                                                            children: \"Transform your vision into scalable applications and sophisticated web platforms. Professional development services that deliver measurable results.\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                            lineNumber: 459,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                    lineNumber: 447,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"hero-btn wow fadeInUp\",\n                                                                    \"data-wow-delay\": \"0.6s\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                            href: \"/main/contact\",\n                                                                            className: \"btn-default btn-highlighted\",\n                                                                            children: \"Get Free Assessment\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                            lineNumber: 470,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                            href: \"/main/services\",\n                                                                            className: \"btn-default\",\n                                                                            children: \"View our services\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                            lineNumber: 476,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                    lineNumber: 466,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"row\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-lg-12\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"hero-company-slider\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"We're Trusted by more than\",\n                                                                        \" \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"counter\",\n                                                                            children: clients.length\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                            lineNumber: 491,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        \"+ companies\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                    lineNumber: 489,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        overflow: 'hidden',\n                                                                        display: 'flex',\n                                                                        alignItems: 'center',\n                                                                        gap: '0px',\n                                                                        position: 'relative',\n                                                                        cursor: isDragging ? 'grabbing' : 'grab'\n                                                                    },\n                                                                    onMouseDown: handleMouseDown,\n                                                                    onMouseMove: handleMouseMove,\n                                                                    onMouseUp: handleMouseUp,\n                                                                    onMouseLeave: handleMouseLeave,\n                                                                    onTouchStart: handleTouchStart,\n                                                                    onTouchMove: handleTouchMove,\n                                                                    onTouchEnd: handleTouchEnd,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            display: 'flex',\n                                                                            alignItems: 'center',\n                                                                            gap: '0px',\n                                                                            transition: isDragging ? 'none' : 'transform 2s ease-in-out',\n                                                                            transform: \"translateX(-\".concat(scrollLeft, \"px)\")\n                                                                        },\n                                                                        children: renderClientItems()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                        lineNumber: 510,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                    lineNumber: 493,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, index, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 17\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                        lineNumber: 424,\n                        columnNumber: 11\n                    }, undefined),\n                    slidesToShow.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"swiper-pagination hero-pagination\",\n                        style: {\n                            position: 'absolute',\n                            bottom: '50px',\n                            left: '50%',\n                            transform: 'translateX(-50%)',\n                            zIndex: 10,\n                            display: 'flex',\n                            justifyContent: 'center',\n                            alignItems: 'center',\n                            gap: '12px'\n                        },\n                        children: slidesToShow.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"swiper-pagination-bullet \".concat(currentSlide === index ? 'swiper-pagination-bullet-active' : ''),\n                                onClick: ()=>goToSlide(index),\n                                \"aria-label\": \"Go to slide \".concat(index + 1),\n                                style: {\n                                    width: '14px',\n                                    height: '14px',\n                                    borderRadius: '50%',\n                                    border: 'none',\n                                    cursor: 'pointer',\n                                    transition: 'all 0.3s ease',\n                                    background: currentSlide === index ? '#ffffff' : 'rgba(255, 255, 255, 0.5)',\n                                    opacity: currentSlide === index ? 1 : 0.7,\n                                    transform: currentSlide === index ? 'scale(1.2)' : 'scale(1)'\n                                }\n                            }, index, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                lineNumber: 548,\n                                columnNumber: 17\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                        lineNumber: 533,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                lineNumber: 423,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n            lineNumber: 422,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(HomeSliderHero, \"yq4GrymekdYHoSzepb9itP+IO7o=\");\n_c = HomeSliderHero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeSliderHero);\nvar _c;\n$RefreshReg$(_c, \"HomeSliderHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/home/<USER>/HomeSliderHero.tsx\n"));

/***/ })

});