"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/main/home/<USER>/HomeMainHero.tsx":
/*!********************************************************!*\
  !*** ./src/components/main/home/<USER>/HomeMainHero.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n// Style constants\nconst LOGO_SIZE = '40px';\nconst DRAG_SENSITIVITY = 0.4;\nconst TEXT_WIDTH_MULTIPLIER = 10;\nconst MIN_TEXT_WIDTH = 60;\nconst TEXT_PADDING = 20;\nconst LOGO_GAP = 4;\nconst logoStyle = {\n    width: LOGO_SIZE,\n    height: LOGO_SIZE,\n    borderRadius: '50%',\n    backgroundSize: 'cover',\n    backgroundRepeat: 'no-repeat',\n    backgroundPosition: 'center',\n    backgroundColor: 'transparent',\n    overflow: 'hidden'\n};\nconst clientItemStyle = (textLength)=>{\n    const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n    const totalWidth = parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n    return {\n        display: 'flex',\n        alignItems: 'center',\n        gap: \"\".concat(LOGO_GAP, \"px\"),\n        width: \"\".concat(totalWidth, \"px\"),\n        flexShrink: 0,\n        marginRight: '0px',\n        overflow: 'hidden'\n    };\n};\nconst companyNameStyle = {\n    color: 'white',\n    fontSize: '18px',\n    fontWeight: 'normal',\n    fontFamily: 'sans-serif',\n    whiteSpace: 'nowrap',\n    overflow: 'hidden',\n    textOverflow: 'ellipsis',\n    flex: 1\n};\nconst containerStyle = {\n    overflow: 'hidden',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '0px' // No gap between blocks\n};\nconst trackStyle = (isDragging, scrollLeft)=>({\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0px',\n        transition: isDragging ? 'none' : 'transform 0.3s ease',\n        transform: \"translateX(-\".concat(scrollLeft, \"px)\")\n    });\n// Logo component\nconst ClientLogo = (param)=>{\n    let { logourl } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            ...logoStyle,\n            backgroundImage: \"url(\".concat(logourl || \"/images/icon-testimonial-logo.svg\", \")\")\n        }\n    }, void 0, false, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n        lineNumber: 73,\n        columnNumber: 3\n    }, undefined);\n};\n_c = ClientLogo;\n// Client item component\nconst ClientItem = (param)=>{\n    let { client, index } = param;\n    const textLength = client.companyname.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: clientItemStyle(textLength),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {\n                logourl: client.logourl\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: companyNameStyle,\n                children: client.companyname\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, client.id || index, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = ClientItem;\n// Helper function to calculate client width\nconst calculateClientWidth = (textLength)=>{\n    const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n    return parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n};\n// Helper function to calculate total width\nconst calculateTotalWidth = (clients)=>{\n    return clients.reduce((sum, client)=>{\n        return sum + calculateClientWidth(client.companyname.length);\n    }, 0);\n};\nconst HomeMainHero = ()=>{\n    _s();\n    const [clients, setClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startX, setStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [scrollLeft, setScrollLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(160); // Start with first client fully hidden\n    const [autoScrollInterval, setAutoScrollInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            const fetchClients = {\n                \"HomeMainHero.useEffect.fetchClients\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const response = await fetch('/api/clients');\n                        const data = await response.json();\n                        if (data.success) {\n                            setClients(data.clients || []);\n                            // Set initial scroll position to hide first client\n                            if (data.clients && data.clients.length > 0) {\n                                const firstClientWidth = calculateClientWidth(data.clients[0].companyname.length);\n                                setScrollLeft(firstClientWidth);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error fetching clients:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"HomeMainHero.useEffect.fetchClients\"];\n            fetchClients();\n        }\n    }[\"HomeMainHero.useEffect\"], []);\n    // Auto-scroll function\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            if (clients.length === 0 || isDragging) return;\n            const interval = setInterval({\n                \"HomeMainHero.useEffect.interval\": ()=>{\n                    setScrollLeft({\n                        \"HomeMainHero.useEffect.interval\": (prevScrollLeft)=>{\n                            const totalWidth = clients.reduce({\n                                \"HomeMainHero.useEffect.interval.totalWidth\": (sum, client)=>{\n                                    const textLength = client.companyname.length;\n                                    const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n                                    return sum + (parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING);\n                                }\n                            }[\"HomeMainHero.useEffect.interval.totalWidth\"], 0);\n                            const firstTextLength = clients[0].companyname.length;\n                            const firstTextWidth = Math.max(firstTextLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n                            const firstClientWidth = parseInt(LOGO_SIZE) + LOGO_GAP + firstTextWidth + TEXT_PADDING;\n                            let newScrollLeft = prevScrollLeft + firstClientWidth;\n                            if (newScrollLeft >= totalWidth) {\n                                newScrollLeft = firstClientWidth;\n                            }\n                            return newScrollLeft;\n                        }\n                    }[\"HomeMainHero.useEffect.interval\"]);\n                }\n            }[\"HomeMainHero.useEffect.interval\"], 5000);\n            setAutoScrollInterval(interval);\n            return ({\n                \"HomeMainHero.useEffect\": ()=>{\n                    if (interval) {\n                        clearInterval(interval);\n                    }\n                }\n            })[\"HomeMainHero.useEffect\"];\n        }\n    }[\"HomeMainHero.useEffect\"], [\n        clients.length,\n        isDragging\n    ]);\n    // Cleanup interval on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            return ({\n                \"HomeMainHero.useEffect\": ()=>{\n                    if (autoScrollInterval) {\n                        clearInterval(autoScrollInterval);\n                    }\n                }\n            })[\"HomeMainHero.useEffect\"];\n        }\n    }[\"HomeMainHero.useEffect\"], [\n        autoScrollInterval\n    ]);\n    const handleMouseDown = (e)=>{\n        setIsDragging(true);\n        setStartX(e.pageX);\n        // Stop auto-scroll when user starts dragging\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        // Calculate the total width dynamically\n        const totalWidth = clients.reduce((sum, client)=>{\n            const textLength = client.companyname.length;\n            const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n            return sum + (parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING);\n        }, 0);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstTextLength = ((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0;\n            const firstTextWidth = Math.max(firstTextLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n            const firstClientWidth = parseInt(LOGO_SIZE) + LOGO_GAP + firstTextWidth + TEXT_PADDING;\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops dragging\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    const totalWidth = clients.reduce((sum, client)=>{\n                        const textLength = client.companyname.length;\n                        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n                        return sum + (parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING);\n                    }, 0);\n                    const firstTextLength = clients[0].companyname.length;\n                    const firstTextWidth = Math.max(firstTextLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n                    const firstClientWidth = parseInt(LOGO_SIZE) + LOGO_GAP + firstTextWidth + TEXT_PADDING;\n                    let newScrollLeft = prevScrollLeft + firstClientWidth;\n                    if (newScrollLeft >= totalWidth) {\n                        newScrollLeft = firstClientWidth;\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const handleMouseLeave = ()=>{\n        setIsDragging(false);\n    };\n    const handleTouchStart = (e)=>{\n        setIsDragging(true);\n        setStartX(e.touches[0].pageX);\n        // Stop auto-scroll when user starts touching\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleTouchMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.touches[0].pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        // Calculate the total width dynamically\n        const totalWidth = clients.reduce((sum, client)=>{\n            const textLength = client.companyname.length;\n            const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n            return sum + (parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING);\n        }, 0);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstTextLength = ((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0;\n            const firstTextWidth = Math.max(firstTextLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n            const firstClientWidth = parseInt(LOGO_SIZE) + LOGO_GAP + firstTextWidth + TEXT_PADDING;\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleTouchEnd = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops touching\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    const totalWidth = clients.reduce((sum, client)=>{\n                        const textLength = client.companyname.length;\n                        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n                        return sum + (parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING);\n                    }, 0);\n                    const firstTextLength = clients[0].companyname.length;\n                    const firstTextWidth = Math.max(firstTextLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n                    const firstClientWidth = parseInt(LOGO_SIZE) + LOGO_GAP + firstTextWidth + TEXT_PADDING;\n                    let newScrollLeft = prevScrollLeft + firstClientWidth;\n                    if (newScrollLeft >= totalWidth) {\n                        newScrollLeft = firstClientWidth;\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const renderClientItems = ()=>{\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: clientItemStyle(7),\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            ...companyNameStyle,\n                            fontSize: '26px',\n                            fontWeight: 'bold'\n                        },\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 325,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (clients.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: clientItemStyle(12),\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            ...companyNameStyle,\n                            fontSize: '26px',\n                            fontWeight: 'bold'\n                        },\n                        children: \"No clients yet\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 336,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, client.id || index, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 11\n                    }, undefined)),\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, \"duplicate-\".concat(client.id || index), false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 11\n                    }, undefined))\n            ]\n        }, void 0, true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"hero\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid-lines\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-1\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-2\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-3\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 366,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-4\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-5\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row align-items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-content\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"section-title section-title-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"wow fadeInUp\",\n                                                    children: \"Enterprise-grade software & web development solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.2s\",\n                                                    \"data-cursor\": \"-opaque\",\n                                                    children: [\n                                                        \"Advanced digital solutions that\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"accelerate your growth\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.4s\",\n                                                    children: \"Transform your vision into scalable applications and sophisticated web platforms. Professional development services that deliver measurable results.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hero-btn wow fadeInUp\",\n                                            \"data-wow-delay\": \"0.6s\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/contact\",\n                                                    className: \"btn-default btn-highlighted\",\n                                                    children: \"Get Free Assessment\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/services\",\n                                                    className: \"btn-default\",\n                                                    children: \"View our services\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-company-slider\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"We're Trusted by more than \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"counter\",\n                                                    children: clients.length\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 46\n                                                }, undefined),\n                                                \"+ companies\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                ...containerStyle,\n                                                cursor: isDragging ? 'grabbing' : 'grab'\n                                            },\n                                            onMouseDown: handleMouseDown,\n                                            onMouseMove: handleMouseMove,\n                                            onMouseUp: handleMouseUp,\n                                            onMouseLeave: handleMouseLeave,\n                                            onTouchStart: handleTouchStart,\n                                            onTouchMove: handleTouchMove,\n                                            onTouchEnd: handleTouchEnd,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: trackStyle(isDragging, scrollLeft),\n                                                children: renderClientItems()\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 409,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                    lineNumber: 371,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n            lineNumber: 361,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(HomeMainHero, \"+cK3ts9BkIzmNmquLDoGqVEeSrY=\");\n_c2 = HomeMainHero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeMainHero);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ClientLogo\");\n$RefreshReg$(_c1, \"ClientItem\");\n$RefreshReg$(_c2, \"HomeMainHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/home/<USER>/HomeMainHero.tsx\n"));

/***/ })

});