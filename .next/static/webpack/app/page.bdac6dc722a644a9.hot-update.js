"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/main/home/<USER>/HomeVideoHero.tsx":
/*!*********************************************************!*\
  !*** ./src/components/main/home/<USER>/HomeVideoHero.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst HomeVideoHero = ()=>{\n    _s();\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [videoStatus, setVideoStatus] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('loading');\n    const [videoSrc, setVideoSrc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('/videos/Hero-video.mp4'); // Default fallback\n    const [posterImage, setPosterImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('/images/hero-bg.jpg'); // Default fallback\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [clients, setClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [clientsLoading, setClientsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startX, setStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentClientIndex, setCurrentClientIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // Track which client is currently visible\n    const [scrollLeft, setScrollLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // Start at beginning\n    const [autoScrollInterval, setAutoScrollInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Style constants\n    const LOGO_SIZE = '60px'; // Increased from 40px to 60px\n    const DRAG_SENSITIVITY = 0.4;\n    const TEXT_WIDTH_MULTIPLIER = 10;\n    const MIN_TEXT_WIDTH = 60;\n    const TEXT_PADDING = 5; // Reduced from 20 to 5\n    const LOGO_GAP = 0; // No gap between logo and client name\n    // Helper function to calculate client width\n    const calculateClientWidth = (textLength)=>{\n        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n        return parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n    };\n    // Helper function to calculate total width\n    const calculateTotalWidth = (clients)=>{\n        return clients.reduce((sum, client)=>{\n            return sum + calculateClientWidth(client.companyname.length);\n        }, 0);\n    };\n    // Helper function to calculate scroll position for a specific client index\n    const calculateScrollPositionForClient = (clients, targetIndex)=>{\n        let scrollPosition = 0;\n        for(let i = 0; i < targetIndex && i < clients.length; i++){\n            scrollPosition += calculateClientWidth(clients[i].companyname.length);\n        }\n        return scrollPosition;\n    };\n    const logoContainerStyle = {\n        width: LOGO_SIZE,\n        height: LOGO_SIZE,\n        borderRadius: '50%',\n        background: 'linear-gradient(var(--bg-color), var(--bg-color)) padding-box, linear-gradient(to left, var(--accent-color), var(--accent-secondary-color)) border-box',\n        border: '1px solid transparent',\n        padding: '2px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n    };\n    const logoStyle = {\n        width: 'calc(100% - 4px)',\n        height: 'calc(100% - 4px)',\n        borderRadius: '50%',\n        backgroundSize: 'cover',\n        backgroundRepeat: 'no-repeat',\n        backgroundPosition: 'center',\n        backgroundColor: 'transparent',\n        overflow: 'hidden'\n    };\n    // Logo component\n    const ClientLogo = (param)=>{\n        let { logourl } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: logoContainerStyle,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    ...logoStyle,\n                    backgroundImage: \"url(\".concat(logourl || \"/images/icon-testimonial-logo.svg\", \")\")\n                }\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n            lineNumber: 80,\n            columnNumber: 5\n        }, undefined);\n    };\n    // Client item component\n    const ClientItem = (param)=>{\n        let { client, index } = param;\n        const textLength = client.companyname.length;\n        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n        const totalWidth = parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: \"\".concat(LOGO_GAP, \"px\"),\n                width: \"\".concat(totalWidth, \"px\"),\n                flexShrink: 0,\n                marginRight: '0px',\n                overflow: 'hidden'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {\n                    logourl: client.logourl\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        color: 'white',\n                        fontSize: '18px',\n                        fontWeight: 'normal',\n                        fontFamily: 'sans-serif',\n                        whiteSpace: 'nowrap',\n                        overflow: 'hidden',\n                        textOverflow: 'ellipsis',\n                        flex: 1\n                    },\n                    children: client.companyname\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, client.id || index, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n            lineNumber: 95,\n            columnNumber: 7\n        }, undefined);\n    };\n    // Fetch hero video settings\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            const fetchHeroVideo = {\n                \"HomeVideoHero.useEffect.fetchHeroVideo\": async ()=>{\n                    try {\n                        const response = await fetch('/api/admin/settings/hero-media');\n                        if (response.ok) {\n                            const data = await response.json();\n                            if (data.success && data.media) {\n                                if (data.media.hero_video_file) {\n                                    setVideoSrc(data.media.hero_video_file);\n                                }\n                                if (data.media.hero_video_poster) {\n                                    setPosterImage(data.media.hero_video_poster);\n                                }\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error fetching hero video:', error);\n                    // Keep default video\n                    }\n                }\n            }[\"HomeVideoHero.useEffect.fetchHeroVideo\"];\n            fetchHeroVideo();\n        }\n    }[\"HomeVideoHero.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            const fetchClients = {\n                \"HomeVideoHero.useEffect.fetchClients\": async ()=>{\n                    try {\n                        setClientsLoading(true);\n                        const response = await fetch('/api/clients');\n                        const data = await response.json();\n                        if (data.success) {\n                            setClients(data.clients || []);\n                            // Set initial scroll position to hide first client\n                            if (data.clients && data.clients.length > 0) {\n                                const firstClientWidth = calculateClientWidth(data.clients[0].companyname.length);\n                                setScrollLeft(firstClientWidth);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error fetching clients:', error);\n                    } finally{\n                        setClientsLoading(false);\n                    }\n                }\n            }[\"HomeVideoHero.useEffect.fetchClients\"];\n            fetchClients();\n        }\n    }[\"HomeVideoHero.useEffect\"], []);\n    // Auto-scroll function\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            if (clients.length === 0 || isDragging) return;\n            const interval = setInterval({\n                \"HomeVideoHero.useEffect.interval\": ()=>{\n                    setScrollLeft({\n                        \"HomeVideoHero.useEffect.interval\": (prevScrollLeft)=>{\n                            const totalWidth = calculateTotalWidth(clients);\n                            const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                            let newScrollLeft = prevScrollLeft + firstClientWidth;\n                            if (newScrollLeft >= totalWidth) {\n                                newScrollLeft = firstClientWidth;\n                            }\n                            return newScrollLeft;\n                        }\n                    }[\"HomeVideoHero.useEffect.interval\"]);\n                }\n            }[\"HomeVideoHero.useEffect.interval\"], 5000);\n            setAutoScrollInterval(interval);\n            return ({\n                \"HomeVideoHero.useEffect\": ()=>{\n                    if (interval) {\n                        clearInterval(interval);\n                    }\n                }\n            })[\"HomeVideoHero.useEffect\"];\n        }\n    }[\"HomeVideoHero.useEffect\"], [\n        clients.length,\n        isDragging\n    ]);\n    // Cleanup interval on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            return ({\n                \"HomeVideoHero.useEffect\": ()=>{\n                    if (autoScrollInterval) {\n                        clearInterval(autoScrollInterval);\n                    }\n                }\n            })[\"HomeVideoHero.useEffect\"];\n        }\n    }[\"HomeVideoHero.useEffect\"], [\n        autoScrollInterval\n    ]);\n    const handleMouseDown = (e)=>{\n        setIsDragging(true);\n        setStartX(e.pageX);\n        // Stop auto-scroll when user starts dragging\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops dragging\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    const totalWidth = calculateTotalWidth(clients);\n                    const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                    let newScrollLeft = prevScrollLeft + firstClientWidth;\n                    if (newScrollLeft >= totalWidth) {\n                        newScrollLeft = firstClientWidth;\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const handleMouseLeave = ()=>{\n        setIsDragging(false);\n    };\n    const handleTouchStart = (e)=>{\n        setIsDragging(true);\n        setStartX(e.touches[0].pageX);\n        // Stop auto-scroll when user starts touching\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleTouchMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.touches[0].pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleTouchEnd = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops touching\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    const totalWidth = calculateTotalWidth(clients);\n                    const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                    let newScrollLeft = prevScrollLeft + firstClientWidth;\n                    if (newScrollLeft >= totalWidth) {\n                        newScrollLeft = firstClientWidth;\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const renderClientItems = ()=>{\n        if (clientsLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0px',\n                    width: '140px',\n                    flexShrink: 0,\n                    marginRight: '0px',\n                    overflow: 'hidden'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            fontSize: '26px',\n                            fontWeight: 'bold',\n                            fontFamily: 'sans-serif',\n                            whiteSpace: 'nowrap',\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            flex: 1\n                        },\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                lineNumber: 323,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (clients.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0px',\n                    width: '200px',\n                    flexShrink: 0,\n                    marginRight: '0px',\n                    overflow: 'hidden'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            fontSize: '26px',\n                            fontWeight: 'bold',\n                            fontFamily: 'sans-serif',\n                            whiteSpace: 'nowrap',\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            flex: 1\n                        },\n                        children: \"No clients yet\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                lineNumber: 351,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, client.id || index, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 11\n                    }, undefined)),\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, \"duplicate-\".concat(client.id || index), false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 384,\n                        columnNumber: 11\n                    }, undefined))\n            ]\n        }, void 0, true);\n    };\n    // Force video element to reload when source changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            const video = videoRef.current;\n            if (video && videoSrc !== '/videos/Hero-video.mp4') {\n                setIsPlaying(false); // Reset playing state\n                video.load(); // This forces the video to reload with the new source\n            }\n        }\n    }[\"HomeVideoHero.useEffect\"], [\n        videoSrc\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            const video = videoRef.current;\n            if (!video) return;\n            // Event handler functions\n            const handleLoadStart = {\n                \"HomeVideoHero.useEffect.handleLoadStart\": ()=>{\n                    setVideoStatus('loading');\n                    setIsPlaying(false);\n                }\n            }[\"HomeVideoHero.useEffect.handleLoadStart\"];\n            const handleLoadedData = {\n                \"HomeVideoHero.useEffect.handleLoadedData\": ()=>setVideoStatus('loaded')\n            }[\"HomeVideoHero.useEffect.handleLoadedData\"];\n            const handleCanPlay = {\n                \"HomeVideoHero.useEffect.handleCanPlay\": ()=>setVideoStatus('canplay')\n            }[\"HomeVideoHero.useEffect.handleCanPlay\"];\n            const handlePlaying = {\n                \"HomeVideoHero.useEffect.handlePlaying\": ()=>{\n                    setVideoStatus('playing');\n                    setIsPlaying(true);\n                }\n            }[\"HomeVideoHero.useEffect.handlePlaying\"];\n            const handleError = {\n                \"HomeVideoHero.useEffect.handleError\": (e)=>{\n                    console.error('Video error:', e);\n                    setVideoStatus('error');\n                    setIsPlaying(false);\n                }\n            }[\"HomeVideoHero.useEffect.handleError\"];\n            // Add event listeners\n            video.addEventListener('loadstart', handleLoadStart);\n            video.addEventListener('loadeddata', handleLoadedData);\n            video.addEventListener('canplay', handleCanPlay);\n            video.addEventListener('playing', handlePlaying);\n            video.addEventListener('error', handleError);\n            // Wait for video to be ready before playing\n            const playVideo = {\n                \"HomeVideoHero.useEffect.playVideo\": ()=>{\n                    if (isPlaying) return; // Prevent multiple play requests\n                    if (video.readyState >= 2) {\n                        setIsPlaying(true);\n                        video.play().catch({\n                            \"HomeVideoHero.useEffect.playVideo\": (error)=>{\n                                console.error('Video autoplay failed:', error);\n                                setIsPlaying(false);\n                            }\n                        }[\"HomeVideoHero.useEffect.playVideo\"]);\n                    } else {\n                        // Wait for video to load\n                        const handleCanPlayOnce = {\n                            \"HomeVideoHero.useEffect.playVideo.handleCanPlayOnce\": ()=>{\n                                if (isPlaying) return; // Prevent multiple play requests\n                                setIsPlaying(true);\n                                video.play().catch({\n                                    \"HomeVideoHero.useEffect.playVideo.handleCanPlayOnce\": (error)=>{\n                                        console.error('Video autoplay failed:', error);\n                                        setIsPlaying(false);\n                                    }\n                                }[\"HomeVideoHero.useEffect.playVideo.handleCanPlayOnce\"]);\n                            }\n                        }[\"HomeVideoHero.useEffect.playVideo.handleCanPlayOnce\"];\n                        video.addEventListener('canplay', handleCanPlayOnce, {\n                            once: true\n                        });\n                    }\n                }\n            }[\"HomeVideoHero.useEffect.playVideo\"];\n            // Small delay to prevent overlapping play requests\n            const playTimeout = setTimeout(playVideo, 100);\n            // Cleanup function\n            return ({\n                \"HomeVideoHero.useEffect\": ()=>{\n                    clearTimeout(playTimeout);\n                    video.removeEventListener('loadstart', handleLoadStart);\n                    video.removeEventListener('loadeddata', handleLoadedData);\n                    video.removeEventListener('canplay', handleCanPlay);\n                    video.removeEventListener('playing', handlePlaying);\n                    video.removeEventListener('error', handleError);\n                }\n            })[\"HomeVideoHero.useEffect\"];\n        }\n    }[\"HomeVideoHero.useEffect\"], [\n        videoSrc\n    ]); // Re-run when video source changes\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"hero hero-bg-image hero-video\",\n            style: {\n                position: 'relative',\n                overflow: 'hidden',\n                backgroundImage: 'url(/images/hero-bg.jpg)',\n                backgroundSize: 'cover',\n                backgroundPosition: 'center',\n                backgroundRepeat: 'no-repeat'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hero-bg-video\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                        ref: videoRef,\n                        autoPlay: true,\n                        muted: true,\n                        loop: true,\n                        playsInline: true,\n                        id: \"myvideo\",\n                        src: videoSrc,\n                        poster: posterImage,\n                        style: {\n                            position: 'absolute',\n                            top: 0,\n                            left: 0,\n                            width: '100%',\n                            height: '100%',\n                            objectFit: 'cover',\n                            zIndex: -1\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                src: videoSrc,\n                                type: \"video/mp4\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Your browser does not support the video tag.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 483,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                    lineNumber: 480,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row align-items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-content\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"section-title section-title-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"wow fadeInUp\",\n                                                    children: \"Enterprise-grade software & web development solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.2s\",\n                                                    \"data-cursor\": \"-opaque\",\n                                                    children: [\n                                                        \"Advanced digital solutions that\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"accelerate your growth\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.4s\",\n                                                    children: \"Transform your vision into scalable applications and sophisticated web platforms. Professional development services that deliver measurable results.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hero-btn wow fadeInUp\",\n                                            \"data-wow-delay\": \"0.6s\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/contact\",\n                                                    className: \"btn-default btn-highlighted\",\n                                                    children: \"Get Free Assessment\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/services\",\n                                                    className: \"btn-default\",\n                                                    children: \"View our services\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                            lineNumber: 544,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                            lineNumber: 520,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-company-slider\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"We're Trusted by more than \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"counter\",\n                                                    children: clients.length\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 46\n                                                }, undefined),\n                                                \"+ companies\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                overflow: 'hidden',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0px',\n                                                position: 'relative',\n                                                cursor: isDragging ? 'grabbing' : 'grab'\n                                            },\n                                            onMouseDown: handleMouseDown,\n                                            onMouseMove: handleMouseMove,\n                                            onMouseUp: handleMouseUp,\n                                            onMouseLeave: handleMouseLeave,\n                                            onTouchStart: handleTouchStart,\n                                            onTouchMove: handleTouchMove,\n                                            onTouchEnd: handleTouchEnd,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: '0px',\n                                                    transition: isDragging ? 'none' : 'transform 2s ease-in-out',\n                                                    transform: \"translateX(-\".concat(scrollLeft, \"px)\")\n                                                },\n                                                children: renderClientItems()\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                    lineNumber: 560,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                lineNumber: 558,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                            lineNumber: 557,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                    lineNumber: 519,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n            lineNumber: 468,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(HomeVideoHero, \"C+ltSsjCuyQUglIwCaY+tcnYus0=\");\n_c = HomeVideoHero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeVideoHero);\nvar _c;\n$RefreshReg$(_c, \"HomeVideoHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/home/<USER>/HomeVideoHero.tsx\n"));

/***/ })

});