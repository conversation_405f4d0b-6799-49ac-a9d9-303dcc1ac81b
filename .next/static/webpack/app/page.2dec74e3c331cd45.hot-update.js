"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/main/home/<USER>/HomeMainHero.tsx":
/*!********************************************************!*\
  !*** ./src/components/main/home/<USER>/HomeMainHero.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n// Style constants\nconst LOGO_SIZE = '40px';\nconst DRAG_SENSITIVITY = 0.4;\nconst logoStyle = {\n    width: LOGO_SIZE,\n    height: LOGO_SIZE,\n    borderRadius: '50%',\n    backgroundSize: 'cover',\n    backgroundRepeat: 'no-repeat',\n    backgroundPosition: 'center',\n    backgroundColor: 'transparent',\n    overflow: 'hidden'\n};\nconst clientItemStyle = (textLength)=>{\n    // Calculate width based on text length: logo (40px) + gap (8px) + text width + padding (20px)\n    const textWidth = Math.max(textLength * 10, 60); // Increased multiplier and minimum for better fit\n    const totalWidth = 40 + 8 + textWidth + 20; // logo + gap + text + padding\n    return {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '8px',\n        width: \"\".concat(totalWidth, \"px\"),\n        flexShrink: 0,\n        marginRight: '0px',\n        overflow: 'hidden' // Ensure no overflow\n    };\n};\nconst companyNameStyle = {\n    color: 'white',\n    fontSize: '18px',\n    fontWeight: 'normal',\n    fontFamily: 'sans-serif',\n    whiteSpace: 'nowrap',\n    overflow: 'hidden',\n    textOverflow: 'ellipsis',\n    flex: 1 // Take remaining space\n};\nconst containerStyle = {\n    overflow: 'hidden',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '0px' // No gap between blocks\n};\nconst trackStyle = (isDragging, scrollLeft)=>({\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0px',\n        transition: isDragging ? 'none' : 'transform 0.3s ease',\n        transform: \"translateX(-\".concat(scrollLeft, \"px)\")\n    });\n// Logo component\nconst ClientLogo = (param)=>{\n    let { logourl } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            ...logoStyle,\n            backgroundImage: \"url(\".concat(logourl || \"/images/icon-testimonial-logo.svg\", \")\")\n        }\n    }, void 0, false, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n        lineNumber: 70,\n        columnNumber: 3\n    }, undefined);\n};\n_c = ClientLogo;\n// Client item component\nconst ClientItem = (param)=>{\n    let { client, index } = param;\n    const textLength = client.companyname.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: clientItemStyle(textLength),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {\n                logourl: client.logourl\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: companyNameStyle,\n                children: client.companyname\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, client.id || index, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = ClientItem;\nconst HomeMainHero = ()=>{\n    _s();\n    const [clients, setClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startX, setStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [scrollLeft, setScrollLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(160); // Start with first client fully hidden\n    const [autoScrollInterval, setAutoScrollInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            const fetchClients = {\n                \"HomeMainHero.useEffect.fetchClients\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const response = await fetch('/api/clients');\n                        const data = await response.json();\n                        if (data.success) {\n                            setClients(data.clients || []);\n                            // Set initial scroll position to hide first client\n                            if (data.clients && data.clients.length > 0) {\n                                const firstTextLength = data.clients[0].companyname.length;\n                                const firstTextWidth = Math.max(firstTextLength * 10, 60); // Updated multiplier\n                                const firstClientWidth = 40 + 8 + firstTextWidth + 20;\n                                setScrollLeft(firstClientWidth);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error fetching clients:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"HomeMainHero.useEffect.fetchClients\"];\n            fetchClients();\n        }\n    }[\"HomeMainHero.useEffect\"], []);\n    // Auto-scroll function\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            if (clients.length === 0 || isDragging) return;\n            console.log('Setting up auto-scroll interval'); // Debug log\n            const interval = setInterval({\n                \"HomeMainHero.useEffect.interval\": ()=>{\n                    console.log('Auto-scroll triggered'); // Debug log\n                    setScrollLeft({\n                        \"HomeMainHero.useEffect.interval\": (prevScrollLeft)=>{\n                            // Calculate total width dynamically\n                            const totalWidth = clients.reduce({\n                                \"HomeMainHero.useEffect.interval.totalWidth\": (sum, client)=>{\n                                    const textLength = client.companyname.length;\n                                    const textWidth = Math.max(textLength * 10, 60);\n                                    return sum + (40 + 8 + textWidth + 20);\n                                }\n                            }[\"HomeMainHero.useEffect.interval.totalWidth\"], 0);\n                            // Simple approach: move by the width of the first client each time\n                            const firstTextLength = clients[0].companyname.length;\n                            const firstTextWidth = Math.max(firstTextLength * 10, 60);\n                            const firstClientWidth = 40 + 8 + firstTextWidth + 20;\n                            let newScrollLeft = prevScrollLeft + firstClientWidth;\n                            // Loop back to beginning when reaching the end\n                            if (newScrollLeft >= totalWidth) {\n                                newScrollLeft = firstClientWidth; // Start with first client fully hidden\n                            }\n                            console.log('Moving from', prevScrollLeft, 'to', newScrollLeft); // Debug log\n                            return newScrollLeft;\n                        }\n                    }[\"HomeMainHero.useEffect.interval\"]);\n                }\n            }[\"HomeMainHero.useEffect.interval\"], 5000); // Move every 5 seconds\n            setAutoScrollInterval(interval);\n            return ({\n                \"HomeMainHero.useEffect\": ()=>{\n                    if (interval) {\n                        clearInterval(interval);\n                    }\n                }\n            })[\"HomeMainHero.useEffect\"];\n        }\n    }[\"HomeMainHero.useEffect\"], [\n        clients.length,\n        isDragging\n    ]);\n    // Cleanup interval on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            return ({\n                \"HomeMainHero.useEffect\": ()=>{\n                    if (autoScrollInterval) {\n                        clearInterval(autoScrollInterval);\n                    }\n                }\n            })[\"HomeMainHero.useEffect\"];\n        }\n    }[\"HomeMainHero.useEffect\"], [\n        autoScrollInterval\n    ]);\n    const handleMouseDown = (e)=>{\n        setIsDragging(true);\n        setStartX(e.pageX);\n        // Stop auto-scroll when user starts dragging\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        // Calculate the total width dynamically\n        const totalWidth = clients.reduce((sum, client)=>{\n            const textLength = client.companyname.length;\n            const textWidth = Math.max(textLength * 8, 50);\n            return sum + (40 + 8 + textWidth + 20);\n        }, 0);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstTextLength = ((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0;\n            const firstTextWidth = Math.max(firstTextLength * 8, 50);\n            const firstClientWidth = 40 + 8 + firstTextWidth + 20;\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops dragging\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    // Calculate total width dynamically\n                    const totalWidth = clients.reduce((sum, client)=>{\n                        const textLength = client.companyname.length;\n                        const textWidth = Math.max(textLength * 10, 60);\n                        return sum + (40 + 8 + textWidth + 20);\n                    }, 0);\n                    // Simple approach: move by the width of the first client each time\n                    const firstTextLength = clients[0].companyname.length;\n                    const firstTextWidth = Math.max(firstTextLength * 10, 60);\n                    const firstClientWidth = 40 + 8 + firstTextWidth + 20;\n                    let newScrollLeft = prevScrollLeft + firstClientWidth;\n                    if (newScrollLeft >= totalWidth) {\n                        newScrollLeft = firstClientWidth;\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const handleMouseLeave = ()=>{\n        setIsDragging(false);\n    };\n    const handleTouchStart = (e)=>{\n        setIsDragging(true);\n        setStartX(e.touches[0].pageX);\n        // Stop auto-scroll when user starts touching\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleTouchMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.touches[0].pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        // Calculate the total width dynamically\n        const totalWidth = clients.reduce((sum, client)=>{\n            const textLength = client.companyname.length;\n            const textWidth = Math.max(textLength * 8, 50);\n            return sum + (40 + 8 + textWidth + 20);\n        }, 0);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstTextLength = ((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0;\n            const firstTextWidth = Math.max(firstTextLength * 8, 50);\n            const firstClientWidth = 40 + 8 + firstTextWidth + 20;\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleTouchEnd = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops touching\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                // Calculate total width dynamically\n                const totalWidth = clients.reduce((sum, client)=>{\n                    const textLength = client.companyname.length;\n                    const textWidth = Math.max(textLength * 10, 60); // Updated multiplier\n                    return sum + (40 + 8 + textWidth + 20);\n                }, 0);\n                setScrollLeft((prevScrollLeft)=>{\n                    // Find which client we're currently showing\n                    let currentClientIndex = 0;\n                    let accumulatedWidth = 0;\n                    for(let i = 0; i < clients.length; i++){\n                        const textLength = clients[i].companyname.length;\n                        const textWidth = Math.max(textLength * 10, 60); // Updated multiplier\n                        const clientWidth = 40 + 8 + textWidth + 20;\n                        if (accumulatedWidth + clientWidth > prevScrollLeft) {\n                            currentClientIndex = i;\n                            break;\n                        }\n                        accumulatedWidth += clientWidth;\n                    }\n                    // Move to next client\n                    const nextClientIndex = (currentClientIndex + 1) % clients.length;\n                    const nextTextLength = clients[nextClientIndex].companyname.length;\n                    const nextTextWidth = Math.max(nextTextLength * 10, 60); // Updated multiplier\n                    const nextClientWidth = 40 + 8 + nextTextWidth + 20;\n                    let newScrollLeft = accumulatedWidth + nextClientWidth;\n                    if (newScrollLeft >= totalWidth) {\n                        const firstTextLength = clients[0].companyname.length;\n                        const firstTextWidth = Math.max(firstTextLength * 10, 60); // Updated multiplier\n                        const firstClientWidth = 40 + 8 + firstTextWidth + 20;\n                        newScrollLeft = firstClientWidth;\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const renderClientItems = ()=>{\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: clientItemStyle(7),\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            ...companyNameStyle,\n                            fontSize: '26px',\n                            fontWeight: 'bold'\n                        },\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 342,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (clients.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: clientItemStyle(12),\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            ...companyNameStyle,\n                            fontSize: '26px',\n                            fontWeight: 'bold'\n                        },\n                        children: \"No clients yet\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 353,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, client.id || index, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 11\n                    }, undefined)),\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, \"duplicate-\".concat(client.id || index), false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 11\n                    }, undefined))\n            ]\n        }, void 0, true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"hero\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid-lines\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-1\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-2\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-3\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-4\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-5\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                    lineNumber: 380,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row align-items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-content\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"section-title section-title-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"wow fadeInUp\",\n                                                    children: \"Enterprise-grade software & web development solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.2s\",\n                                                    \"data-cursor\": \"-opaque\",\n                                                    children: [\n                                                        \"Advanced digital solutions that\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"accelerate your growth\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.4s\",\n                                                    children: \"Transform your vision into scalable applications and sophisticated web platforms. Professional development services that deliver measurable results.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hero-btn wow fadeInUp\",\n                                            \"data-wow-delay\": \"0.6s\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/contact\",\n                                                    className: \"btn-default btn-highlighted\",\n                                                    children: \"Get Free Assessment\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/services\",\n                                                    className: \"btn-default\",\n                                                    children: \"View our services\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 389,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-company-slider\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"We're Trusted by more than \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"counter\",\n                                                    children: clients.length\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 46\n                                                }, undefined),\n                                                \"+ companies\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                ...containerStyle,\n                                                cursor: isDragging ? 'grabbing' : 'grab'\n                                            },\n                                            onMouseDown: handleMouseDown,\n                                            onMouseMove: handleMouseMove,\n                                            onMouseUp: handleMouseUp,\n                                            onMouseLeave: handleMouseLeave,\n                                            onTouchStart: handleTouchStart,\n                                            onTouchMove: handleTouchMove,\n                                            onTouchEnd: handleTouchEnd,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: trackStyle(isDragging, scrollLeft),\n                                                children: renderClientItems()\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 426,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                    lineNumber: 388,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n            lineNumber: 378,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(HomeMainHero, \"+cK3ts9BkIzmNmquLDoGqVEeSrY=\");\n_c2 = HomeMainHero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeMainHero);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ClientLogo\");\n$RefreshReg$(_c1, \"ClientItem\");\n$RefreshReg$(_c2, \"HomeMainHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/home/<USER>/HomeMainHero.tsx\n"));

/***/ })

});