"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/main/home/<USER>/HomeMainHero.tsx":
/*!********************************************************!*\
  !*** ./src/components/main/home/<USER>/HomeMainHero.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n// Style constants\nconst LOGO_SIZE = '60px'; // Increased from 40px to 60px\nconst DRAG_SENSITIVITY = 0.4;\nconst TEXT_WIDTH_MULTIPLIER = 10;\nconst MIN_TEXT_WIDTH = 60;\nconst TEXT_PADDING = 5; // Reduced from 20 to 5\nconst LOGO_GAP = 0; // No gap between logo and client name\nconst logoContainerStyle = {\n    width: LOGO_SIZE,\n    height: LOGO_SIZE,\n    borderRadius: '50%',\n    background: 'linear-gradient(var(--bg-color), var(--bg-color)) padding-box, linear-gradient(to left, var(--accent-color), var(--accent-secondary-color)) border-box',\n    border: '1px solid transparent',\n    padding: '2px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center'\n};\nconst logoStyle = {\n    width: 'calc(100% - 4px)',\n    height: 'calc(100% - 4px)',\n    borderRadius: '50%',\n    backgroundSize: 'cover',\n    backgroundRepeat: 'no-repeat',\n    backgroundPosition: 'center',\n    backgroundColor: 'transparent',\n    overflow: 'hidden'\n};\nconst clientItemStyle = (textLength)=>{\n    const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n    const totalWidth = parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n    return {\n        display: 'flex',\n        alignItems: 'center',\n        gap: \"\".concat(LOGO_GAP, \"px\"),\n        width: \"\".concat(totalWidth, \"px\"),\n        flexShrink: 0,\n        marginRight: '0px',\n        overflow: 'hidden'\n    };\n};\nconst companyNameStyle = {\n    color: 'white',\n    fontSize: '18px',\n    fontWeight: 'normal',\n    fontFamily: 'sans-serif',\n    whiteSpace: 'nowrap',\n    overflow: 'hidden',\n    textOverflow: 'ellipsis',\n    flex: 1\n};\nconst containerStyle = {\n    overflow: 'hidden',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '20px' // Consistent gap between all client blocks\n};\nconst trackStyle = (isDragging, scrollLeft)=>({\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0px',\n        transition: isDragging ? 'none' : 'transform 2s ease-in-out',\n        transform: \"translateX(-\".concat(scrollLeft, \"px)\")\n    });\n// Logo component\nconst ClientLogo = (param)=>{\n    let { logourl } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: logoContainerStyle,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                ...logoStyle,\n                backgroundImage: \"url(\".concat(logourl || \"/images/icon-testimonial-logo.svg\", \")\")\n            }\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n            lineNumber: 86,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n        lineNumber: 85,\n        columnNumber: 3\n    }, undefined);\n};\n_c = ClientLogo;\n// Client item component\nconst ClientItem = (param)=>{\n    let { client, index } = param;\n    const textLength = client.companyname.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: clientItemStyle(textLength),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {\n                logourl: client.logourl\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: companyNameStyle,\n                children: client.companyname\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, client.id || index, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = ClientItem;\n// Helper function to calculate client width\nconst calculateClientWidth = (textLength)=>{\n    const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n    return parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n};\n// Helper function to calculate total width\nconst calculateTotalWidth = (clients)=>{\n    return clients.reduce((sum, client)=>{\n        return sum + calculateClientWidth(client.companyname.length);\n    }, 0);\n};\nconst HomeMainHero = ()=>{\n    _s();\n    const [clients, setClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startX, setStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [scrollLeft, setScrollLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(160); // Start with first client fully hidden\n    const [autoScrollInterval, setAutoScrollInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            const fetchClients = {\n                \"HomeMainHero.useEffect.fetchClients\": async ()=>{\n                    try {\n                        setLoading(true);\n                        // Fetch all active clients without limit, sorted by name\n                        const response = await fetch('/api/clients?limit=0');\n                        const data = await response.json();\n                        if (data.success) {\n                            // Sort clients by company name to ensure alphabetical order\n                            const sortedClients = (data.clients || []).sort({\n                                \"HomeMainHero.useEffect.fetchClients.sortedClients\": (a, b)=>a.companyname.localeCompare(b.companyname)\n                            }[\"HomeMainHero.useEffect.fetchClients.sortedClients\"]);\n                            setClients(sortedClients);\n                            // Set initial scroll position to hide first client\n                            if (sortedClients.length > 0) {\n                                const firstClientWidth = calculateClientWidth(sortedClients[0].companyname.length);\n                                setScrollLeft(firstClientWidth);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error fetching clients:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"HomeMainHero.useEffect.fetchClients\"];\n            fetchClients();\n        }\n    }[\"HomeMainHero.useEffect\"], []);\n    // Auto-scroll function - moves one client at a time\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            if (clients.length === 0 || isDragging) return;\n            let currentClientIndex = 0;\n            const interval = setInterval({\n                \"HomeMainHero.useEffect.interval\": ()=>{\n                    setScrollLeft({\n                        \"HomeMainHero.useEffect.interval\": (prevScrollLeft)=>{\n                            var _clients_currentClientIndex;\n                            const totalWidth = calculateTotalWidth(clients);\n                            // Calculate the width of the current client to move\n                            const currentClientWidth = calculateClientWidth(((_clients_currentClientIndex = clients[currentClientIndex]) === null || _clients_currentClientIndex === void 0 ? void 0 : _clients_currentClientIndex.companyname.length) || 0);\n                            let newScrollLeft = prevScrollLeft + currentClientWidth;\n                            // Move to next client\n                            currentClientIndex = (currentClientIndex + 1) % clients.length;\n                            // Reset to beginning when we've scrolled through all clients\n                            if (newScrollLeft >= totalWidth) {\n                                var _clients_;\n                                newScrollLeft = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n                                currentClientIndex = 1; // Start from second client next time\n                            }\n                            return newScrollLeft;\n                        }\n                    }[\"HomeMainHero.useEffect.interval\"]);\n                }\n            }[\"HomeMainHero.useEffect.interval\"], 5000);\n            setAutoScrollInterval(interval);\n            return ({\n                \"HomeMainHero.useEffect\": ()=>{\n                    if (interval) {\n                        clearInterval(interval);\n                    }\n                }\n            })[\"HomeMainHero.useEffect\"];\n        }\n    }[\"HomeMainHero.useEffect\"], [\n        clients.length,\n        isDragging\n    ]);\n    // Cleanup interval on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            return ({\n                \"HomeMainHero.useEffect\": ()=>{\n                    if (autoScrollInterval) {\n                        clearInterval(autoScrollInterval);\n                    }\n                }\n            })[\"HomeMainHero.useEffect\"];\n        }\n    }[\"HomeMainHero.useEffect\"], [\n        autoScrollInterval\n    ]);\n    const handleMouseDown = (e)=>{\n        setIsDragging(true);\n        setStartX(e.pageX);\n        // Stop auto-scroll when user starts dragging\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops dragging\n        if (clients.length > 0) {\n            let currentClientIndex = 0;\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    var _clients_currentClientIndex;\n                    const totalWidth = calculateTotalWidth(clients);\n                    // Calculate the width of the current client to move\n                    const currentClientWidth = calculateClientWidth(((_clients_currentClientIndex = clients[currentClientIndex]) === null || _clients_currentClientIndex === void 0 ? void 0 : _clients_currentClientIndex.companyname.length) || 0);\n                    let newScrollLeft = prevScrollLeft + currentClientWidth;\n                    // Move to next client\n                    currentClientIndex = (currentClientIndex + 1) % clients.length;\n                    // Reset to beginning when we've scrolled through all clients\n                    if (newScrollLeft >= totalWidth) {\n                        var _clients_;\n                        newScrollLeft = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n                        currentClientIndex = 1; // Start from second client next time\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const handleMouseLeave = ()=>{\n        setIsDragging(false);\n    };\n    const handleTouchStart = (e)=>{\n        setIsDragging(true);\n        setStartX(e.touches[0].pageX);\n        // Stop auto-scroll when user starts touching\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleTouchMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.touches[0].pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleTouchEnd = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops touching\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    const totalWidth = calculateTotalWidth(clients);\n                    const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                    let newScrollLeft = prevScrollLeft + firstClientWidth;\n                    if (newScrollLeft >= totalWidth) {\n                        newScrollLeft = firstClientWidth;\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const renderClientItems = ()=>{\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: clientItemStyle(7),\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            ...companyNameStyle,\n                            fontSize: '26px',\n                            fontWeight: 'bold'\n                        },\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 327,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (clients.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: clientItemStyle(12),\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            ...companyNameStyle,\n                            fontSize: '26px',\n                            fontWeight: 'bold'\n                        },\n                        children: \"No clients yet\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 338,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, client.id || index, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 11\n                    }, undefined)),\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, \"duplicate-\".concat(client.id || index), false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, undefined))\n            ]\n        }, void 0, true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"hero\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid-lines\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-1\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 366,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-2\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-3\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-4\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-5\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                    lineNumber: 365,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row align-items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-content\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"section-title section-title-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"wow fadeInUp\",\n                                                    children: \"Enterprise-grade software & web development solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.2s\",\n                                                    \"data-cursor\": \"-opaque\",\n                                                    children: [\n                                                        \"Advanced digital solutions that\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"accelerate your growth\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.4s\",\n                                                    children: \"Transform your vision into scalable applications and sophisticated web platforms. Professional development services that deliver measurable results.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hero-btn wow fadeInUp\",\n                                            \"data-wow-delay\": \"0.6s\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/contact\",\n                                                    className: \"btn-default btn-highlighted\",\n                                                    children: \"Get Free Assessment\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/services\",\n                                                    className: \"btn-default\",\n                                                    children: \"View our services\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 374,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-company-slider\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"We're Trusted by more than \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"counter\",\n                                                    children: clients.length\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 46\n                                                }, undefined),\n                                                \"+ companies\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                ...containerStyle,\n                                                cursor: isDragging ? 'grabbing' : 'grab'\n                                            },\n                                            onMouseDown: handleMouseDown,\n                                            onMouseMove: handleMouseMove,\n                                            onMouseUp: handleMouseUp,\n                                            onMouseLeave: handleMouseLeave,\n                                            onTouchStart: handleTouchStart,\n                                            onTouchMove: handleTouchMove,\n                                            onTouchEnd: handleTouchEnd,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: trackStyle(isDragging, scrollLeft),\n                                                children: renderClientItems()\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                    lineNumber: 373,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n            lineNumber: 363,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(HomeMainHero, \"+cK3ts9BkIzmNmquLDoGqVEeSrY=\");\n_c2 = HomeMainHero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeMainHero);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ClientLogo\");\n$RefreshReg$(_c1, \"ClientItem\");\n$RefreshReg$(_c2, \"HomeMainHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL21haW4vaG9tZS9oZXJvL0hvbWVNYWluSGVyby50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRW1EO0FBUW5ELGtCQUFrQjtBQUNsQixNQUFNRyxZQUFZLFFBQVEsOEJBQThCO0FBQ3hELE1BQU1DLG1CQUFtQjtBQUN6QixNQUFNQyx3QkFBd0I7QUFDOUIsTUFBTUMsaUJBQWlCO0FBQ3ZCLE1BQU1DLGVBQWUsR0FBRyx1QkFBdUI7QUFDL0MsTUFBTUMsV0FBVyxHQUFHLHNDQUFzQztBQUUxRCxNQUFNQyxxQkFBcUI7SUFDekJDLE9BQU9QO0lBQ1BRLFFBQVFSO0lBQ1JTLGNBQWM7SUFDZEMsWUFBWTtJQUNaQyxRQUFRO0lBQ1JDLFNBQVM7SUFDVEMsU0FBUztJQUNUQyxZQUFZO0lBQ1pDLGdCQUFnQjtBQUNsQjtBQUVBLE1BQU1DLFlBQVk7SUFDaEJULE9BQU87SUFDUEMsUUFBUTtJQUNSQyxjQUFjO0lBQ2RRLGdCQUFnQjtJQUNoQkMsa0JBQWtCO0lBQ2xCQyxvQkFBb0I7SUFDcEJDLGlCQUFpQjtJQUNqQkMsVUFBVTtBQUNaO0FBRUEsTUFBTUMsa0JBQWtCLENBQUNDO0lBQ3ZCLE1BQU1DLFlBQVlDLEtBQUtDLEdBQUcsQ0FBQ0gsYUFBYXJCLHVCQUF1QkM7SUFDL0QsTUFBTXdCLGFBQWFDLFNBQVM1QixhQUFhSyxXQUFXbUIsWUFBWXBCO0lBRWhFLE9BQU87UUFDTFMsU0FBUztRQUNUQyxZQUFZO1FBQ1plLEtBQUssR0FBWSxPQUFUeEIsVUFBUztRQUNqQkUsT0FBTyxHQUFjLE9BQVhvQixZQUFXO1FBQ3JCRyxZQUFZO1FBQ1pDLGFBQWE7UUFDYlYsVUFBVTtJQUNaO0FBQ0Y7QUFFQSxNQUFNVyxtQkFBbUI7SUFDdkJDLE9BQU87SUFDUEMsVUFBVTtJQUNWQyxZQUFZO0lBQ1pDLFlBQVk7SUFDWkMsWUFBWTtJQUNaaEIsVUFBVTtJQUNWaUIsY0FBYztJQUNkQyxNQUFNO0FBQ1I7QUFFQSxNQUFNQyxpQkFBaUI7SUFDckJuQixVQUFVO0lBQ1ZSLFNBQVM7SUFDVEMsWUFBWTtJQUNaZSxLQUFLLE9BQU8sMkNBQTJDO0FBQ3pEO0FBRUEsTUFBTVksYUFBYSxDQUFDQyxZQUFxQkMsYUFBd0I7UUFDL0Q5QixTQUFTO1FBQ1RDLFlBQVk7UUFDWmUsS0FBSztRQUNMZSxZQUFZRixhQUFhLFNBQVM7UUFDbENHLFdBQVcsZUFBMEIsT0FBWEYsWUFBVztJQUN2QztBQUVBLGlCQUFpQjtBQUNqQixNQUFNRyxhQUE2QztRQUFDLEVBQUVDLE9BQU8sRUFBRTt5QkFDN0QsOERBQUNDO1FBQUlDLE9BQU8zQztrQkFDViw0RUFBQzBDO1lBQUlDLE9BQU87Z0JBQ1YsR0FBR2pDLFNBQVM7Z0JBQ1prQyxpQkFBaUIsT0FBc0QsT0FBL0NILFdBQVcscUNBQW9DO1lBQ3pFOzs7Ozs7Ozs7Ozs7S0FMRUQ7QUFTTix3QkFBd0I7QUFDeEIsTUFBTUssYUFBMEQ7UUFBQyxFQUFFQyxNQUFNLEVBQUVDLEtBQUssRUFBRTtJQUNoRixNQUFNOUIsYUFBYTZCLE9BQU9FLFdBQVcsQ0FBQ0MsTUFBTTtJQUM1QyxxQkFDRSw4REFBQ1A7UUFBNkJDLE9BQU8zQixnQkFBZ0JDOzswQkFDbkQsOERBQUN1QjtnQkFBV0MsU0FBU0ssT0FBT0wsT0FBTzs7Ozs7OzBCQUNuQyw4REFBQ0M7Z0JBQUlDLE9BQU9qQjswQkFDVG9CLE9BQU9FLFdBQVc7Ozs7Ozs7T0FIYkYsT0FBT0ksRUFBRSxJQUFJSDs7Ozs7QUFPM0I7TUFWTUY7QUFZTiw0Q0FBNEM7QUFDNUMsTUFBTU0sdUJBQXVCLENBQUNsQztJQUM1QixNQUFNQyxZQUFZQyxLQUFLQyxHQUFHLENBQUNILGFBQWFyQix1QkFBdUJDO0lBQy9ELE9BQU95QixTQUFTNUIsYUFBYUssV0FBV21CLFlBQVlwQjtBQUN0RDtBQUVBLDJDQUEyQztBQUMzQyxNQUFNc0Qsc0JBQXNCLENBQUNDO0lBQzNCLE9BQU9BLFFBQVFDLE1BQU0sQ0FBQyxDQUFDQyxLQUFLVDtRQUMxQixPQUFPUyxNQUFNSixxQkFBcUJMLE9BQU9FLFdBQVcsQ0FBQ0MsTUFBTTtJQUM3RCxHQUFHO0FBQ0w7QUFFQSxNQUFNTyxlQUF5Qjs7SUFDN0IsTUFBTSxDQUFDSCxTQUFTSSxXQUFXLEdBQUdqRSwrQ0FBUUEsQ0FBVyxFQUFFO0lBQ25ELE1BQU0sQ0FBQ2tFLFNBQVNDLFdBQVcsR0FBR25FLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQzRDLFlBQVl3QixjQUFjLEdBQUdwRSwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUNxRSxRQUFRQyxVQUFVLEdBQUd0RSwrQ0FBUUEsQ0FBQztJQUNyQyxNQUFNLENBQUM2QyxZQUFZMEIsY0FBYyxHQUFHdkUsK0NBQVFBLENBQUMsTUFBTSx1Q0FBdUM7SUFDMUYsTUFBTSxDQUFDd0Usb0JBQW9CQyxzQkFBc0IsR0FBR3pFLCtDQUFRQSxDQUF3QjtJQUVwRkMsZ0RBQVNBO2tDQUFDO1lBQ1IsTUFBTXlFO3VEQUFlO29CQUNuQixJQUFJO3dCQUNGUCxXQUFXO3dCQUNYLHlEQUF5RDt3QkFDekQsTUFBTVEsV0FBVyxNQUFNQyxNQUFNO3dCQUM3QixNQUFNQyxPQUFPLE1BQU1GLFNBQVNHLElBQUk7d0JBQ2hDLElBQUlELEtBQUtFLE9BQU8sRUFBRTs0QkFDaEIsNERBQTREOzRCQUM1RCxNQUFNQyxnQkFBZ0IsQ0FBQ0gsS0FBS2hCLE9BQU8sSUFBSSxFQUFFLEVBQUVvQixJQUFJO3FGQUFDLENBQUNDLEdBQVdDLElBQzFERCxFQUFFMUIsV0FBVyxDQUFDNEIsYUFBYSxDQUFDRCxFQUFFM0IsV0FBVzs7NEJBRTNDUyxXQUFXZTs0QkFDWCxtREFBbUQ7NEJBQ25ELElBQUlBLGNBQWN2QixNQUFNLEdBQUcsR0FBRztnQ0FDNUIsTUFBTTRCLG1CQUFtQjFCLHFCQUFxQnFCLGFBQWEsQ0FBQyxFQUFFLENBQUN4QixXQUFXLENBQUNDLE1BQU07Z0NBQ2pGYyxjQUFjYzs0QkFDaEI7d0JBQ0Y7b0JBQ0YsRUFBRSxPQUFPQyxPQUFPO3dCQUNkQyxRQUFRRCxLQUFLLENBQUMsMkJBQTJCQTtvQkFDM0MsU0FBVTt3QkFDUm5CLFdBQVc7b0JBQ2I7Z0JBQ0Y7O1lBQ0FPO1FBQ0Y7aUNBQUcsRUFBRTtJQUVMLG9EQUFvRDtJQUNwRHpFLGdEQUFTQTtrQ0FBQztZQUNSLElBQUk0RCxRQUFRSixNQUFNLEtBQUssS0FBS2IsWUFBWTtZQUV4QyxJQUFJNEMscUJBQXFCO1lBRXpCLE1BQU1DLFdBQVdDO21EQUFZO29CQUMzQm5COzJEQUFjb0IsQ0FBQUE7Z0NBSW9DOUI7NEJBSGhELE1BQU1oQyxhQUFhK0Isb0JBQW9CQzs0QkFFdkMsb0RBQW9EOzRCQUNwRCxNQUFNK0IscUJBQXFCakMscUJBQXFCRSxFQUFBQSw4QkFBQUEsT0FBTyxDQUFDMkIsbUJBQW1CLGNBQTNCM0Isa0RBQUFBLDRCQUE2QkwsV0FBVyxDQUFDQyxNQUFNLEtBQUk7NEJBRW5HLElBQUlvQyxnQkFBZ0JGLGlCQUFpQkM7NEJBRXJDLHNCQUFzQjs0QkFDdEJKLHFCQUFxQixDQUFDQSxxQkFBcUIsS0FBSzNCLFFBQVFKLE1BQU07NEJBRTlELDZEQUE2RDs0QkFDN0QsSUFBSW9DLGlCQUFpQmhFLFlBQVk7b0NBQ01nQztnQ0FBckNnQyxnQkFBZ0JsQyxxQkFBcUJFLEVBQUFBLFlBQUFBLE9BQU8sQ0FBQyxFQUFFLGNBQVZBLGdDQUFBQSxVQUFZTCxXQUFXLENBQUNDLE1BQU0sS0FBSTtnQ0FDdkUrQixxQkFBcUIsR0FBRyxxQ0FBcUM7NEJBQy9EOzRCQUVBLE9BQU9LO3dCQUNUOztnQkFDRjtrREFBRztZQUVIcEIsc0JBQXNCZ0I7WUFFdEI7MENBQU87b0JBQ0wsSUFBSUEsVUFBVTt3QkFDWkssY0FBY0w7b0JBQ2hCO2dCQUNGOztRQUNGO2lDQUFHO1FBQUM1QixRQUFRSixNQUFNO1FBQUViO0tBQVc7SUFFL0IsOEJBQThCO0lBQzlCM0MsZ0RBQVNBO2tDQUFDO1lBQ1I7MENBQU87b0JBQ0wsSUFBSXVFLG9CQUFvQjt3QkFDdEJzQixjQUFjdEI7b0JBQ2hCO2dCQUNGOztRQUNGO2lDQUFHO1FBQUNBO0tBQW1CO0lBRXZCLE1BQU11QixrQkFBa0IsQ0FBQ0M7UUFDdkI1QixjQUFjO1FBQ2RFLFVBQVUwQixFQUFFQyxLQUFLO1FBQ2pCLDZDQUE2QztRQUM3QyxJQUFJekIsb0JBQW9CO1lBQ3RCc0IsY0FBY3RCO1lBQ2RDLHNCQUFzQjtRQUN4QjtJQUNGO0lBRUEsTUFBTXlCLGtCQUFrQixDQUFDRjtRQUN2QixJQUFJLENBQUNwRCxZQUFZO1FBQ2pCb0QsRUFBRUcsY0FBYztRQUNoQixNQUFNQyxJQUFJSixFQUFFQyxLQUFLO1FBQ2pCLE1BQU1JLE9BQU8sQ0FBQ0QsSUFBSS9CLE1BQUssSUFBS2xFO1FBQzVCLElBQUkwRixnQkFBZ0JoRCxhQUFhd0Q7UUFFakMsTUFBTXhFLGFBQWErQixvQkFBb0JDO1FBRXZDLCtDQUErQztRQUMvQyxJQUFJZ0MsaUJBQWlCaEUsWUFBWTtnQkFDZWdDO1lBQTlDLE1BQU13QixtQkFBbUIxQixxQkFBcUJFLEVBQUFBLFlBQUFBLE9BQU8sQ0FBQyxFQUFFLGNBQVZBLGdDQUFBQSxVQUFZTCxXQUFXLENBQUNDLE1BQU0sS0FBSTtZQUNoRm9DLGdCQUFnQkEsZ0JBQWdCaEUsYUFBYXdEO1FBQy9DLE9BRUssSUFBSVEsZ0JBQWdCLEdBQUc7WUFDMUJBLGdCQUFnQmhFLGFBQWFnRTtRQUMvQjtRQUVBdEIsY0FBY3NCO0lBQ2hCO0lBRUEsTUFBTVMsZ0JBQWdCO1FBQ3BCbEMsY0FBYztRQUNkLGdEQUFnRDtRQUNoRCxJQUFJUCxRQUFRSixNQUFNLEdBQUcsR0FBRztZQUN0QixJQUFJK0IscUJBQXFCO1lBRXpCLE1BQU1DLFdBQVdDLFlBQVk7Z0JBQzNCbkIsY0FBY29CLENBQUFBO3dCQUlvQzlCO29CQUhoRCxNQUFNaEMsYUFBYStCLG9CQUFvQkM7b0JBRXZDLG9EQUFvRDtvQkFDcEQsTUFBTStCLHFCQUFxQmpDLHFCQUFxQkUsRUFBQUEsOEJBQUFBLE9BQU8sQ0FBQzJCLG1CQUFtQixjQUEzQjNCLGtEQUFBQSw0QkFBNkJMLFdBQVcsQ0FBQ0MsTUFBTSxLQUFJO29CQUVuRyxJQUFJb0MsZ0JBQWdCRixpQkFBaUJDO29CQUVyQyxzQkFBc0I7b0JBQ3RCSixxQkFBcUIsQ0FBQ0EscUJBQXFCLEtBQUszQixRQUFRSixNQUFNO29CQUU5RCw2REFBNkQ7b0JBQzdELElBQUlvQyxpQkFBaUJoRSxZQUFZOzRCQUNNZ0M7d0JBQXJDZ0MsZ0JBQWdCbEMscUJBQXFCRSxFQUFBQSxZQUFBQSxPQUFPLENBQUMsRUFBRSxjQUFWQSxnQ0FBQUEsVUFBWUwsV0FBVyxDQUFDQyxNQUFNLEtBQUk7d0JBQ3ZFK0IscUJBQXFCLEdBQUcscUNBQXFDO29CQUMvRDtvQkFFQSxPQUFPSztnQkFDVDtZQUNGLEdBQUc7WUFFSHBCLHNCQUFzQmdCO1FBQ3hCO0lBQ0Y7SUFFQSxNQUFNYyxtQkFBbUI7UUFDdkJuQyxjQUFjO0lBQ2hCO0lBRUEsTUFBTW9DLG1CQUFtQixDQUFDUjtRQUN4QjVCLGNBQWM7UUFDZEUsVUFBVTBCLEVBQUVTLE9BQU8sQ0FBQyxFQUFFLENBQUNSLEtBQUs7UUFDNUIsNkNBQTZDO1FBQzdDLElBQUl6QixvQkFBb0I7WUFDdEJzQixjQUFjdEI7WUFDZEMsc0JBQXNCO1FBQ3hCO0lBQ0Y7SUFFQSxNQUFNaUMsa0JBQWtCLENBQUNWO1FBQ3ZCLElBQUksQ0FBQ3BELFlBQVk7UUFDakJvRCxFQUFFRyxjQUFjO1FBQ2hCLE1BQU1DLElBQUlKLEVBQUVTLE9BQU8sQ0FBQyxFQUFFLENBQUNSLEtBQUs7UUFDNUIsTUFBTUksT0FBTyxDQUFDRCxJQUFJL0IsTUFBSyxJQUFLbEU7UUFDNUIsSUFBSTBGLGdCQUFnQmhELGFBQWF3RDtRQUVqQyxNQUFNeEUsYUFBYStCLG9CQUFvQkM7UUFFdkMsK0NBQStDO1FBQy9DLElBQUlnQyxpQkFBaUJoRSxZQUFZO2dCQUNlZ0M7WUFBOUMsTUFBTXdCLG1CQUFtQjFCLHFCQUFxQkUsRUFBQUEsWUFBQUEsT0FBTyxDQUFDLEVBQUUsY0FBVkEsZ0NBQUFBLFVBQVlMLFdBQVcsQ0FBQ0MsTUFBTSxLQUFJO1lBQ2hGb0MsZ0JBQWdCQSxnQkFBZ0JoRSxhQUFhd0Q7UUFDL0MsT0FFSyxJQUFJUSxnQkFBZ0IsR0FBRztZQUMxQkEsZ0JBQWdCaEUsYUFBYWdFO1FBQy9CO1FBRUF0QixjQUFjc0I7SUFDaEI7SUFFQSxNQUFNYyxpQkFBaUI7UUFDckJ2QyxjQUFjO1FBQ2QsZ0RBQWdEO1FBQ2hELElBQUlQLFFBQVFKLE1BQU0sR0FBRyxHQUFHO1lBQ3RCLE1BQU1nQyxXQUFXQyxZQUFZO2dCQUMzQm5CLGNBQWNvQixDQUFBQTtvQkFDWixNQUFNOUQsYUFBYStCLG9CQUFvQkM7b0JBQ3ZDLE1BQU13QixtQkFBbUIxQixxQkFBcUJFLE9BQU8sQ0FBQyxFQUFFLENBQUNMLFdBQVcsQ0FBQ0MsTUFBTTtvQkFFM0UsSUFBSW9DLGdCQUFnQkYsaUJBQWlCTjtvQkFFckMsSUFBSVEsaUJBQWlCaEUsWUFBWTt3QkFDL0JnRSxnQkFBZ0JSO29CQUNsQjtvQkFFQSxPQUFPUTtnQkFDVDtZQUNGLEdBQUc7WUFFSHBCLHNCQUFzQmdCO1FBQ3hCO0lBQ0Y7SUFFQSxNQUFNbUIsb0JBQW9CO1FBQ3hCLElBQUkxQyxTQUFTO1lBQ1gscUJBQ0UsOERBQUNoQjtnQkFBSUMsT0FBTzNCLGdCQUFnQjs7b0JBQUk7a0NBQzlCLDhEQUFDd0I7Ozs7O2tDQUNELDhEQUFDRTt3QkFBSUMsT0FBTzs0QkFBRSxHQUFHakIsZ0JBQWdCOzRCQUFFRSxVQUFVOzRCQUFRQyxZQUFZO3dCQUFPO2tDQUFHOzs7Ozs7Ozs7Ozs7UUFLakY7UUFFQSxJQUFJd0IsUUFBUUosTUFBTSxLQUFLLEdBQUc7WUFDeEIscUJBQ0UsOERBQUNQO2dCQUFJQyxPQUFPM0IsZ0JBQWdCOztvQkFBSztrQ0FDL0IsOERBQUN3Qjs7Ozs7a0NBQ0QsOERBQUNFO3dCQUFJQyxPQUFPOzRCQUFFLEdBQUdqQixnQkFBZ0I7NEJBQUVFLFVBQVU7NEJBQVFDLFlBQVk7d0JBQU87a0NBQUc7Ozs7Ozs7Ozs7OztRQUtqRjtRQUVBLHFCQUNFOztnQkFDR3dCLFFBQVFnRCxHQUFHLENBQUMsQ0FBQ3ZELFFBQVFDLHNCQUNwQiw4REFBQ0Y7d0JBQW9DQyxRQUFRQTt3QkFBUUMsT0FBT0E7dUJBQTNDRCxPQUFPSSxFQUFFLElBQUlIOzs7OztnQkFHL0JNLFFBQVFnRCxHQUFHLENBQUMsQ0FBQ3ZELFFBQVFDLHNCQUNwQiw4REFBQ0Y7d0JBQW1EQyxRQUFRQTt3QkFBUUMsT0FBT0E7dUJBQTFELGFBQWdDLE9BQW5CRCxPQUFPSSxFQUFFLElBQUlIOzs7Ozs7O0lBSW5EO0lBRUEscUJBQ0U7a0JBRUUsNEVBQUNMO1lBQUk0RCxXQUFVOzs4QkFFYiw4REFBQzVEO29CQUFJNEQsV0FBVTs7c0NBQ2IsOERBQUM1RDs0QkFBSTRELFdBQVU7Ozs7OztzQ0FDZiw4REFBQzVEOzRCQUFJNEQsV0FBVTs7Ozs7O3NDQUNmLDhEQUFDNUQ7NEJBQUk0RCxXQUFVOzs7Ozs7c0NBQ2YsOERBQUM1RDs0QkFBSTRELFdBQVU7Ozs7OztzQ0FDZiw4REFBQzVEOzRCQUFJNEQsV0FBVTs7Ozs7Ozs7Ozs7OzhCQUdqQiw4REFBQzVEO29CQUFJNEQsV0FBVTs7c0NBQ2IsOERBQUM1RDs0QkFBSTRELFdBQVU7c0NBQ2IsNEVBQUM1RDtnQ0FBSTRELFdBQVU7MENBRWIsNEVBQUM1RDtvQ0FBSTRELFdBQVU7O3NEQUViLDhEQUFDNUQ7NENBQUk0RCxXQUFVOzs4REFDYiw4REFBQ0M7b0RBQUdELFdBQVU7OERBQWU7Ozs7Ozs4REFHN0IsOERBQUNFO29EQUNDRixXQUFVO29EQUNWRyxrQkFBZTtvREFDZkMsZUFBWTs7d0RBQ2I7d0RBQ2lDO3NFQUNoQyw4REFBQ0M7c0VBQUs7Ozs7Ozs7Ozs7Ozs4REFFUiw4REFBQ0M7b0RBQUVOLFdBQVU7b0RBQWVHLGtCQUFlOzhEQUFPOzs7Ozs7Ozs7Ozs7c0RBT3BELDhEQUFDL0Q7NENBQUk0RCxXQUFVOzRDQUF3Qkcsa0JBQWU7OzhEQUNwRCw4REFBQy9CO29EQUFFbUMsTUFBSztvREFBZ0JQLFdBQVU7OERBQThCOzs7Ozs7OERBR2hFLDhEQUFDNUI7b0RBQUVtQyxNQUFLO29EQUFpQlAsV0FBVTs4REFBYzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FTekQsOERBQUM1RDs0QkFBSTRELFdBQVU7c0NBQ2IsNEVBQUM1RDtnQ0FBSTRELFdBQVU7MENBRWIsNEVBQUM1RDtvQ0FBSTRELFdBQVU7O3NEQUNiLDhEQUFDTTs7Z0RBQUU7OERBQzBCLDhEQUFDRDtvREFBS0wsV0FBVTs4REFBV2pELFFBQVFKLE1BQU07Ozs7OztnREFBUTs7Ozs7OztzREFHOUUsOERBQUNQOzRDQUNDQyxPQUFPO2dEQUNMLEdBQUdULGNBQWM7Z0RBQ2pCNEUsUUFBUTFFLGFBQWEsYUFBYTs0Q0FDcEM7NENBQ0EyRSxhQUFheEI7NENBQ2J5QixhQUFhdEI7NENBQ2J1QixXQUFXbkI7NENBQ1hvQixjQUFjbkI7NENBQ2RvQixjQUFjbkI7NENBQ2RvQixhQUFhbEI7NENBQ2JtQixZQUFZbEI7c0RBRVosNEVBQUN6RDtnREFBSUMsT0FBT1IsV0FBV0MsWUFBWUM7MERBQ2hDK0Q7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFZckI7R0F0VU01QztNQUFBQTtBQXdVTixpRUFBZUEsWUFBWUEsRUFBQyIsInNvdXJjZXMiOlsiL1ZvbHVtZXMvRmlsZXMvVGVjaG5vbG93YXktTmV3LVdlYnNpdGUvVGVjaG5vbG93YXkvc3JjL2NvbXBvbmVudHMvbWFpbi9ob21lL2hlcm8vSG9tZU1haW5IZXJvLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuXG5pbnRlcmZhY2UgQ2xpZW50IHtcbiAgaWQ6IHN0cmluZztcbiAgY29tcGFueW5hbWU6IHN0cmluZztcbiAgbG9nb3VybD86IHN0cmluZztcbn1cblxuLy8gU3R5bGUgY29uc3RhbnRzXG5jb25zdCBMT0dPX1NJWkUgPSAnNjBweCc7IC8vIEluY3JlYXNlZCBmcm9tIDQwcHggdG8gNjBweFxuY29uc3QgRFJBR19TRU5TSVRJVklUWSA9IDAuNDtcbmNvbnN0IFRFWFRfV0lEVEhfTVVMVElQTElFUiA9IDEwO1xuY29uc3QgTUlOX1RFWFRfV0lEVEggPSA2MDtcbmNvbnN0IFRFWFRfUEFERElORyA9IDU7IC8vIFJlZHVjZWQgZnJvbSAyMCB0byA1XG5jb25zdCBMT0dPX0dBUCA9IDA7IC8vIE5vIGdhcCBiZXR3ZWVuIGxvZ28gYW5kIGNsaWVudCBuYW1lXG5cbmNvbnN0IGxvZ29Db250YWluZXJTdHlsZSA9IHtcbiAgd2lkdGg6IExPR09fU0laRSxcbiAgaGVpZ2h0OiBMT0dPX1NJWkUsXG4gIGJvcmRlclJhZGl1czogJzUwJScsXG4gIGJhY2tncm91bmQ6ICdsaW5lYXItZ3JhZGllbnQodmFyKC0tYmctY29sb3IpLCB2YXIoLS1iZy1jb2xvcikpIHBhZGRpbmctYm94LCBsaW5lYXItZ3JhZGllbnQodG8gbGVmdCwgdmFyKC0tYWNjZW50LWNvbG9yKSwgdmFyKC0tYWNjZW50LXNlY29uZGFyeS1jb2xvcikpIGJvcmRlci1ib3gnLFxuICBib3JkZXI6ICcxcHggc29saWQgdHJhbnNwYXJlbnQnLFxuICBwYWRkaW5nOiAnMnB4JywgLy8gU3BhY2UgZm9yIHRoZSBib3JkZXJcbiAgZGlzcGxheTogJ2ZsZXgnLFxuICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInXG59O1xuXG5jb25zdCBsb2dvU3R5bGUgPSB7XG4gIHdpZHRoOiAnY2FsYygxMDAlIC0gNHB4KScsIC8vIFNsaWdodGx5IHNtYWxsZXIgdG8gYWNjb3VudCBmb3IgYm9yZGVyXG4gIGhlaWdodDogJ2NhbGMoMTAwJSAtIDRweCknLFxuICBib3JkZXJSYWRpdXM6ICc1MCUnLFxuICBiYWNrZ3JvdW5kU2l6ZTogJ2NvdmVyJyxcbiAgYmFja2dyb3VuZFJlcGVhdDogJ25vLXJlcGVhdCcsXG4gIGJhY2tncm91bmRQb3NpdGlvbjogJ2NlbnRlcicsXG4gIGJhY2tncm91bmRDb2xvcjogJ3RyYW5zcGFyZW50JyxcbiAgb3ZlcmZsb3c6ICdoaWRkZW4nXG59O1xuXG5jb25zdCBjbGllbnRJdGVtU3R5bGUgPSAodGV4dExlbmd0aDogbnVtYmVyKSA9PiB7XG4gIGNvbnN0IHRleHRXaWR0aCA9IE1hdGgubWF4KHRleHRMZW5ndGggKiBURVhUX1dJRFRIX01VTFRJUExJRVIsIE1JTl9URVhUX1dJRFRIKTtcbiAgY29uc3QgdG90YWxXaWR0aCA9IHBhcnNlSW50KExPR09fU0laRSkgKyBMT0dPX0dBUCArIHRleHRXaWR0aCArIFRFWFRfUEFERElORztcblxuICByZXR1cm4ge1xuICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICBnYXA6IGAke0xPR09fR0FQfXB4YCxcbiAgICB3aWR0aDogYCR7dG90YWxXaWR0aH1weGAsXG4gICAgZmxleFNocmluazogMCxcbiAgICBtYXJnaW5SaWdodDogJzBweCcsIC8vIE5vIG1hcmdpbiBzaW5jZSBjb250YWluZXIgaGFuZGxlcyBzcGFjaW5nXG4gICAgb3ZlcmZsb3c6ICdoaWRkZW4nXG4gIH07XG59O1xuXG5jb25zdCBjb21wYW55TmFtZVN0eWxlID0ge1xuICBjb2xvcjogJ3doaXRlJyxcbiAgZm9udFNpemU6ICcxOHB4JyxcbiAgZm9udFdlaWdodDogJ25vcm1hbCcsXG4gIGZvbnRGYW1pbHk6ICdzYW5zLXNlcmlmJyxcbiAgd2hpdGVTcGFjZTogJ25vd3JhcCcsXG4gIG92ZXJmbG93OiAnaGlkZGVuJyxcbiAgdGV4dE92ZXJmbG93OiAnZWxsaXBzaXMnLFxuICBmbGV4OiAxXG59O1xuXG5jb25zdCBjb250YWluZXJTdHlsZSA9IHtcbiAgb3ZlcmZsb3c6ICdoaWRkZW4nLFxuICBkaXNwbGF5OiAnZmxleCcsXG4gIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICBnYXA6ICcyMHB4JyAvLyBDb25zaXN0ZW50IGdhcCBiZXR3ZWVuIGFsbCBjbGllbnQgYmxvY2tzXG59O1xuXG5jb25zdCB0cmFja1N0eWxlID0gKGlzRHJhZ2dpbmc6IGJvb2xlYW4sIHNjcm9sbExlZnQ6IG51bWJlcikgPT4gKHtcbiAgZGlzcGxheTogJ2ZsZXgnLFxuICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgZ2FwOiAnMHB4JyxcbiAgdHJhbnNpdGlvbjogaXNEcmFnZ2luZyA/ICdub25lJyA6ICd0cmFuc2Zvcm0gMnMgZWFzZS1pbi1vdXQnLFxuICB0cmFuc2Zvcm06IGB0cmFuc2xhdGVYKC0ke3Njcm9sbExlZnR9cHgpYFxufSk7XG5cbi8vIExvZ28gY29tcG9uZW50XG5jb25zdCBDbGllbnRMb2dvOiBSZWFjdC5GQzx7IGxvZ291cmw/OiBzdHJpbmcgfT4gPSAoeyBsb2dvdXJsIH0pID0+IChcbiAgPGRpdiBzdHlsZT17bG9nb0NvbnRhaW5lclN0eWxlfT5cbiAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAuLi5sb2dvU3R5bGUsXG4gICAgICBiYWNrZ3JvdW5kSW1hZ2U6IGB1cmwoJHtsb2dvdXJsIHx8IFwiL2ltYWdlcy9pY29uLXRlc3RpbW9uaWFsLWxvZ28uc3ZnXCJ9KWBcbiAgICB9fSAvPlxuICA8L2Rpdj5cbik7XG5cbi8vIENsaWVudCBpdGVtIGNvbXBvbmVudFxuY29uc3QgQ2xpZW50SXRlbTogUmVhY3QuRkM8eyBjbGllbnQ6IENsaWVudDsgaW5kZXg6IG51bWJlciB9PiA9ICh7IGNsaWVudCwgaW5kZXggfSkgPT4ge1xuICBjb25zdCB0ZXh0TGVuZ3RoID0gY2xpZW50LmNvbXBhbnluYW1lLmxlbmd0aDtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGtleT17Y2xpZW50LmlkIHx8IGluZGV4fSBzdHlsZT17Y2xpZW50SXRlbVN0eWxlKHRleHRMZW5ndGgpfT5cbiAgICAgIDxDbGllbnRMb2dvIGxvZ291cmw9e2NsaWVudC5sb2dvdXJsfSAvPlxuICAgICAgPGRpdiBzdHlsZT17Y29tcGFueU5hbWVTdHlsZX0+XG4gICAgICAgIHtjbGllbnQuY29tcGFueW5hbWV9XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbi8vIEhlbHBlciBmdW5jdGlvbiB0byBjYWxjdWxhdGUgY2xpZW50IHdpZHRoXG5jb25zdCBjYWxjdWxhdGVDbGllbnRXaWR0aCA9ICh0ZXh0TGVuZ3RoOiBudW1iZXIpOiBudW1iZXIgPT4ge1xuICBjb25zdCB0ZXh0V2lkdGggPSBNYXRoLm1heCh0ZXh0TGVuZ3RoICogVEVYVF9XSURUSF9NVUxUSVBMSUVSLCBNSU5fVEVYVF9XSURUSCk7XG4gIHJldHVybiBwYXJzZUludChMT0dPX1NJWkUpICsgTE9HT19HQVAgKyB0ZXh0V2lkdGggKyBURVhUX1BBRERJTkc7XG59O1xuXG4vLyBIZWxwZXIgZnVuY3Rpb24gdG8gY2FsY3VsYXRlIHRvdGFsIHdpZHRoXG5jb25zdCBjYWxjdWxhdGVUb3RhbFdpZHRoID0gKGNsaWVudHM6IENsaWVudFtdKTogbnVtYmVyID0+IHtcbiAgcmV0dXJuIGNsaWVudHMucmVkdWNlKChzdW0sIGNsaWVudCkgPT4ge1xuICAgIHJldHVybiBzdW0gKyBjYWxjdWxhdGVDbGllbnRXaWR0aChjbGllbnQuY29tcGFueW5hbWUubGVuZ3RoKTtcbiAgfSwgMCk7XG59O1xuXG5jb25zdCBIb21lTWFpbkhlcm86IFJlYWN0LkZDID0gKCkgPT4ge1xuICBjb25zdCBbY2xpZW50cywgc2V0Q2xpZW50c10gPSB1c2VTdGF0ZTxDbGllbnRbXT4oW10pO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW2lzRHJhZ2dpbmcsIHNldElzRHJhZ2dpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc3RhcnRYLCBzZXRTdGFydFhdID0gdXNlU3RhdGUoMCk7XG4gIGNvbnN0IFtzY3JvbGxMZWZ0LCBzZXRTY3JvbGxMZWZ0XSA9IHVzZVN0YXRlKDE2MCk7IC8vIFN0YXJ0IHdpdGggZmlyc3QgY2xpZW50IGZ1bGx5IGhpZGRlblxuICBjb25zdCBbYXV0b1Njcm9sbEludGVydmFsLCBzZXRBdXRvU2Nyb2xsSW50ZXJ2YWxdID0gdXNlU3RhdGU8Tm9kZUpTLlRpbWVvdXQgfCBudWxsPihudWxsKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGZldGNoQ2xpZW50cyA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICAgIC8vIEZldGNoIGFsbCBhY3RpdmUgY2xpZW50cyB3aXRob3V0IGxpbWl0LCBzb3J0ZWQgYnkgbmFtZVxuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2NsaWVudHM/bGltaXQ9MCcpO1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBpZiAoZGF0YS5zdWNjZXNzKSB7XG4gICAgICAgICAgLy8gU29ydCBjbGllbnRzIGJ5IGNvbXBhbnkgbmFtZSB0byBlbnN1cmUgYWxwaGFiZXRpY2FsIG9yZGVyXG4gICAgICAgICAgY29uc3Qgc29ydGVkQ2xpZW50cyA9IChkYXRhLmNsaWVudHMgfHwgW10pLnNvcnQoKGE6IENsaWVudCwgYjogQ2xpZW50KSA9PlxuICAgICAgICAgICAgYS5jb21wYW55bmFtZS5sb2NhbGVDb21wYXJlKGIuY29tcGFueW5hbWUpXG4gICAgICAgICAgKTtcbiAgICAgICAgICBzZXRDbGllbnRzKHNvcnRlZENsaWVudHMpO1xuICAgICAgICAgIC8vIFNldCBpbml0aWFsIHNjcm9sbCBwb3NpdGlvbiB0byBoaWRlIGZpcnN0IGNsaWVudFxuICAgICAgICAgIGlmIChzb3J0ZWRDbGllbnRzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgIGNvbnN0IGZpcnN0Q2xpZW50V2lkdGggPSBjYWxjdWxhdGVDbGllbnRXaWR0aChzb3J0ZWRDbGllbnRzWzBdLmNvbXBhbnluYW1lLmxlbmd0aCk7XG4gICAgICAgICAgICBzZXRTY3JvbGxMZWZ0KGZpcnN0Q2xpZW50V2lkdGgpO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgY2xpZW50czonLCBlcnJvcik7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICAgIH1cbiAgICB9O1xuICAgIGZldGNoQ2xpZW50cygpO1xuICB9LCBbXSk7XG5cbiAgLy8gQXV0by1zY3JvbGwgZnVuY3Rpb24gLSBtb3ZlcyBvbmUgY2xpZW50IGF0IGEgdGltZVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChjbGllbnRzLmxlbmd0aCA9PT0gMCB8fCBpc0RyYWdnaW5nKSByZXR1cm47XG5cbiAgICBsZXQgY3VycmVudENsaWVudEluZGV4ID0gMDtcblxuICAgIGNvbnN0IGludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgc2V0U2Nyb2xsTGVmdChwcmV2U2Nyb2xsTGVmdCA9PiB7XG4gICAgICAgIGNvbnN0IHRvdGFsV2lkdGggPSBjYWxjdWxhdGVUb3RhbFdpZHRoKGNsaWVudHMpO1xuXG4gICAgICAgIC8vIENhbGN1bGF0ZSB0aGUgd2lkdGggb2YgdGhlIGN1cnJlbnQgY2xpZW50IHRvIG1vdmVcbiAgICAgICAgY29uc3QgY3VycmVudENsaWVudFdpZHRoID0gY2FsY3VsYXRlQ2xpZW50V2lkdGgoY2xpZW50c1tjdXJyZW50Q2xpZW50SW5kZXhdPy5jb21wYW55bmFtZS5sZW5ndGggfHwgMCk7XG5cbiAgICAgICAgbGV0IG5ld1Njcm9sbExlZnQgPSBwcmV2U2Nyb2xsTGVmdCArIGN1cnJlbnRDbGllbnRXaWR0aDtcblxuICAgICAgICAvLyBNb3ZlIHRvIG5leHQgY2xpZW50XG4gICAgICAgIGN1cnJlbnRDbGllbnRJbmRleCA9IChjdXJyZW50Q2xpZW50SW5kZXggKyAxKSAlIGNsaWVudHMubGVuZ3RoO1xuXG4gICAgICAgIC8vIFJlc2V0IHRvIGJlZ2lubmluZyB3aGVuIHdlJ3ZlIHNjcm9sbGVkIHRocm91Z2ggYWxsIGNsaWVudHNcbiAgICAgICAgaWYgKG5ld1Njcm9sbExlZnQgPj0gdG90YWxXaWR0aCkge1xuICAgICAgICAgIG5ld1Njcm9sbExlZnQgPSBjYWxjdWxhdGVDbGllbnRXaWR0aChjbGllbnRzWzBdPy5jb21wYW55bmFtZS5sZW5ndGggfHwgMCk7XG4gICAgICAgICAgY3VycmVudENsaWVudEluZGV4ID0gMTsgLy8gU3RhcnQgZnJvbSBzZWNvbmQgY2xpZW50IG5leHQgdGltZVxuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIG5ld1Njcm9sbExlZnQ7XG4gICAgICB9KTtcbiAgICB9LCA1MDAwKTtcblxuICAgIHNldEF1dG9TY3JvbGxJbnRlcnZhbChpbnRlcnZhbCk7XG5cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgaWYgKGludGVydmFsKSB7XG4gICAgICAgIGNsZWFySW50ZXJ2YWwoaW50ZXJ2YWwpO1xuICAgICAgfVxuICAgIH07XG4gIH0sIFtjbGllbnRzLmxlbmd0aCwgaXNEcmFnZ2luZ10pO1xuXG4gIC8vIENsZWFudXAgaW50ZXJ2YWwgb24gdW5tb3VudFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBpZiAoYXV0b1Njcm9sbEludGVydmFsKSB7XG4gICAgICAgIGNsZWFySW50ZXJ2YWwoYXV0b1Njcm9sbEludGVydmFsKTtcbiAgICAgIH1cbiAgICB9O1xuICB9LCBbYXV0b1Njcm9sbEludGVydmFsXSk7XG5cbiAgY29uc3QgaGFuZGxlTW91c2VEb3duID0gKGU6IFJlYWN0Lk1vdXNlRXZlbnQpID0+IHtcbiAgICBzZXRJc0RyYWdnaW5nKHRydWUpO1xuICAgIHNldFN0YXJ0WChlLnBhZ2VYKTtcbiAgICAvLyBTdG9wIGF1dG8tc2Nyb2xsIHdoZW4gdXNlciBzdGFydHMgZHJhZ2dpbmdcbiAgICBpZiAoYXV0b1Njcm9sbEludGVydmFsKSB7XG4gICAgICBjbGVhckludGVydmFsKGF1dG9TY3JvbGxJbnRlcnZhbCk7XG4gICAgICBzZXRBdXRvU2Nyb2xsSW50ZXJ2YWwobnVsbCk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZU1vdXNlTW92ZSA9IChlOiBSZWFjdC5Nb3VzZUV2ZW50KSA9PiB7XG4gICAgaWYgKCFpc0RyYWdnaW5nKSByZXR1cm47XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIGNvbnN0IHggPSBlLnBhZ2VYO1xuICAgIGNvbnN0IHdhbGsgPSAoeCAtIHN0YXJ0WCkgKiBEUkFHX1NFTlNJVElWSVRZO1xuICAgIGxldCBuZXdTY3JvbGxMZWZ0ID0gc2Nyb2xsTGVmdCAtIHdhbGs7XG4gICAgXG4gICAgY29uc3QgdG90YWxXaWR0aCA9IGNhbGN1bGF0ZVRvdGFsV2lkdGgoY2xpZW50cyk7XG4gICAgXG4gICAgLy8gTG9vcCBiYWNrIHRvIGJlZ2lubmluZyB3aGVuIHJlYWNoaW5nIHRoZSBlbmRcbiAgICBpZiAobmV3U2Nyb2xsTGVmdCA+PSB0b3RhbFdpZHRoKSB7XG4gICAgICBjb25zdCBmaXJzdENsaWVudFdpZHRoID0gY2FsY3VsYXRlQ2xpZW50V2lkdGgoY2xpZW50c1swXT8uY29tcGFueW5hbWUubGVuZ3RoIHx8IDApO1xuICAgICAgbmV3U2Nyb2xsTGVmdCA9IG5ld1Njcm9sbExlZnQgLSB0b3RhbFdpZHRoICsgZmlyc3RDbGllbnRXaWR0aDtcbiAgICB9XG4gICAgLy8gTG9vcCBiYWNrIHRvIGVuZCB3aGVuIGdvaW5nIGJlZm9yZSBiZWdpbm5pbmdcbiAgICBlbHNlIGlmIChuZXdTY3JvbGxMZWZ0IDwgMCkge1xuICAgICAgbmV3U2Nyb2xsTGVmdCA9IHRvdGFsV2lkdGggKyBuZXdTY3JvbGxMZWZ0O1xuICAgIH1cbiAgICBcbiAgICBzZXRTY3JvbGxMZWZ0KG5ld1Njcm9sbExlZnQpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZU1vdXNlVXAgPSAoKSA9PiB7XG4gICAgc2V0SXNEcmFnZ2luZyhmYWxzZSk7XG4gICAgLy8gUmVzdGFydCBhdXRvLXNjcm9sbCBhZnRlciB1c2VyIHN0b3BzIGRyYWdnaW5nXG4gICAgaWYgKGNsaWVudHMubGVuZ3RoID4gMCkge1xuICAgICAgbGV0IGN1cnJlbnRDbGllbnRJbmRleCA9IDA7XG5cbiAgICAgIGNvbnN0IGludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgICBzZXRTY3JvbGxMZWZ0KHByZXZTY3JvbGxMZWZ0ID0+IHtcbiAgICAgICAgICBjb25zdCB0b3RhbFdpZHRoID0gY2FsY3VsYXRlVG90YWxXaWR0aChjbGllbnRzKTtcblxuICAgICAgICAgIC8vIENhbGN1bGF0ZSB0aGUgd2lkdGggb2YgdGhlIGN1cnJlbnQgY2xpZW50IHRvIG1vdmVcbiAgICAgICAgICBjb25zdCBjdXJyZW50Q2xpZW50V2lkdGggPSBjYWxjdWxhdGVDbGllbnRXaWR0aChjbGllbnRzW2N1cnJlbnRDbGllbnRJbmRleF0/LmNvbXBhbnluYW1lLmxlbmd0aCB8fCAwKTtcblxuICAgICAgICAgIGxldCBuZXdTY3JvbGxMZWZ0ID0gcHJldlNjcm9sbExlZnQgKyBjdXJyZW50Q2xpZW50V2lkdGg7XG5cbiAgICAgICAgICAvLyBNb3ZlIHRvIG5leHQgY2xpZW50XG4gICAgICAgICAgY3VycmVudENsaWVudEluZGV4ID0gKGN1cnJlbnRDbGllbnRJbmRleCArIDEpICUgY2xpZW50cy5sZW5ndGg7XG5cbiAgICAgICAgICAvLyBSZXNldCB0byBiZWdpbm5pbmcgd2hlbiB3ZSd2ZSBzY3JvbGxlZCB0aHJvdWdoIGFsbCBjbGllbnRzXG4gICAgICAgICAgaWYgKG5ld1Njcm9sbExlZnQgPj0gdG90YWxXaWR0aCkge1xuICAgICAgICAgICAgbmV3U2Nyb2xsTGVmdCA9IGNhbGN1bGF0ZUNsaWVudFdpZHRoKGNsaWVudHNbMF0/LmNvbXBhbnluYW1lLmxlbmd0aCB8fCAwKTtcbiAgICAgICAgICAgIGN1cnJlbnRDbGllbnRJbmRleCA9IDE7IC8vIFN0YXJ0IGZyb20gc2Vjb25kIGNsaWVudCBuZXh0IHRpbWVcbiAgICAgICAgICB9XG5cbiAgICAgICAgICByZXR1cm4gbmV3U2Nyb2xsTGVmdDtcbiAgICAgICAgfSk7XG4gICAgICB9LCA1MDAwKTtcblxuICAgICAgc2V0QXV0b1Njcm9sbEludGVydmFsKGludGVydmFsKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlTW91c2VMZWF2ZSA9ICgpID0+IHtcbiAgICBzZXRJc0RyYWdnaW5nKGZhbHNlKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVUb3VjaFN0YXJ0ID0gKGU6IFJlYWN0LlRvdWNoRXZlbnQpID0+IHtcbiAgICBzZXRJc0RyYWdnaW5nKHRydWUpO1xuICAgIHNldFN0YXJ0WChlLnRvdWNoZXNbMF0ucGFnZVgpO1xuICAgIC8vIFN0b3AgYXV0by1zY3JvbGwgd2hlbiB1c2VyIHN0YXJ0cyB0b3VjaGluZ1xuICAgIGlmIChhdXRvU2Nyb2xsSW50ZXJ2YWwpIHtcbiAgICAgIGNsZWFySW50ZXJ2YWwoYXV0b1Njcm9sbEludGVydmFsKTtcbiAgICAgIHNldEF1dG9TY3JvbGxJbnRlcnZhbChudWxsKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlVG91Y2hNb3ZlID0gKGU6IFJlYWN0LlRvdWNoRXZlbnQpID0+IHtcbiAgICBpZiAoIWlzRHJhZ2dpbmcpIHJldHVybjtcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgY29uc3QgeCA9IGUudG91Y2hlc1swXS5wYWdlWDtcbiAgICBjb25zdCB3YWxrID0gKHggLSBzdGFydFgpICogRFJBR19TRU5TSVRJVklUWTtcbiAgICBsZXQgbmV3U2Nyb2xsTGVmdCA9IHNjcm9sbExlZnQgLSB3YWxrO1xuICAgIFxuICAgIGNvbnN0IHRvdGFsV2lkdGggPSBjYWxjdWxhdGVUb3RhbFdpZHRoKGNsaWVudHMpO1xuICAgIFxuICAgIC8vIExvb3AgYmFjayB0byBiZWdpbm5pbmcgd2hlbiByZWFjaGluZyB0aGUgZW5kXG4gICAgaWYgKG5ld1Njcm9sbExlZnQgPj0gdG90YWxXaWR0aCkge1xuICAgICAgY29uc3QgZmlyc3RDbGllbnRXaWR0aCA9IGNhbGN1bGF0ZUNsaWVudFdpZHRoKGNsaWVudHNbMF0/LmNvbXBhbnluYW1lLmxlbmd0aCB8fCAwKTtcbiAgICAgIG5ld1Njcm9sbExlZnQgPSBuZXdTY3JvbGxMZWZ0IC0gdG90YWxXaWR0aCArIGZpcnN0Q2xpZW50V2lkdGg7XG4gICAgfVxuICAgIC8vIExvb3AgYmFjayB0byBlbmQgd2hlbiBnb2luZyBiZWZvcmUgYmVnaW5uaW5nXG4gICAgZWxzZSBpZiAobmV3U2Nyb2xsTGVmdCA8IDApIHtcbiAgICAgIG5ld1Njcm9sbExlZnQgPSB0b3RhbFdpZHRoICsgbmV3U2Nyb2xsTGVmdDtcbiAgICB9XG4gICAgXG4gICAgc2V0U2Nyb2xsTGVmdChuZXdTY3JvbGxMZWZ0KTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVUb3VjaEVuZCA9ICgpID0+IHtcbiAgICBzZXRJc0RyYWdnaW5nKGZhbHNlKTtcbiAgICAvLyBSZXN0YXJ0IGF1dG8tc2Nyb2xsIGFmdGVyIHVzZXIgc3RvcHMgdG91Y2hpbmdcbiAgICBpZiAoY2xpZW50cy5sZW5ndGggPiAwKSB7XG4gICAgICBjb25zdCBpbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHtcbiAgICAgICAgc2V0U2Nyb2xsTGVmdChwcmV2U2Nyb2xsTGVmdCA9PiB7XG4gICAgICAgICAgY29uc3QgdG90YWxXaWR0aCA9IGNhbGN1bGF0ZVRvdGFsV2lkdGgoY2xpZW50cyk7XG4gICAgICAgICAgY29uc3QgZmlyc3RDbGllbnRXaWR0aCA9IGNhbGN1bGF0ZUNsaWVudFdpZHRoKGNsaWVudHNbMF0uY29tcGFueW5hbWUubGVuZ3RoKTtcbiAgICAgICAgICBcbiAgICAgICAgICBsZXQgbmV3U2Nyb2xsTGVmdCA9IHByZXZTY3JvbGxMZWZ0ICsgZmlyc3RDbGllbnRXaWR0aDtcbiAgICAgICAgICBcbiAgICAgICAgICBpZiAobmV3U2Nyb2xsTGVmdCA+PSB0b3RhbFdpZHRoKSB7XG4gICAgICAgICAgICBuZXdTY3JvbGxMZWZ0ID0gZmlyc3RDbGllbnRXaWR0aDtcbiAgICAgICAgICB9XG4gICAgICAgICAgXG4gICAgICAgICAgcmV0dXJuIG5ld1Njcm9sbExlZnQ7XG4gICAgICAgIH0pO1xuICAgICAgfSwgNTAwMCk7XG4gICAgICBcbiAgICAgIHNldEF1dG9TY3JvbGxJbnRlcnZhbChpbnRlcnZhbCk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHJlbmRlckNsaWVudEl0ZW1zID0gKCkgPT4ge1xuICAgIGlmIChsb2FkaW5nKSB7XG4gICAgICByZXR1cm4gKFxuICAgICAgICA8ZGl2IHN0eWxlPXtjbGllbnRJdGVtU3R5bGUoNyl9PiB7LyogXCJMb2FkaW5nXCIgaXMgNyBjaGFyYWN0ZXJzICovfVxuICAgICAgICAgIDxDbGllbnRMb2dvIC8+XG4gICAgICAgICAgPGRpdiBzdHlsZT17eyAuLi5jb21wYW55TmFtZVN0eWxlLCBmb250U2l6ZTogJzI2cHgnLCBmb250V2VpZ2h0OiAnYm9sZCcgfX0+XG4gICAgICAgICAgICBMb2FkaW5nLi4uXG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKTtcbiAgICB9XG5cbiAgICBpZiAoY2xpZW50cy5sZW5ndGggPT09IDApIHtcbiAgICAgIHJldHVybiAoXG4gICAgICAgIDxkaXYgc3R5bGU9e2NsaWVudEl0ZW1TdHlsZSgxMil9PiB7LyogXCJObyBjbGllbnRzIHlldFwiIGlzIDEyIGNoYXJhY3RlcnMgKi99XG4gICAgICAgICAgPENsaWVudExvZ28gLz5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IC4uLmNvbXBhbnlOYW1lU3R5bGUsIGZvbnRTaXplOiAnMjZweCcsIGZvbnRXZWlnaHQ6ICdib2xkJyB9fT5cbiAgICAgICAgICAgIE5vIGNsaWVudHMgeWV0XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKTtcbiAgICB9XG5cbiAgICByZXR1cm4gKFxuICAgICAgPD5cbiAgICAgICAge2NsaWVudHMubWFwKChjbGllbnQsIGluZGV4KSA9PiAoXG4gICAgICAgICAgPENsaWVudEl0ZW0ga2V5PXtjbGllbnQuaWQgfHwgaW5kZXh9IGNsaWVudD17Y2xpZW50fSBpbmRleD17aW5kZXh9IC8+XG4gICAgICAgICkpfVxuICAgICAgICB7LyogRHVwbGljYXRlIGZvciBzZWFtbGVzcyBsb29wICovfVxuICAgICAgICB7Y2xpZW50cy5tYXAoKGNsaWVudCwgaW5kZXgpID0+IChcbiAgICAgICAgICA8Q2xpZW50SXRlbSBrZXk9e2BkdXBsaWNhdGUtJHtjbGllbnQuaWQgfHwgaW5kZXh9YH0gY2xpZW50PXtjbGllbnR9IGluZGV4PXtpbmRleH0gLz5cbiAgICAgICAgKSl9XG4gICAgICA8Lz5cbiAgICApO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPD5cbiAgICAgIHsvKiBIZXJvIFNlY3Rpb24gU3RhcnQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImhlcm9cIj5cbiAgICAgICAgey8qIEdyaWQgTGluZXMgU3RhcnQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZC1saW5lc1wiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZC1saW5lLTFcIiAvPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZC1saW5lLTJcIiAvPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZC1saW5lLTNcIiAvPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZC1saW5lLTRcIiAvPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZC1saW5lLTVcIiAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgey8qIEdyaWQgTGluZXMgRW5kICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicm93IGFsaWduLWl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtbGctMTJcIj5cbiAgICAgICAgICAgICAgey8qIEhlcm8gQ29udGVudCBTdGFydCAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoZXJvLWNvbnRlbnRcIj5cbiAgICAgICAgICAgICAgICB7LyogU2VjdGlvbiBUaXRsZSBTdGFydCAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNlY3Rpb24tdGl0bGUgc2VjdGlvbi10aXRsZS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ3b3cgZmFkZUluVXBcIj5cbiAgICAgICAgICAgICAgICAgICAgRW50ZXJwcmlzZS1ncmFkZSBzb2Z0d2FyZSAmIHdlYiBkZXZlbG9wbWVudCBzb2x1dGlvbnNcbiAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICA8aDFcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwid293IGZhZGVJblVwXCJcbiAgICAgICAgICAgICAgICAgICAgZGF0YS13b3ctZGVsYXk9XCIwLjJzXCJcbiAgICAgICAgICAgICAgICAgICAgZGF0YS1jdXJzb3I9XCItb3BhcXVlXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgQWR2YW5jZWQgZGlnaXRhbCBzb2x1dGlvbnMgdGhhdHtcIiBcIn1cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4+YWNjZWxlcmF0ZSB5b3VyIGdyb3d0aDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ3b3cgZmFkZUluVXBcIiBkYXRhLXdvdy1kZWxheT1cIjAuNHNcIj5cbiAgICAgICAgICAgICAgICAgICAgVHJhbnNmb3JtIHlvdXIgdmlzaW9uIGludG8gc2NhbGFibGUgYXBwbGljYXRpb25zIGFuZCBzb3BoaXN0aWNhdGVkIHdlYiBwbGF0Zm9ybXMuXG4gICAgICAgICAgICAgICAgICAgIFByb2Zlc3Npb25hbCBkZXZlbG9wbWVudCBzZXJ2aWNlcyB0aGF0IGRlbGl2ZXIgbWVhc3VyYWJsZSByZXN1bHRzLlxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIHsvKiBTZWN0aW9uIFRpdGxlIEVuZCAqL31cbiAgICAgICAgICAgICAgICB7LyogSGVybyBCdXR0b24gU3RhcnQgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoZXJvLWJ0biB3b3cgZmFkZUluVXBcIiBkYXRhLXdvdy1kZWxheT1cIjAuNnNcIj5cbiAgICAgICAgICAgICAgICAgIDxhIGhyZWY9XCIvbWFpbi9jb250YWN0XCIgY2xhc3NOYW1lPVwiYnRuLWRlZmF1bHQgYnRuLWhpZ2hsaWdodGVkXCI+XG4gICAgICAgICAgICAgICAgICAgIEdldCBGcmVlIEFzc2Vzc21lbnRcbiAgICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICAgIDxhIGhyZWY9XCIvbWFpbi9zZXJ2aWNlc1wiIGNsYXNzTmFtZT1cImJ0bi1kZWZhdWx0XCI+XG4gICAgICAgICAgICAgICAgICAgIFZpZXcgb3VyIHNlcnZpY2VzXG4gICAgICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgey8qIEhlcm8gQnV0dG9uIEVuZCAqL31cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIHsvKiBIZXJvIENvbnRlbnQgRW5kICovfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyb3dcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLWxnLTEyXCI+XG4gICAgICAgICAgICAgIHsvKiBIZXJvIENvbXBhbnkgU2xpZGVyIFN0YXJ0ICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImhlcm8tY29tcGFueS1zbGlkZXJcIj5cbiAgICAgICAgICAgICAgICA8cD5cbiAgICAgICAgICAgICAgICAgIFdlJ3JlIFRydXN0ZWQgYnkgbW9yZSB0aGFuIDxzcGFuIGNsYXNzTmFtZT1cImNvdW50ZXJcIj57Y2xpZW50cy5sZW5ndGh9PC9zcGFuPitcbiAgICAgICAgICAgICAgICAgIGNvbXBhbmllc1xuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8ZGl2IFxuICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgXG4gICAgICAgICAgICAgICAgICAgIC4uLmNvbnRhaW5lclN0eWxlLFxuICAgICAgICAgICAgICAgICAgICBjdXJzb3I6IGlzRHJhZ2dpbmcgPyAnZ3JhYmJpbmcnIDogJ2dyYWInXG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgb25Nb3VzZURvd249e2hhbmRsZU1vdXNlRG93bn1cbiAgICAgICAgICAgICAgICAgIG9uTW91c2VNb3ZlPXtoYW5kbGVNb3VzZU1vdmV9XG4gICAgICAgICAgICAgICAgICBvbk1vdXNlVXA9e2hhbmRsZU1vdXNlVXB9XG4gICAgICAgICAgICAgICAgICBvbk1vdXNlTGVhdmU9e2hhbmRsZU1vdXNlTGVhdmV9XG4gICAgICAgICAgICAgICAgICBvblRvdWNoU3RhcnQ9e2hhbmRsZVRvdWNoU3RhcnR9XG4gICAgICAgICAgICAgICAgICBvblRvdWNoTW92ZT17aGFuZGxlVG91Y2hNb3ZlfVxuICAgICAgICAgICAgICAgICAgb25Ub3VjaEVuZD17aGFuZGxlVG91Y2hFbmR9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17dHJhY2tTdHlsZShpc0RyYWdnaW5nLCBzY3JvbGxMZWZ0KX0+XG4gICAgICAgICAgICAgICAgICAgIHtyZW5kZXJDbGllbnRJdGVtcygpfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICB7LyogSGVybyBDb21wYW55IFNsaWRlciBFbmQgKi99XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICAgIHsvKiBIZXJvIFNlY3Rpb24gRW5kICovfVxuICAgIDwvPlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgSG9tZU1haW5IZXJvO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJMT0dPX1NJWkUiLCJEUkFHX1NFTlNJVElWSVRZIiwiVEVYVF9XSURUSF9NVUxUSVBMSUVSIiwiTUlOX1RFWFRfV0lEVEgiLCJURVhUX1BBRERJTkciLCJMT0dPX0dBUCIsImxvZ29Db250YWluZXJTdHlsZSIsIndpZHRoIiwiaGVpZ2h0IiwiYm9yZGVyUmFkaXVzIiwiYmFja2dyb3VuZCIsImJvcmRlciIsInBhZGRpbmciLCJkaXNwbGF5IiwiYWxpZ25JdGVtcyIsImp1c3RpZnlDb250ZW50IiwibG9nb1N0eWxlIiwiYmFja2dyb3VuZFNpemUiLCJiYWNrZ3JvdW5kUmVwZWF0IiwiYmFja2dyb3VuZFBvc2l0aW9uIiwiYmFja2dyb3VuZENvbG9yIiwib3ZlcmZsb3ciLCJjbGllbnRJdGVtU3R5bGUiLCJ0ZXh0TGVuZ3RoIiwidGV4dFdpZHRoIiwiTWF0aCIsIm1heCIsInRvdGFsV2lkdGgiLCJwYXJzZUludCIsImdhcCIsImZsZXhTaHJpbmsiLCJtYXJnaW5SaWdodCIsImNvbXBhbnlOYW1lU3R5bGUiLCJjb2xvciIsImZvbnRTaXplIiwiZm9udFdlaWdodCIsImZvbnRGYW1pbHkiLCJ3aGl0ZVNwYWNlIiwidGV4dE92ZXJmbG93IiwiZmxleCIsImNvbnRhaW5lclN0eWxlIiwidHJhY2tTdHlsZSIsImlzRHJhZ2dpbmciLCJzY3JvbGxMZWZ0IiwidHJhbnNpdGlvbiIsInRyYW5zZm9ybSIsIkNsaWVudExvZ28iLCJsb2dvdXJsIiwiZGl2Iiwic3R5bGUiLCJiYWNrZ3JvdW5kSW1hZ2UiLCJDbGllbnRJdGVtIiwiY2xpZW50IiwiaW5kZXgiLCJjb21wYW55bmFtZSIsImxlbmd0aCIsImlkIiwiY2FsY3VsYXRlQ2xpZW50V2lkdGgiLCJjYWxjdWxhdGVUb3RhbFdpZHRoIiwiY2xpZW50cyIsInJlZHVjZSIsInN1bSIsIkhvbWVNYWluSGVybyIsInNldENsaWVudHMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInNldElzRHJhZ2dpbmciLCJzdGFydFgiLCJzZXRTdGFydFgiLCJzZXRTY3JvbGxMZWZ0IiwiYXV0b1Njcm9sbEludGVydmFsIiwic2V0QXV0b1Njcm9sbEludGVydmFsIiwiZmV0Y2hDbGllbnRzIiwicmVzcG9uc2UiLCJmZXRjaCIsImRhdGEiLCJqc29uIiwic3VjY2VzcyIsInNvcnRlZENsaWVudHMiLCJzb3J0IiwiYSIsImIiLCJsb2NhbGVDb21wYXJlIiwiZmlyc3RDbGllbnRXaWR0aCIsImVycm9yIiwiY29uc29sZSIsImN1cnJlbnRDbGllbnRJbmRleCIsImludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJwcmV2U2Nyb2xsTGVmdCIsImN1cnJlbnRDbGllbnRXaWR0aCIsIm5ld1Njcm9sbExlZnQiLCJjbGVhckludGVydmFsIiwiaGFuZGxlTW91c2VEb3duIiwiZSIsInBhZ2VYIiwiaGFuZGxlTW91c2VNb3ZlIiwicHJldmVudERlZmF1bHQiLCJ4Iiwid2FsayIsImhhbmRsZU1vdXNlVXAiLCJoYW5kbGVNb3VzZUxlYXZlIiwiaGFuZGxlVG91Y2hTdGFydCIsInRvdWNoZXMiLCJoYW5kbGVUb3VjaE1vdmUiLCJoYW5kbGVUb3VjaEVuZCIsInJlbmRlckNsaWVudEl0ZW1zIiwibWFwIiwiY2xhc3NOYW1lIiwiaDMiLCJoMSIsImRhdGEtd293LWRlbGF5IiwiZGF0YS1jdXJzb3IiLCJzcGFuIiwicCIsImhyZWYiLCJjdXJzb3IiLCJvbk1vdXNlRG93biIsIm9uTW91c2VNb3ZlIiwib25Nb3VzZVVwIiwib25Nb3VzZUxlYXZlIiwib25Ub3VjaFN0YXJ0Iiwib25Ub3VjaE1vdmUiLCJvblRvdWNoRW5kIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/home/<USER>/HomeMainHero.tsx\n"));

/***/ })

});