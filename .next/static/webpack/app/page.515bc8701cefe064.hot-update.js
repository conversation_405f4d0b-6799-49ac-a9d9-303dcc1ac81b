"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/main/home/<USER>/HomeImageHero.tsx":
/*!*********************************************************!*\
  !*** ./src/components/main/home/<USER>/HomeImageHero.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst HomeImageHero = ()=>{\n    _s();\n    const [bgImage, setBgImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('/images/hero-bg.jpg'); // Default fallback\n    const [clients, setClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [clientsLoading, setClientsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startX, setStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [scrollLeft, setScrollLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [autoScrollInterval, setAutoScrollInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Style constants\n    const LOGO_SIZE = '40px';\n    const DRAG_SENSITIVITY = 0.4;\n    const TEXT_WIDTH_MULTIPLIER = 10;\n    const MIN_TEXT_WIDTH = 60;\n    const TEXT_PADDING = 20;\n    const LOGO_GAP = 0;\n    // Helper function to calculate client width\n    const calculateClientWidth = (textLength)=>{\n        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n        return parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n    };\n    // Helper function to calculate total width\n    const calculateTotalWidth = (clients)=>{\n        return clients.reduce((sum, client)=>{\n            return sum + calculateClientWidth(client.companyname.length);\n        }, 0);\n    };\n    // Logo component\n    const ClientLogo = (param)=>{\n        let { logourl } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                width: LOGO_SIZE,\n                height: LOGO_SIZE,\n                borderRadius: '50%',\n                backgroundImage: \"url(\".concat(logourl || \"/images/icon-testimonial-logo.svg\", \")\"),\n                backgroundSize: '70%',\n                backgroundRepeat: 'no-repeat',\n                backgroundPosition: 'center',\n                backgroundColor: 'transparent',\n                overflow: 'hidden',\n                background: 'linear-gradient(transparent, transparent) padding-box, linear-gradient(to left, var(--accent-color), var(--accent-secondary-color)) border-box',\n                border: '1px solid transparent',\n                position: 'relative'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'absolute',\n                    top: 0,\n                    bottom: 0,\n                    left: '-15%',\n                    right: 0,\n                    width: 0,\n                    height: '106%',\n                    background: 'linear-gradient(to left, var(--accent-color) 0%, var(--accent-secondary-color) 100%)',\n                    transform: 'skew(45deg)',\n                    transition: 'all 0.4s ease-in-out',\n                    zIndex: -1\n                }\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n            lineNumber: 35,\n            columnNumber: 5\n        }, undefined);\n    };\n    // Client item component\n    const ClientItem = (param)=>{\n        let { client, index } = param;\n        const textLength = client.companyname.length;\n        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n        const totalWidth = parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: \"\".concat(LOGO_GAP, \"px\"),\n                width: \"\".concat(totalWidth, \"px\"),\n                flexShrink: 0,\n                marginRight: '0px',\n                overflow: 'hidden'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {\n                    logourl: client.logourl\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        color: 'white',\n                        fontSize: '18px',\n                        fontWeight: 'normal',\n                        fontFamily: 'sans-serif',\n                        whiteSpace: 'nowrap',\n                        overflow: 'hidden',\n                        textOverflow: 'ellipsis',\n                        flex: 1\n                    },\n                    children: client.companyname\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, client.id || index, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, undefined);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeImageHero.useEffect\": ()=>{\n            const fetchHeroImage = {\n                \"HomeImageHero.useEffect.fetchHeroImage\": async ()=>{\n                    try {\n                        const response = await fetch('/api/admin/settings/hero-media');\n                        if (response.ok) {\n                            var _data_media;\n                            const data = await response.json();\n                            if (data.success && ((_data_media = data.media) === null || _data_media === void 0 ? void 0 : _data_media.hero_main_bg_image)) {\n                                setBgImage(data.media.hero_main_bg_image);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error fetching hero image:', error);\n                    // Keep default image\n                    }\n                }\n            }[\"HomeImageHero.useEffect.fetchHeroImage\"];\n            fetchHeroImage();\n        }\n    }[\"HomeImageHero.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeImageHero.useEffect\": ()=>{\n            const fetchClients = {\n                \"HomeImageHero.useEffect.fetchClients\": async ()=>{\n                    try {\n                        setClientsLoading(true);\n                        const response = await fetch('/api/clients');\n                        const data = await response.json();\n                        if (data.success) {\n                            setClients(data.clients || []);\n                            // Set initial scroll position to hide first client\n                            if (data.clients && data.clients.length > 0) {\n                                const firstClientWidth = calculateClientWidth(data.clients[0].companyname.length);\n                                setScrollLeft(firstClientWidth);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error fetching clients:', error);\n                    } finally{\n                        setClientsLoading(false);\n                    }\n                }\n            }[\"HomeImageHero.useEffect.fetchClients\"];\n            fetchClients();\n        }\n    }[\"HomeImageHero.useEffect\"], []);\n    // Auto-scroll function\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeImageHero.useEffect\": ()=>{\n            if (clients.length === 0 || isDragging) return;\n            const interval = setInterval({\n                \"HomeImageHero.useEffect.interval\": ()=>{\n                    setScrollLeft({\n                        \"HomeImageHero.useEffect.interval\": (prevScrollLeft)=>{\n                            const totalWidth = calculateTotalWidth(clients);\n                            const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                            let newScrollLeft = prevScrollLeft + firstClientWidth;\n                            if (newScrollLeft >= totalWidth) {\n                                newScrollLeft = firstClientWidth;\n                            }\n                            return newScrollLeft;\n                        }\n                    }[\"HomeImageHero.useEffect.interval\"]);\n                }\n            }[\"HomeImageHero.useEffect.interval\"], 5000);\n            setAutoScrollInterval(interval);\n            return ({\n                \"HomeImageHero.useEffect\": ()=>{\n                    if (interval) {\n                        clearInterval(interval);\n                    }\n                }\n            })[\"HomeImageHero.useEffect\"];\n        }\n    }[\"HomeImageHero.useEffect\"], [\n        clients.length,\n        isDragging\n    ]);\n    // Cleanup interval on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeImageHero.useEffect\": ()=>{\n            return ({\n                \"HomeImageHero.useEffect\": ()=>{\n                    if (autoScrollInterval) {\n                        clearInterval(autoScrollInterval);\n                    }\n                }\n            })[\"HomeImageHero.useEffect\"];\n        }\n    }[\"HomeImageHero.useEffect\"], [\n        autoScrollInterval\n    ]);\n    const handleMouseDown = (e)=>{\n        setIsDragging(true);\n        setStartX(e.pageX);\n        // Stop auto-scroll when user starts dragging\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops dragging\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    const totalWidth = calculateTotalWidth(clients);\n                    const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                    let newScrollLeft = prevScrollLeft + firstClientWidth;\n                    if (newScrollLeft >= totalWidth) {\n                        newScrollLeft = firstClientWidth;\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const handleMouseLeave = ()=>{\n        setIsDragging(false);\n    };\n    const handleTouchStart = (e)=>{\n        setIsDragging(true);\n        setStartX(e.touches[0].pageX);\n        // Stop auto-scroll when user starts touching\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleTouchMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.touches[0].pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleTouchEnd = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops touching\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    const totalWidth = calculateTotalWidth(clients);\n                    const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                    let newScrollLeft = prevScrollLeft + firstClientWidth;\n                    if (newScrollLeft >= totalWidth) {\n                        newScrollLeft = firstClientWidth;\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const renderClientItems = ()=>{\n        if (clientsLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0px',\n                    width: '140px',\n                    flexShrink: 0,\n                    marginRight: '0px',\n                    overflow: 'hidden'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            fontSize: '26px',\n                            fontWeight: 'bold',\n                            fontFamily: 'sans-serif',\n                            whiteSpace: 'nowrap',\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            flex: 1\n                        },\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                lineNumber: 294,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (clients.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0px',\n                    width: '200px',\n                    flexShrink: 0,\n                    marginRight: '0px',\n                    overflow: 'hidden'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            fontSize: '26px',\n                            fontWeight: 'bold',\n                            fontFamily: 'sans-serif',\n                            whiteSpace: 'nowrap',\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            flex: 1\n                        },\n                        children: \"No clients yet\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                lineNumber: 322,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, client.id || index, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, undefined)),\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, \"duplicate-\".concat(client.id || index), false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 11\n                    }, undefined))\n            ]\n        }, void 0, true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"hero hero-bg-image parallaxie\",\n            style: {\n                backgroundImage: \"url(\".concat(bgImage, \")\"),\n                backgroundSize: 'cover',\n                backgroundPosition: 'center',\n                backgroundRepeat: 'no-repeat'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row align-items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-lg-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hero-content\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"section-title section-title-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"wow fadeInUp\",\n                                                children: \"Enterprise-grade software & web development solutions\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"wow fadeInUp\",\n                                                \"data-wow-delay\": \"0.2s\",\n                                                \"data-cursor\": \"-opaque\",\n                                                children: [\n                                                    \"Advanced digital solutions that\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"accelerate your growth\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"wow fadeInUp\",\n                                                \"data-wow-delay\": \"0.4s\",\n                                                children: \"Transform your vision into scalable applications and sophisticated web platforms. Professional development services that deliver measurable results.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hero-btn wow fadeInUp\",\n                                        \"data-wow-delay\": \"0.6s\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/main/contact\",\n                                                className: \"btn-default btn-highlighted\",\n                                                children: \"Get Free Assessment\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/main/services\",\n                                                className: \"btn-default\",\n                                                children: \"View our services\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                            lineNumber: 375,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-lg-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hero-company-slider\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"We're Trusted by more than \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"counter\",\n                                                children: clients.length\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 46\n                                            }, undefined),\n                                            \"+ companies\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"swiper\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"swiper-wrapper\",\n                                            children: clientsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"swiper-slide\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"company-logo\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            color: 'white',\n                                                            fontSize: '14px'\n                                                        },\n                                                        children: \"Loading...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 23\n                                            }, undefined) : clients.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"swiper-slide\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"company-logo\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            color: 'white',\n                                                            fontSize: '14px'\n                                                        },\n                                                        children: \"No clients available\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 23\n                                            }, undefined) : clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"swiper-slide\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"company-logo\",\n                                                        style: {\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'flex-start',\n                                                            gap: '12px',\n                                                            padding: '10px',\n                                                            minWidth: '200px',\n                                                            whiteSpace: 'nowrap'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '70px',\n                                                                    height: '70px',\n                                                                    borderRadius: '50%',\n                                                                    overflow: 'hidden',\n                                                                    display: 'flex',\n                                                                    alignItems: 'center',\n                                                                    justifyContent: 'center',\n                                                                    flexShrink: 0,\n                                                                    backgroundColor: 'transparent'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: client.logourl || \"/images/icon-testimonial-logo.svg\",\n                                                                    alt: client.companyname,\n                                                                    style: {\n                                                                        width: '100%',\n                                                                        height: '100%',\n                                                                        objectFit: 'cover',\n                                                                        borderRadius: '50%'\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                                    lineNumber: 456,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    color: 'white',\n                                                                    fontSize: '26px',\n                                                                    fontWeight: 'bold',\n                                                                    fontFamily: 'sans-serif',\n                                                                    whiteSpace: 'nowrap',\n                                                                    overflow: 'hidden',\n                                                                    textOverflow: 'ellipsis',\n                                                                    maxWidth: '150px'\n                                                                },\n                                                                children: client.companyname\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, client.id || index, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 25\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 411,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                lineNumber: 373,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n            lineNumber: 364,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(HomeImageHero, \"vtuUfRQdZAFf1EVKjY94RogV8N0=\");\n_c = HomeImageHero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeImageHero);\nvar _c;\n$RefreshReg$(_c, \"HomeImageHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/home/<USER>/HomeImageHero.tsx\n"));

/***/ })

});