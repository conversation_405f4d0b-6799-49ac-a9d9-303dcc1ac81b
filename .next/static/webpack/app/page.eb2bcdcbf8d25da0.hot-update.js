"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/main/home/<USER>/HomeImageHero.tsx":
/*!*********************************************************!*\
  !*** ./src/components/main/home/<USER>/HomeImageHero.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst HomeImageHero = ()=>{\n    _s();\n    const [bgImage, setBgImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('/images/hero-bg.jpg'); // Default fallback\n    const [clients, setClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [clientsLoading, setClientsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startX, setStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentClientIndex, setCurrentClientIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // Track which client is currently visible\n    const [scrollLeft, setScrollLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // Start at beginning\n    const [autoScrollInterval, setAutoScrollInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Style constants\n    const LOGO_SIZE = '60px'; // Increased from 40px to 60px\n    const DRAG_SENSITIVITY = 0.4;\n    const TEXT_WIDTH_MULTIPLIER = 10;\n    const MIN_TEXT_WIDTH = 60;\n    const TEXT_PADDING = 5; // Reduced from 20 to 5\n    const LOGO_GAP = 0; // No gap between logo and client name\n    // Helper function to calculate client width\n    const calculateClientWidth = (textLength)=>{\n        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n        return parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n    };\n    // Helper function to calculate total width\n    const calculateTotalWidth = (clients)=>{\n        return clients.reduce((sum, client)=>{\n            return sum + calculateClientWidth(client.companyname.length);\n        }, 0);\n    };\n    // Helper function to calculate scroll position for a specific client index\n    const calculateScrollPositionForClient = (clients, targetIndex)=>{\n        let scrollPosition = 0;\n        for(let i = 0; i < targetIndex && i < clients.length; i++){\n            scrollPosition += calculateClientWidth(clients[i].companyname.length);\n        }\n        return scrollPosition;\n    };\n    const logoContainerStyle = {\n        width: LOGO_SIZE,\n        height: LOGO_SIZE,\n        borderRadius: '50%',\n        background: 'linear-gradient(var(--bg-color), var(--bg-color)) padding-box, linear-gradient(to left, var(--accent-color), var(--accent-secondary-color)) border-box',\n        border: '1px solid transparent',\n        padding: '2px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n    };\n    const logoStyle = {\n        width: 'calc(100% - 4px)',\n        height: 'calc(100% - 4px)',\n        borderRadius: '50%',\n        backgroundSize: 'cover',\n        backgroundRepeat: 'no-repeat',\n        backgroundPosition: 'center',\n        backgroundColor: 'transparent',\n        overflow: 'hidden'\n    };\n    // Logo component\n    const ClientLogo = (param)=>{\n        let { logourl } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: logoContainerStyle,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    ...logoStyle,\n                    backgroundImage: \"url(\".concat(logourl || \"/images/icon-testimonial-logo.svg\", \")\")\n                }\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n            lineNumber: 74,\n            columnNumber: 5\n        }, undefined);\n    };\n    const clientItemStyle = (textLength)=>{\n        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n        const totalWidth = parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n        return {\n            display: 'flex',\n            alignItems: 'center',\n            gap: \"\".concat(LOGO_GAP, \"px\"),\n            width: \"\".concat(totalWidth, \"px\"),\n            flexShrink: 0,\n            marginRight: '0px',\n            overflow: 'hidden'\n        };\n    };\n    const companyNameStyle = {\n        color: 'white',\n        fontSize: '18px',\n        fontWeight: 'normal',\n        fontFamily: 'sans-serif',\n        whiteSpace: 'nowrap',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n        flex: 1\n    };\n    const containerStyle = {\n        overflow: 'hidden',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '20px' // Consistent gap between all client blocks\n    };\n    // Client item component\n    const ClientItem = (param)=>{\n        let { client, index } = param;\n        const textLength = client.companyname.length;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: clientItemStyle(textLength),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {\n                    logourl: client.logourl\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: companyNameStyle,\n                    children: client.companyname\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, client.id || index, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, undefined);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeImageHero.useEffect\": ()=>{\n            const fetchHeroImage = {\n                \"HomeImageHero.useEffect.fetchHeroImage\": async ()=>{\n                    try {\n                        const response = await fetch('/api/admin/settings/hero-media');\n                        if (response.ok) {\n                            var _data_media;\n                            const data = await response.json();\n                            if (data.success && ((_data_media = data.media) === null || _data_media === void 0 ? void 0 : _data_media.hero_main_bg_image)) {\n                                setBgImage(data.media.hero_main_bg_image);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error fetching hero image:', error);\n                    // Keep default image\n                    }\n                }\n            }[\"HomeImageHero.useEffect.fetchHeroImage\"];\n            fetchHeroImage();\n        }\n    }[\"HomeImageHero.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeImageHero.useEffect\": ()=>{\n            const fetchClients = {\n                \"HomeImageHero.useEffect.fetchClients\": async ()=>{\n                    try {\n                        setClientsLoading(true);\n                        // Fetch all active clients without limit, sorted by name\n                        const response = await fetch('/api/clients?limit=0');\n                        const data = await response.json();\n                        if (data.success) {\n                            // Sort clients by company name to ensure alphabetical order\n                            const sortedClients = (data.clients || []).sort({\n                                \"HomeImageHero.useEffect.fetchClients.sortedClients\": (a, b)=>a.companyname.localeCompare(b.companyname)\n                            }[\"HomeImageHero.useEffect.fetchClients.sortedClients\"]);\n                            setClients(sortedClients);\n                            // Start with the first client fully visible\n                            setCurrentClientIndex(0);\n                            setScrollLeft(0);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching clients:', error);\n                    } finally{\n                        setClientsLoading(false);\n                    }\n                }\n            }[\"HomeImageHero.useEffect.fetchClients\"];\n            fetchClients();\n        }\n    }[\"HomeImageHero.useEffect\"], []);\n    // Auto-scroll function\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeImageHero.useEffect\": ()=>{\n            if (clients.length === 0 || isDragging) return;\n            const interval = setInterval({\n                \"HomeImageHero.useEffect.interval\": ()=>{\n                    setScrollLeft({\n                        \"HomeImageHero.useEffect.interval\": (prevScrollLeft)=>{\n                            const totalWidth = calculateTotalWidth(clients);\n                            const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                            let newScrollLeft = prevScrollLeft + firstClientWidth;\n                            if (newScrollLeft >= totalWidth) {\n                                newScrollLeft = firstClientWidth;\n                            }\n                            return newScrollLeft;\n                        }\n                    }[\"HomeImageHero.useEffect.interval\"]);\n                }\n            }[\"HomeImageHero.useEffect.interval\"], 5000);\n            setAutoScrollInterval(interval);\n            return ({\n                \"HomeImageHero.useEffect\": ()=>{\n                    if (interval) {\n                        clearInterval(interval);\n                    }\n                }\n            })[\"HomeImageHero.useEffect\"];\n        }\n    }[\"HomeImageHero.useEffect\"], [\n        clients.length,\n        isDragging\n    ]);\n    // Cleanup interval on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeImageHero.useEffect\": ()=>{\n            return ({\n                \"HomeImageHero.useEffect\": ()=>{\n                    if (autoScrollInterval) {\n                        clearInterval(autoScrollInterval);\n                    }\n                }\n            })[\"HomeImageHero.useEffect\"];\n        }\n    }[\"HomeImageHero.useEffect\"], [\n        autoScrollInterval\n    ]);\n    const handleMouseDown = (e)=>{\n        setIsDragging(true);\n        setStartX(e.pageX);\n        // Stop auto-scroll when user starts dragging\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops dragging\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    const totalWidth = calculateTotalWidth(clients);\n                    const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                    let newScrollLeft = prevScrollLeft + firstClientWidth;\n                    if (newScrollLeft >= totalWidth) {\n                        newScrollLeft = firstClientWidth;\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const handleMouseLeave = ()=>{\n        setIsDragging(false);\n    };\n    const handleTouchStart = (e)=>{\n        setIsDragging(true);\n        setStartX(e.touches[0].pageX);\n        // Stop auto-scroll when user starts touching\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleTouchMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.touches[0].pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleTouchEnd = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops touching\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    const totalWidth = calculateTotalWidth(clients);\n                    const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                    let newScrollLeft = prevScrollLeft + firstClientWidth;\n                    if (newScrollLeft >= totalWidth) {\n                        newScrollLeft = firstClientWidth;\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const renderClientItems = ()=>{\n        if (clientsLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0px',\n                    width: '140px',\n                    flexShrink: 0,\n                    marginRight: '0px',\n                    overflow: 'hidden'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            fontSize: '26px',\n                            fontWeight: 'bold',\n                            fontFamily: 'sans-serif',\n                            whiteSpace: 'nowrap',\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            flex: 1\n                        },\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                lineNumber: 327,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (clients.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0px',\n                    width: '200px',\n                    flexShrink: 0,\n                    marginRight: '0px',\n                    overflow: 'hidden'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            fontSize: '26px',\n                            fontWeight: 'bold',\n                            fontFamily: 'sans-serif',\n                            whiteSpace: 'nowrap',\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            flex: 1\n                        },\n                        children: \"No clients yet\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                lineNumber: 355,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, client.id || index, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 384,\n                        columnNumber: 11\n                    }, undefined)),\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, \"duplicate-\".concat(client.id || index), false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 11\n                    }, undefined))\n            ]\n        }, void 0, true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"hero hero-bg-image parallaxie\",\n            style: {\n                backgroundImage: \"url(\".concat(bgImage, \")\"),\n                backgroundSize: 'cover',\n                backgroundPosition: 'center',\n                backgroundRepeat: 'no-repeat'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row align-items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-lg-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hero-content\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"section-title section-title-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"wow fadeInUp\",\n                                                children: \"Enterprise-grade software & web development solutions\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"wow fadeInUp\",\n                                                \"data-wow-delay\": \"0.2s\",\n                                                \"data-cursor\": \"-opaque\",\n                                                children: [\n                                                    \"Advanced digital solutions that\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"accelerate your growth\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"wow fadeInUp\",\n                                                \"data-wow-delay\": \"0.4s\",\n                                                children: \"Transform your vision into scalable applications and sophisticated web platforms. Professional development services that deliver measurable results.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hero-btn wow fadeInUp\",\n                                        \"data-wow-delay\": \"0.6s\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/main/contact\",\n                                                className: \"btn-default btn-highlighted\",\n                                                children: \"Get Free Assessment\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/main/services\",\n                                                className: \"btn-default\",\n                                                children: \"View our services\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                            lineNumber: 408,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 407,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-lg-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hero-company-slider\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"We're Trusted by more than \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"counter\",\n                                                children: clients.length\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 46\n                                            }, undefined),\n                                            \"+ companies\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            overflow: 'hidden',\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0px',\n                                            position: 'relative',\n                                            cursor: isDragging ? 'grabbing' : 'grab'\n                                        },\n                                        onMouseDown: handleMouseDown,\n                                        onMouseMove: handleMouseMove,\n                                        onMouseUp: handleMouseUp,\n                                        onMouseLeave: handleMouseLeave,\n                                        onTouchStart: handleTouchStart,\n                                        onTouchMove: handleTouchMove,\n                                        onTouchEnd: handleTouchEnd,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0px',\n                                                transition: isDragging ? 'none' : 'transform 2s ease-in-out',\n                                                transform: \"translateX(-\".concat(scrollLeft, \"px)\")\n                                            },\n                                            children: renderClientItems()\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                lineNumber: 406,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n            lineNumber: 397,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(HomeImageHero, \"+2TM6P9b2hp+5LFvgcsIf+koDe8=\");\n_c = HomeImageHero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeImageHero);\nvar _c;\n$RefreshReg$(_c, \"HomeImageHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/home/<USER>/HomeImageHero.tsx\n"));

/***/ })

});