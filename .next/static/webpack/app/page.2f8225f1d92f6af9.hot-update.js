"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/main/home/<USER>/HomeImageHero.tsx":
/*!*********************************************************!*\
  !*** ./src/components/main/home/<USER>/HomeImageHero.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst HomeImageHero = ()=>{\n    _s();\n    const [bgImage, setBgImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('/images/hero-bg.jpg'); // Default fallback\n    const [clients, setClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [clientsLoading, setClientsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startX, setStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentClientIndex, setCurrentClientIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // Track which client is currently visible\n    const [scrollLeft, setScrollLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // Start at beginning\n    const [autoScrollInterval, setAutoScrollInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Style constants\n    const LOGO_SIZE = '60px'; // Increased from 40px to 60px\n    const DRAG_SENSITIVITY = 0.4;\n    const TEXT_WIDTH_MULTIPLIER = 10;\n    const MIN_TEXT_WIDTH = 60;\n    const TEXT_PADDING = 5; // Reduced from 20 to 5\n    const LOGO_GAP = 0; // No gap between logo and client name\n    // Helper function to calculate client width\n    const calculateClientWidth = (textLength)=>{\n        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n        return parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n    };\n    // Helper function to calculate total width\n    const calculateTotalWidth = (clients)=>{\n        return clients.reduce((sum, client)=>{\n            return sum + calculateClientWidth(client.companyname.length);\n        }, 0);\n    };\n    // Logo component\n    const ClientLogo = (param)=>{\n        let { logourl } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                width: LOGO_SIZE,\n                height: LOGO_SIZE,\n                borderRadius: '50%',\n                backgroundImage: \"url(\".concat(logourl || \"/images/icon-testimonial-logo.svg\", \")\"),\n                backgroundSize: '70%',\n                backgroundRepeat: 'no-repeat',\n                backgroundPosition: 'center',\n                backgroundColor: 'transparent',\n                overflow: 'hidden',\n                background: 'linear-gradient(transparent, transparent) padding-box, linear-gradient(to left, var(--accent-color), var(--accent-secondary-color)) border-box',\n                border: '1px solid transparent',\n                position: 'relative'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'absolute',\n                    top: 0,\n                    bottom: 0,\n                    left: '-15%',\n                    right: 0,\n                    width: 0,\n                    height: '106%',\n                    background: 'linear-gradient(to left, var(--accent-color) 0%, var(--accent-secondary-color) 100%)',\n                    transform: 'skew(45deg)',\n                    transition: 'all 0.4s ease-in-out',\n                    zIndex: -1\n                }\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n            lineNumber: 42,\n            columnNumber: 5\n        }, undefined);\n    };\n    // Client item component\n    const ClientItem = (param)=>{\n        let { client, index } = param;\n        const textLength = client.companyname.length;\n        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n        const totalWidth = parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: \"\".concat(LOGO_GAP, \"px\"),\n                width: \"\".concat(totalWidth, \"px\"),\n                flexShrink: 0,\n                marginRight: '0px',\n                overflow: 'hidden'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {\n                    logourl: client.logourl\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        color: 'white',\n                        fontSize: '18px',\n                        fontWeight: 'normal',\n                        fontFamily: 'sans-serif',\n                        whiteSpace: 'nowrap',\n                        overflow: 'hidden',\n                        textOverflow: 'ellipsis',\n                        flex: 1\n                    },\n                    children: client.companyname\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, client.id || index, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, undefined);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeImageHero.useEffect\": ()=>{\n            const fetchHeroImage = {\n                \"HomeImageHero.useEffect.fetchHeroImage\": async ()=>{\n                    try {\n                        const response = await fetch('/api/admin/settings/hero-media');\n                        if (response.ok) {\n                            var _data_media;\n                            const data = await response.json();\n                            if (data.success && ((_data_media = data.media) === null || _data_media === void 0 ? void 0 : _data_media.hero_main_bg_image)) {\n                                setBgImage(data.media.hero_main_bg_image);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error fetching hero image:', error);\n                    // Keep default image\n                    }\n                }\n            }[\"HomeImageHero.useEffect.fetchHeroImage\"];\n            fetchHeroImage();\n        }\n    }[\"HomeImageHero.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeImageHero.useEffect\": ()=>{\n            const fetchClients = {\n                \"HomeImageHero.useEffect.fetchClients\": async ()=>{\n                    try {\n                        setClientsLoading(true);\n                        const response = await fetch('/api/clients');\n                        const data = await response.json();\n                        if (data.success) {\n                            setClients(data.clients || []);\n                            // Set initial scroll position to hide first client\n                            if (data.clients && data.clients.length > 0) {\n                                const firstClientWidth = calculateClientWidth(data.clients[0].companyname.length);\n                                setScrollLeft(firstClientWidth);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error fetching clients:', error);\n                    } finally{\n                        setClientsLoading(false);\n                    }\n                }\n            }[\"HomeImageHero.useEffect.fetchClients\"];\n            fetchClients();\n        }\n    }[\"HomeImageHero.useEffect\"], []);\n    // Auto-scroll function\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeImageHero.useEffect\": ()=>{\n            if (clients.length === 0 || isDragging) return;\n            const interval = setInterval({\n                \"HomeImageHero.useEffect.interval\": ()=>{\n                    setScrollLeft({\n                        \"HomeImageHero.useEffect.interval\": (prevScrollLeft)=>{\n                            const totalWidth = calculateTotalWidth(clients);\n                            const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                            let newScrollLeft = prevScrollLeft + firstClientWidth;\n                            if (newScrollLeft >= totalWidth) {\n                                newScrollLeft = firstClientWidth;\n                            }\n                            return newScrollLeft;\n                        }\n                    }[\"HomeImageHero.useEffect.interval\"]);\n                }\n            }[\"HomeImageHero.useEffect.interval\"], 5000);\n            setAutoScrollInterval(interval);\n            return ({\n                \"HomeImageHero.useEffect\": ()=>{\n                    if (interval) {\n                        clearInterval(interval);\n                    }\n                }\n            })[\"HomeImageHero.useEffect\"];\n        }\n    }[\"HomeImageHero.useEffect\"], [\n        clients.length,\n        isDragging\n    ]);\n    // Cleanup interval on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeImageHero.useEffect\": ()=>{\n            return ({\n                \"HomeImageHero.useEffect\": ()=>{\n                    if (autoScrollInterval) {\n                        clearInterval(autoScrollInterval);\n                    }\n                }\n            })[\"HomeImageHero.useEffect\"];\n        }\n    }[\"HomeImageHero.useEffect\"], [\n        autoScrollInterval\n    ]);\n    const handleMouseDown = (e)=>{\n        setIsDragging(true);\n        setStartX(e.pageX);\n        // Stop auto-scroll when user starts dragging\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops dragging\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    const totalWidth = calculateTotalWidth(clients);\n                    const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                    let newScrollLeft = prevScrollLeft + firstClientWidth;\n                    if (newScrollLeft >= totalWidth) {\n                        newScrollLeft = firstClientWidth;\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const handleMouseLeave = ()=>{\n        setIsDragging(false);\n    };\n    const handleTouchStart = (e)=>{\n        setIsDragging(true);\n        setStartX(e.touches[0].pageX);\n        // Stop auto-scroll when user starts touching\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleTouchMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.touches[0].pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleTouchEnd = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops touching\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    const totalWidth = calculateTotalWidth(clients);\n                    const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                    let newScrollLeft = prevScrollLeft + firstClientWidth;\n                    if (newScrollLeft >= totalWidth) {\n                        newScrollLeft = firstClientWidth;\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const renderClientItems = ()=>{\n        if (clientsLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0px',\n                    width: '140px',\n                    flexShrink: 0,\n                    marginRight: '0px',\n                    overflow: 'hidden'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            fontSize: '26px',\n                            fontWeight: 'bold',\n                            fontFamily: 'sans-serif',\n                            whiteSpace: 'nowrap',\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            flex: 1\n                        },\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                lineNumber: 301,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (clients.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0px',\n                    width: '200px',\n                    flexShrink: 0,\n                    marginRight: '0px',\n                    overflow: 'hidden'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            fontSize: '26px',\n                            fontWeight: 'bold',\n                            fontFamily: 'sans-serif',\n                            whiteSpace: 'nowrap',\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            flex: 1\n                        },\n                        children: \"No clients yet\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                lineNumber: 329,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, client.id || index, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 11\n                    }, undefined)),\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, \"duplicate-\".concat(client.id || index), false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 11\n                    }, undefined))\n            ]\n        }, void 0, true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"hero hero-bg-image parallaxie\",\n            style: {\n                backgroundImage: \"url(\".concat(bgImage, \")\"),\n                backgroundSize: 'cover',\n                backgroundPosition: 'center',\n                backgroundRepeat: 'no-repeat'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row align-items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-lg-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hero-content\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"section-title section-title-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"wow fadeInUp\",\n                                                children: \"Enterprise-grade software & web development solutions\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"wow fadeInUp\",\n                                                \"data-wow-delay\": \"0.2s\",\n                                                \"data-cursor\": \"-opaque\",\n                                                children: [\n                                                    \"Advanced digital solutions that\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"accelerate your growth\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"wow fadeInUp\",\n                                                \"data-wow-delay\": \"0.4s\",\n                                                children: \"Transform your vision into scalable applications and sophisticated web platforms. Professional development services that deliver measurable results.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hero-btn wow fadeInUp\",\n                                        \"data-wow-delay\": \"0.6s\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/main/contact\",\n                                                className: \"btn-default btn-highlighted\",\n                                                children: \"Get Free Assessment\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/main/services\",\n                                                className: \"btn-default\",\n                                                children: \"View our services\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-lg-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hero-company-slider\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"We're Trusted by more than \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"counter\",\n                                                children: clients.length\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 46\n                                            }, undefined),\n                                            \"+ companies\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            overflow: 'hidden',\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0px',\n                                            position: 'relative',\n                                            cursor: isDragging ? 'grabbing' : 'grab'\n                                        },\n                                        onMouseDown: handleMouseDown,\n                                        onMouseMove: handleMouseMove,\n                                        onMouseUp: handleMouseUp,\n                                        onMouseLeave: handleMouseLeave,\n                                        onTouchStart: handleTouchStart,\n                                        onTouchMove: handleTouchMove,\n                                        onTouchEnd: handleTouchEnd,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0px',\n                                                transition: isDragging ? 'none' : 'transform 2s ease-in-out',\n                                                transform: \"translateX(-\".concat(scrollLeft, \"px)\")\n                                            },\n                                            children: renderClientItems()\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n                lineNumber: 380,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeImageHero.tsx\",\n            lineNumber: 371,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(HomeImageHero, \"+2TM6P9b2hp+5LFvgcsIf+koDe8=\");\n_c = HomeImageHero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeImageHero);\nvar _c;\n$RefreshReg$(_c, \"HomeImageHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/home/<USER>/HomeImageHero.tsx\n"));

/***/ })

});