"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/main/home/<USER>/HomeMainHero.tsx":
/*!********************************************************!*\
  !*** ./src/components/main/home/<USER>/HomeMainHero.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n// Style constants\nconst LOGO_SIZE = '40px';\nconst DRAG_SENSITIVITY = 0.4;\nconst logoStyle = {\n    width: LOGO_SIZE,\n    height: LOGO_SIZE,\n    borderRadius: '50%',\n    backgroundSize: 'cover',\n    backgroundRepeat: 'no-repeat',\n    backgroundPosition: 'center',\n    backgroundColor: 'transparent',\n    overflow: 'hidden'\n};\nconst clientItemStyle = (textLength)=>{\n    // Calculate width based on text length: logo (40px) + gap (8px) + text width + padding (20px)\n    const textWidth = Math.max(textLength * 10, 60); // Increased multiplier and minimum for better fit\n    const totalWidth = 40 + 8 + textWidth + 20; // logo + gap + text + padding\n    return {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '8px',\n        width: \"\".concat(totalWidth, \"px\"),\n        flexShrink: 0,\n        marginRight: '0px',\n        overflow: 'hidden' // Ensure no overflow\n    };\n};\nconst companyNameStyle = {\n    color: 'white',\n    fontSize: '18px',\n    fontWeight: 'normal',\n    fontFamily: 'sans-serif',\n    whiteSpace: 'nowrap',\n    overflow: 'hidden',\n    textOverflow: 'ellipsis',\n    flex: 1 // Take remaining space\n};\nconst containerStyle = {\n    overflow: 'hidden',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '0px' // No gap between blocks\n};\nconst trackStyle = (isDragging, scrollLeft)=>({\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0px',\n        transition: isDragging ? 'none' : 'transform 0.3s ease',\n        transform: \"translateX(-\".concat(scrollLeft, \"px)\")\n    });\n// Logo component\nconst ClientLogo = (param)=>{\n    let { logourl } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            ...logoStyle,\n            backgroundImage: \"url(\".concat(logourl || \"/images/icon-testimonial-logo.svg\", \")\")\n        }\n    }, void 0, false, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n        lineNumber: 70,\n        columnNumber: 3\n    }, undefined);\n};\n_c = ClientLogo;\n// Client item component\nconst ClientItem = (param)=>{\n    let { client, index } = param;\n    const textLength = client.companyname.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: clientItemStyle(textLength),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {\n                logourl: client.logourl\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: companyNameStyle,\n                children: client.companyname\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, client.id || index, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = ClientItem;\nconst HomeMainHero = ()=>{\n    _s();\n    const [clients, setClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startX, setStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [scrollLeft, setScrollLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(160); // Start with first client fully hidden\n    const [autoScrollInterval, setAutoScrollInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            const fetchClients = {\n                \"HomeMainHero.useEffect.fetchClients\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const response = await fetch('/api/clients');\n                        const data = await response.json();\n                        if (data.success) {\n                            setClients(data.clients || []);\n                            // Set initial scroll position to hide first client\n                            if (data.clients && data.clients.length > 0) {\n                                const firstTextLength = data.clients[0].companyname.length;\n                                const firstTextWidth = Math.max(firstTextLength * 10, 60); // Updated multiplier\n                                const firstClientWidth = 40 + 8 + firstTextWidth + 20;\n                                setScrollLeft(firstClientWidth);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error fetching clients:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"HomeMainHero.useEffect.fetchClients\"];\n            fetchClients();\n        }\n    }[\"HomeMainHero.useEffect\"], []);\n    // Auto-scroll function\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            if (clients.length === 0 || isDragging) return;\n            const interval = setInterval({\n                \"HomeMainHero.useEffect.interval\": ()=>{\n                    // Calculate total width dynamically\n                    const totalWidth = clients.reduce({\n                        \"HomeMainHero.useEffect.interval.totalWidth\": (sum, client)=>{\n                            const textLength = client.companyname.length;\n                            const textWidth = Math.max(textLength * 10, 60); // Updated multiplier\n                            return sum + (40 + 8 + textWidth + 20); // logo + gap + text + padding\n                        }\n                    }[\"HomeMainHero.useEffect.interval.totalWidth\"], 0);\n                    setScrollLeft({\n                        \"HomeMainHero.useEffect.interval\": (prevScrollLeft)=>{\n                            // Find which client we're currently showing\n                            let currentClientIndex = 0;\n                            let accumulatedWidth = 0;\n                            for(let i = 0; i < clients.length; i++){\n                                const textLength = clients[i].companyname.length;\n                                const textWidth = Math.max(textLength * 10, 60); // Updated multiplier\n                                const clientWidth = 40 + 8 + textWidth + 20;\n                                if (accumulatedWidth + clientWidth > prevScrollLeft) {\n                                    currentClientIndex = i;\n                                    break;\n                                }\n                                accumulatedWidth += clientWidth;\n                            }\n                            // Move to next client\n                            const nextClientIndex = (currentClientIndex + 1) % clients.length;\n                            const nextTextLength = clients[nextClientIndex].companyname.length;\n                            const nextTextWidth = Math.max(nextTextLength * 10, 60); // Updated multiplier\n                            const nextClientWidth = 40 + 8 + nextTextWidth + 20;\n                            let newScrollLeft = accumulatedWidth + nextClientWidth;\n                            // Loop back to beginning when reaching the end\n                            if (newScrollLeft >= totalWidth) {\n                                const firstTextLength = clients[0].companyname.length;\n                                const firstTextWidth = Math.max(firstTextLength * 8, 50);\n                                const firstClientWidth = 40 + 8 + firstTextWidth + 20;\n                                newScrollLeft = firstClientWidth; // Start with first client fully hidden\n                            }\n                            return newScrollLeft;\n                        }\n                    }[\"HomeMainHero.useEffect.interval\"]);\n                }\n            }[\"HomeMainHero.useEffect.interval\"], 5000); // Move every 5 seconds\n            setAutoScrollInterval(interval);\n            return ({\n                \"HomeMainHero.useEffect\": ()=>{\n                    if (interval) {\n                        clearInterval(interval);\n                    }\n                }\n            })[\"HomeMainHero.useEffect\"];\n        }\n    }[\"HomeMainHero.useEffect\"], [\n        clients.length,\n        isDragging\n    ]);\n    // Cleanup interval on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            return ({\n                \"HomeMainHero.useEffect\": ()=>{\n                    if (autoScrollInterval) {\n                        clearInterval(autoScrollInterval);\n                    }\n                }\n            })[\"HomeMainHero.useEffect\"];\n        }\n    }[\"HomeMainHero.useEffect\"], [\n        autoScrollInterval\n    ]);\n    const handleMouseDown = (e)=>{\n        setIsDragging(true);\n        setStartX(e.pageX);\n        // Stop auto-scroll when user starts dragging\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        // Calculate the total width dynamically\n        const totalWidth = clients.reduce((sum, client)=>{\n            const textLength = client.companyname.length;\n            const textWidth = Math.max(textLength * 8, 50);\n            return sum + (40 + 8 + textWidth + 20);\n        }, 0);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstTextLength = ((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0;\n            const firstTextWidth = Math.max(firstTextLength * 8, 50);\n            const firstClientWidth = 40 + 8 + firstTextWidth + 20;\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops dragging\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                // Calculate total width dynamically\n                const totalWidth = clients.reduce((sum, client)=>{\n                    const textLength = client.companyname.length;\n                    const textWidth = Math.max(textLength * 10, 60); // Updated multiplier\n                    return sum + (40 + 8 + textWidth + 20);\n                }, 0);\n                setScrollLeft((prevScrollLeft)=>{\n                    // Find which client we're currently showing\n                    let currentClientIndex = 0;\n                    let accumulatedWidth = 0;\n                    for(let i = 0; i < clients.length; i++){\n                        const textLength = clients[i].companyname.length;\n                        const textWidth = Math.max(textLength * 10, 60); // Updated multiplier\n                        const clientWidth = 40 + 8 + textWidth + 20;\n                        if (accumulatedWidth + clientWidth > prevScrollLeft) {\n                            currentClientIndex = i;\n                            break;\n                        }\n                        accumulatedWidth += clientWidth;\n                    }\n                    // Move to next client\n                    const nextClientIndex = (currentClientIndex + 1) % clients.length;\n                    const nextTextLength = clients[nextClientIndex].companyname.length;\n                    const nextTextWidth = Math.max(nextTextLength * 10, 60); // Updated multiplier\n                    const nextClientWidth = 40 + 8 + nextTextWidth + 20;\n                    let newScrollLeft = accumulatedWidth + nextClientWidth;\n                    if (newScrollLeft >= totalWidth) {\n                        const firstTextLength = clients[0].companyname.length;\n                        const firstTextWidth = Math.max(firstTextLength * 8, 50);\n                        const firstClientWidth = 40 + 8 + firstTextWidth + 20;\n                        newScrollLeft = firstClientWidth;\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const handleMouseLeave = ()=>{\n        setIsDragging(false);\n    };\n    const handleTouchStart = (e)=>{\n        setIsDragging(true);\n        setStartX(e.touches[0].pageX);\n        // Stop auto-scroll when user starts touching\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleTouchMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.touches[0].pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        // Calculate the total width dynamically\n        const totalWidth = clients.reduce((sum, client)=>{\n            const textLength = client.companyname.length;\n            const textWidth = Math.max(textLength * 8, 50);\n            return sum + (40 + 8 + textWidth + 20);\n        }, 0);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstTextLength = ((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0;\n            const firstTextWidth = Math.max(firstTextLength * 8, 50);\n            const firstClientWidth = 40 + 8 + firstTextWidth + 20;\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleTouchEnd = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops touching\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                // Calculate total width dynamically\n                const totalWidth = clients.reduce((sum, client)=>{\n                    const textLength = client.companyname.length;\n                    const textWidth = Math.max(textLength * 10, 60); // Updated multiplier\n                    return sum + (40 + 8 + textWidth + 20);\n                }, 0);\n                setScrollLeft((prevScrollLeft)=>{\n                    // Find which client we're currently showing\n                    let currentClientIndex = 0;\n                    let accumulatedWidth = 0;\n                    for(let i = 0; i < clients.length; i++){\n                        const textLength = clients[i].companyname.length;\n                        const textWidth = Math.max(textLength * 10, 60); // Updated multiplier\n                        const clientWidth = 40 + 8 + textWidth + 20;\n                        if (accumulatedWidth + clientWidth > prevScrollLeft) {\n                            currentClientIndex = i;\n                            break;\n                        }\n                        accumulatedWidth += clientWidth;\n                    }\n                    // Move to next client\n                    const nextClientIndex = (currentClientIndex + 1) % clients.length;\n                    const nextTextLength = clients[nextClientIndex].companyname.length;\n                    const nextTextWidth = Math.max(nextTextLength * 10, 60); // Updated multiplier\n                    const nextClientWidth = 40 + 8 + nextTextWidth + 20;\n                    let newScrollLeft = accumulatedWidth + nextClientWidth;\n                    if (newScrollLeft >= totalWidth) {\n                        const firstTextLength = clients[0].companyname.length;\n                        const firstTextWidth = Math.max(firstTextLength * 8, 50);\n                        const firstClientWidth = 40 + 8 + firstTextWidth + 20;\n                        newScrollLeft = firstClientWidth;\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const renderClientItems = ()=>{\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: clientItemStyle(7),\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            ...companyNameStyle,\n                            fontSize: '26px',\n                            fontWeight: 'bold'\n                        },\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 378,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (clients.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: clientItemStyle(12),\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            ...companyNameStyle,\n                            fontSize: '26px',\n                            fontWeight: 'bold'\n                        },\n                        children: \"No clients yet\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 389,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, client.id || index, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 11\n                    }, undefined)),\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, \"duplicate-\".concat(client.id || index), false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 11\n                    }, undefined))\n            ]\n        }, void 0, true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"hero\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid-lines\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-1\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 417,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-2\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-3\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-4\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-5\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                    lineNumber: 416,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row align-items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-content\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"section-title section-title-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"wow fadeInUp\",\n                                                    children: \"Enterprise-grade software & web development solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.2s\",\n                                                    \"data-cursor\": \"-opaque\",\n                                                    children: [\n                                                        \"Advanced digital solutions that\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"accelerate your growth\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.4s\",\n                                                    children: \"Transform your vision into scalable applications and sophisticated web platforms. Professional development services that deliver measurable results.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hero-btn wow fadeInUp\",\n                                            \"data-wow-delay\": \"0.6s\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/contact\",\n                                                    className: \"btn-default btn-highlighted\",\n                                                    children: \"Get Free Assessment\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/services\",\n                                                    className: \"btn-default\",\n                                                    children: \"View our services\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 425,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-company-slider\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"We're Trusted by more than \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"counter\",\n                                                    children: clients.length\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 46\n                                                }, undefined),\n                                                \"+ companies\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                ...containerStyle,\n                                                cursor: isDragging ? 'grabbing' : 'grab'\n                                            },\n                                            onMouseDown: handleMouseDown,\n                                            onMouseMove: handleMouseMove,\n                                            onMouseUp: handleMouseUp,\n                                            onMouseLeave: handleMouseLeave,\n                                            onTouchStart: handleTouchStart,\n                                            onTouchMove: handleTouchMove,\n                                            onTouchEnd: handleTouchEnd,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: trackStyle(isDragging, scrollLeft),\n                                                children: renderClientItems()\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                    lineNumber: 424,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n            lineNumber: 414,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(HomeMainHero, \"+cK3ts9BkIzmNmquLDoGqVEeSrY=\");\n_c2 = HomeMainHero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeMainHero);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ClientLogo\");\n$RefreshReg$(_c1, \"ClientItem\");\n$RefreshReg$(_c2, \"HomeMainHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/home/<USER>/HomeMainHero.tsx\n"));

/***/ })

});