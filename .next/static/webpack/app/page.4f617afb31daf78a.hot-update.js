"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/main/home/<USER>/HomeVideoHero.tsx":
/*!*********************************************************!*\
  !*** ./src/components/main/home/<USER>/HomeVideoHero.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst HomeVideoHero = ()=>{\n    _s();\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [videoStatus, setVideoStatus] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('loading');\n    const [videoSrc, setVideoSrc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('/videos/Hero-video.mp4'); // Default fallback\n    const [posterImage, setPosterImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('/images/hero-bg.jpg'); // Default fallback\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [clients, setClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [clientsLoading, setClientsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startX, setStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentClientIndex, setCurrentClientIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // Track which client is currently visible\n    const [scrollLeft, setScrollLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // Start at beginning\n    const [autoScrollInterval, setAutoScrollInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Style constants\n    const LOGO_SIZE = '60px'; // Increased from 40px to 60px\n    const DRAG_SENSITIVITY = 0.4;\n    const TEXT_WIDTH_MULTIPLIER = 10;\n    const MIN_TEXT_WIDTH = 60;\n    const TEXT_PADDING = 5; // Reduced from 20 to 5\n    const LOGO_GAP = 0; // No gap between logo and client name\n    // Helper function to calculate client width\n    const calculateClientWidth = (textLength)=>{\n        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n        return parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n    };\n    // Helper function to calculate total width\n    const calculateTotalWidth = (clients)=>{\n        return clients.reduce((sum, client)=>{\n            return sum + calculateClientWidth(client.companyname.length);\n        }, 0);\n    };\n    // Helper function to calculate scroll position for a specific client index\n    const calculateScrollPositionForClient = (clients, targetIndex)=>{\n        let scrollPosition = 0;\n        for(let i = 0; i < targetIndex && i < clients.length; i++){\n            scrollPosition += calculateClientWidth(clients[i].companyname.length);\n            // Add gap after each client (20px)\n            scrollPosition += 20; // 20px gap between client blocks\n        }\n        return scrollPosition;\n    };\n    const logoContainerStyle = {\n        width: LOGO_SIZE,\n        height: LOGO_SIZE,\n        borderRadius: '50%',\n        background: 'linear-gradient(var(--bg-color), var(--bg-color)) padding-box, linear-gradient(to left, var(--accent-color), var(--accent-secondary-color)) border-box',\n        border: '1px solid transparent',\n        padding: '2px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n    };\n    const logoStyle = {\n        width: 'calc(100% - 4px)',\n        height: 'calc(100% - 4px)',\n        borderRadius: '50%',\n        backgroundSize: 'cover',\n        backgroundRepeat: 'no-repeat',\n        backgroundPosition: 'center',\n        backgroundColor: 'transparent',\n        overflow: 'hidden'\n    };\n    // Logo component\n    const ClientLogo = (param)=>{\n        let { logourl } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: logoContainerStyle,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    ...logoStyle,\n                    backgroundImage: \"url(\".concat(logourl || \"/images/icon-testimonial-logo.svg\", \")\")\n                }\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n            lineNumber: 82,\n            columnNumber: 5\n        }, undefined);\n    };\n    const clientItemStyle = (textLength)=>{\n        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n        const totalWidth = parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n        return {\n            display: 'flex',\n            alignItems: 'center',\n            gap: \"\".concat(LOGO_GAP, \"px\"),\n            width: \"\".concat(totalWidth, \"px\"),\n            flexShrink: 0,\n            marginRight: '0px',\n            overflow: 'hidden'\n        };\n    };\n    const companyNameStyle = {\n        color: 'white',\n        fontSize: '18px',\n        fontWeight: 'normal',\n        fontFamily: 'sans-serif',\n        whiteSpace: 'nowrap',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n        flex: 1\n    };\n    const containerStyle = {\n        overflow: 'hidden',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '20px' // Consistent gap between all client blocks\n    };\n    // Client item component\n    const ClientItem = (param)=>{\n        let { client, index } = param;\n        const textLength = client.companyname.length;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: clientItemStyle(textLength),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {\n                    logourl: client.logourl\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: companyNameStyle,\n                    children: client.companyname\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, client.id || index, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n            lineNumber: 127,\n            columnNumber: 7\n        }, undefined);\n    };\n    // Fetch hero video settings\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            const fetchHeroVideo = {\n                \"HomeVideoHero.useEffect.fetchHeroVideo\": async ()=>{\n                    try {\n                        const response = await fetch('/api/admin/settings/hero-media');\n                        if (response.ok) {\n                            const data = await response.json();\n                            if (data.success && data.media) {\n                                if (data.media.hero_video_file) {\n                                    setVideoSrc(data.media.hero_video_file);\n                                }\n                                if (data.media.hero_video_poster) {\n                                    setPosterImage(data.media.hero_video_poster);\n                                }\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error fetching hero video:', error);\n                    // Keep default video\n                    }\n                }\n            }[\"HomeVideoHero.useEffect.fetchHeroVideo\"];\n            fetchHeroVideo();\n        }\n    }[\"HomeVideoHero.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            const fetchClients = {\n                \"HomeVideoHero.useEffect.fetchClients\": async ()=>{\n                    try {\n                        setClientsLoading(true);\n                        // Fetch all active clients without limit, sorted by name\n                        const response = await fetch('/api/clients?limit=0');\n                        const data = await response.json();\n                        if (data.success) {\n                            // Sort clients by company name to ensure alphabetical order\n                            const sortedClients = (data.clients || []).sort({\n                                \"HomeVideoHero.useEffect.fetchClients.sortedClients\": (a, b)=>a.companyname.localeCompare(b.companyname)\n                            }[\"HomeVideoHero.useEffect.fetchClients.sortedClients\"]);\n                            setClients(sortedClients);\n                            // Start with the first client fully visible\n                            setCurrentClientIndex(0);\n                            setScrollLeft(0);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching clients:', error);\n                    } finally{\n                        setClientsLoading(false);\n                    }\n                }\n            }[\"HomeVideoHero.useEffect.fetchClients\"];\n            fetchClients();\n        }\n    }[\"HomeVideoHero.useEffect\"], []);\n    // Auto-scroll function - moves one client at a time as complete blocks\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            if (clients.length === 0 || isDragging) return;\n            const interval = setInterval({\n                \"HomeVideoHero.useEffect.interval\": ()=>{\n                    setCurrentClientIndex({\n                        \"HomeVideoHero.useEffect.interval\": (prevIndex)=>{\n                            const nextIndex = (prevIndex + 1) % clients.length;\n                            // Calculate exact scroll position for the next client\n                            const nextScrollPosition = calculateScrollPositionForClient(clients, nextIndex);\n                            setScrollLeft(nextScrollPosition);\n                            return nextIndex;\n                        }\n                    }[\"HomeVideoHero.useEffect.interval\"]);\n                }\n            }[\"HomeVideoHero.useEffect.interval\"], 5000);\n            setAutoScrollInterval(interval);\n            return ({\n                \"HomeVideoHero.useEffect\": ()=>{\n                    if (interval) {\n                        clearInterval(interval);\n                    }\n                }\n            })[\"HomeVideoHero.useEffect\"];\n        }\n    }[\"HomeVideoHero.useEffect\"], [\n        clients.length,\n        isDragging\n    ]);\n    // Cleanup interval on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            return ({\n                \"HomeVideoHero.useEffect\": ()=>{\n                    if (autoScrollInterval) {\n                        clearInterval(autoScrollInterval);\n                    }\n                }\n            })[\"HomeVideoHero.useEffect\"];\n        }\n    }[\"HomeVideoHero.useEffect\"], [\n        autoScrollInterval\n    ]);\n    const handleMouseDown = (e)=>{\n        setIsDragging(true);\n        setStartX(e.pageX);\n        // Stop auto-scroll when user starts dragging\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops dragging\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setCurrentClientIndex((prevIndex)=>{\n                    const nextIndex = (prevIndex + 1) % clients.length;\n                    // Calculate exact scroll position for the next client\n                    const nextScrollPosition = calculateScrollPositionForClient(clients, nextIndex);\n                    setScrollLeft(nextScrollPosition);\n                    return nextIndex;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const handleMouseLeave = ()=>{\n        setIsDragging(false);\n    };\n    const handleTouchStart = (e)=>{\n        setIsDragging(true);\n        setStartX(e.touches[0].pageX);\n        // Stop auto-scroll when user starts touching\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleTouchMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.touches[0].pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleTouchEnd = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops touching\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setCurrentClientIndex((prevIndex)=>{\n                    const nextIndex = (prevIndex + 1) % clients.length;\n                    // Calculate exact scroll position for the next client\n                    const nextScrollPosition = calculateScrollPositionForClient(clients, nextIndex);\n                    setScrollLeft(nextScrollPosition);\n                    return nextIndex;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const renderClientItems = ()=>{\n        if (clientsLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0px',\n                    width: '140px',\n                    flexShrink: 0,\n                    marginRight: '0px',\n                    overflow: 'hidden'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            fontSize: '26px',\n                            fontWeight: 'bold',\n                            fontFamily: 'sans-serif',\n                            whiteSpace: 'nowrap',\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            flex: 1\n                        },\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                lineNumber: 332,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (clients.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0px',\n                    width: '200px',\n                    flexShrink: 0,\n                    marginRight: '0px',\n                    overflow: 'hidden'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            fontSize: '26px',\n                            fontWeight: 'bold',\n                            fontFamily: 'sans-serif',\n                            whiteSpace: 'nowrap',\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            flex: 1\n                        },\n                        children: \"No clients yet\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                lineNumber: 360,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, client.id || index, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 389,\n                        columnNumber: 11\n                    }, undefined)),\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, \"duplicate-\".concat(client.id || index), false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 11\n                    }, undefined))\n            ]\n        }, void 0, true);\n    };\n    // Force video element to reload when source changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            const video = videoRef.current;\n            if (video && videoSrc !== '/videos/Hero-video.mp4') {\n                setIsPlaying(false); // Reset playing state\n                video.load(); // This forces the video to reload with the new source\n            }\n        }\n    }[\"HomeVideoHero.useEffect\"], [\n        videoSrc\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            const video = videoRef.current;\n            if (!video) return;\n            // Event handler functions\n            const handleLoadStart = {\n                \"HomeVideoHero.useEffect.handleLoadStart\": ()=>{\n                    setVideoStatus('loading');\n                    setIsPlaying(false);\n                }\n            }[\"HomeVideoHero.useEffect.handleLoadStart\"];\n            const handleLoadedData = {\n                \"HomeVideoHero.useEffect.handleLoadedData\": ()=>setVideoStatus('loaded')\n            }[\"HomeVideoHero.useEffect.handleLoadedData\"];\n            const handleCanPlay = {\n                \"HomeVideoHero.useEffect.handleCanPlay\": ()=>setVideoStatus('canplay')\n            }[\"HomeVideoHero.useEffect.handleCanPlay\"];\n            const handlePlaying = {\n                \"HomeVideoHero.useEffect.handlePlaying\": ()=>{\n                    setVideoStatus('playing');\n                    setIsPlaying(true);\n                }\n            }[\"HomeVideoHero.useEffect.handlePlaying\"];\n            const handleError = {\n                \"HomeVideoHero.useEffect.handleError\": (e)=>{\n                    console.error('Video error:', e);\n                    setVideoStatus('error');\n                    setIsPlaying(false);\n                }\n            }[\"HomeVideoHero.useEffect.handleError\"];\n            // Add event listeners\n            video.addEventListener('loadstart', handleLoadStart);\n            video.addEventListener('loadeddata', handleLoadedData);\n            video.addEventListener('canplay', handleCanPlay);\n            video.addEventListener('playing', handlePlaying);\n            video.addEventListener('error', handleError);\n            // Wait for video to be ready before playing\n            const playVideo = {\n                \"HomeVideoHero.useEffect.playVideo\": ()=>{\n                    if (isPlaying) return; // Prevent multiple play requests\n                    if (video.readyState >= 2) {\n                        setIsPlaying(true);\n                        video.play().catch({\n                            \"HomeVideoHero.useEffect.playVideo\": (error)=>{\n                                console.error('Video autoplay failed:', error);\n                                setIsPlaying(false);\n                            }\n                        }[\"HomeVideoHero.useEffect.playVideo\"]);\n                    } else {\n                        // Wait for video to load\n                        const handleCanPlayOnce = {\n                            \"HomeVideoHero.useEffect.playVideo.handleCanPlayOnce\": ()=>{\n                                if (isPlaying) return; // Prevent multiple play requests\n                                setIsPlaying(true);\n                                video.play().catch({\n                                    \"HomeVideoHero.useEffect.playVideo.handleCanPlayOnce\": (error)=>{\n                                        console.error('Video autoplay failed:', error);\n                                        setIsPlaying(false);\n                                    }\n                                }[\"HomeVideoHero.useEffect.playVideo.handleCanPlayOnce\"]);\n                            }\n                        }[\"HomeVideoHero.useEffect.playVideo.handleCanPlayOnce\"];\n                        video.addEventListener('canplay', handleCanPlayOnce, {\n                            once: true\n                        });\n                    }\n                }\n            }[\"HomeVideoHero.useEffect.playVideo\"];\n            // Small delay to prevent overlapping play requests\n            const playTimeout = setTimeout(playVideo, 100);\n            // Cleanup function\n            return ({\n                \"HomeVideoHero.useEffect\": ()=>{\n                    clearTimeout(playTimeout);\n                    video.removeEventListener('loadstart', handleLoadStart);\n                    video.removeEventListener('loadeddata', handleLoadedData);\n                    video.removeEventListener('canplay', handleCanPlay);\n                    video.removeEventListener('playing', handlePlaying);\n                    video.removeEventListener('error', handleError);\n                }\n            })[\"HomeVideoHero.useEffect\"];\n        }\n    }[\"HomeVideoHero.useEffect\"], [\n        videoSrc\n    ]); // Re-run when video source changes\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"hero hero-bg-image hero-video\",\n            style: {\n                position: 'relative',\n                overflow: 'hidden',\n                backgroundImage: 'url(/images/hero-bg.jpg)',\n                backgroundSize: 'cover',\n                backgroundPosition: 'center',\n                backgroundRepeat: 'no-repeat'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hero-bg-video\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                        ref: videoRef,\n                        autoPlay: true,\n                        muted: true,\n                        loop: true,\n                        playsInline: true,\n                        id: \"myvideo\",\n                        src: videoSrc,\n                        poster: posterImage,\n                        style: {\n                            position: 'absolute',\n                            top: 0,\n                            left: 0,\n                            width: '100%',\n                            height: '100%',\n                            objectFit: 'cover',\n                            zIndex: -1\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                src: videoSrc,\n                                type: \"video/mp4\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Your browser does not support the video tag.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 492,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                    lineNumber: 489,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row align-items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-content\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"section-title section-title-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"wow fadeInUp\",\n                                                    children: \"Enterprise-grade software & web development solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.2s\",\n                                                    \"data-cursor\": \"-opaque\",\n                                                    children: [\n                                                        \"Advanced digital solutions that\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"accelerate your growth\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.4s\",\n                                                    children: \"Transform your vision into scalable applications and sophisticated web platforms. Professional development services that deliver measurable results.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hero-btn wow fadeInUp\",\n                                            \"data-wow-delay\": \"0.6s\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/contact\",\n                                                    className: \"btn-default btn-highlighted\",\n                                                    children: \"Get Free Assessment\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/services\",\n                                                    className: \"btn-default\",\n                                                    children: \"View our services\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                            lineNumber: 553,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                    lineNumber: 532,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                lineNumber: 530,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                            lineNumber: 529,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-company-slider\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"We're Trusted by more than \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"counter\",\n                                                    children: clients.length\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 46\n                                                }, undefined),\n                                                \"+ companies\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                ...containerStyle,\n                                                cursor: isDragging ? 'grabbing' : 'grab'\n                                            },\n                                            onMouseDown: handleMouseDown,\n                                            onMouseMove: handleMouseMove,\n                                            onMouseUp: handleMouseUp,\n                                            onMouseLeave: handleMouseLeave,\n                                            onTouchStart: handleTouchStart,\n                                            onTouchMove: handleTouchMove,\n                                            onTouchEnd: handleTouchEnd,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: '20px',\n                                                    transition: isDragging ? 'none' : 'transform 2s ease-in-out',\n                                                    transform: \"translateX(-\".concat(scrollLeft, \"px)\")\n                                                },\n                                                children: renderClientItems()\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                lineNumber: 587,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                lineNumber: 567,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                            lineNumber: 566,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                    lineNumber: 528,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n            lineNumber: 477,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(HomeVideoHero, \"C+ltSsjCuyQUglIwCaY+tcnYus0=\");\n_c = HomeVideoHero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeVideoHero);\nvar _c;\n$RefreshReg$(_c, \"HomeVideoHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/home/<USER>/HomeVideoHero.tsx\n"));

/***/ })

});