"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/main/home/<USER>/HomeMainHero.tsx":
/*!********************************************************!*\
  !*** ./src/components/main/home/<USER>/HomeMainHero.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n// Style constants\nconst LOGO_SIZE = '60px'; // Increased from 40px to 60px\nconst DRAG_SENSITIVITY = 0.4;\nconst TEXT_WIDTH_MULTIPLIER = 10;\nconst MIN_TEXT_WIDTH = 60;\nconst TEXT_PADDING = 5; // Reduced from 20 to 5\nconst LOGO_GAP = 0; // No gap between logo and client name\nconst logoContainerStyle = {\n    width: LOGO_SIZE,\n    height: LOGO_SIZE,\n    borderRadius: '50%',\n    background: 'linear-gradient(var(--bg-color), var(--bg-color)) padding-box, linear-gradient(to left, var(--accent-color), var(--accent-secondary-color)) border-box',\n    border: '1px solid transparent',\n    padding: '2px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center'\n};\nconst logoStyle = {\n    width: 'calc(100% - 4px)',\n    height: 'calc(100% - 4px)',\n    borderRadius: '50%',\n    backgroundSize: 'cover',\n    backgroundRepeat: 'no-repeat',\n    backgroundPosition: 'center',\n    backgroundColor: 'transparent',\n    overflow: 'hidden'\n};\nconst clientItemStyle = (textLength)=>{\n    const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n    const totalWidth = parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n    return {\n        display: 'flex',\n        alignItems: 'center',\n        gap: \"\".concat(LOGO_GAP, \"px\"),\n        width: \"\".concat(totalWidth, \"px\"),\n        flexShrink: 0,\n        marginRight: '0px',\n        overflow: 'hidden'\n    };\n};\nconst companyNameStyle = {\n    color: 'white',\n    fontSize: '18px',\n    fontWeight: 'normal',\n    fontFamily: 'sans-serif',\n    whiteSpace: 'nowrap',\n    overflow: 'hidden',\n    textOverflow: 'ellipsis',\n    flex: 1\n};\nconst containerStyle = {\n    overflow: 'hidden',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '20px' // Consistent gap between all client blocks\n};\nconst trackStyle = (isDragging, scrollLeft)=>({\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0px',\n        transition: isDragging ? 'none' : 'transform 2s ease-in-out',\n        transform: \"translateX(-\".concat(scrollLeft, \"px)\")\n    });\n// Logo component\nconst ClientLogo = (param)=>{\n    let { logourl } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: logoContainerStyle,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                ...logoStyle,\n                backgroundImage: \"url(\".concat(logourl || \"/images/icon-testimonial-logo.svg\", \")\")\n            }\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n            lineNumber: 86,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n        lineNumber: 85,\n        columnNumber: 3\n    }, undefined);\n};\n_c = ClientLogo;\n// Client item component\nconst ClientItem = (param)=>{\n    let { client, index } = param;\n    const textLength = client.companyname.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: clientItemStyle(textLength),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {\n                logourl: client.logourl\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: companyNameStyle,\n                children: client.companyname\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, client.id || index, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = ClientItem;\n// Helper function to calculate client width\nconst calculateClientWidth = (textLength)=>{\n    const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n    return parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n};\n// Helper function to calculate total width\nconst calculateTotalWidth = (clients)=>{\n    return clients.reduce((sum, client)=>{\n        return sum + calculateClientWidth(client.companyname.length);\n    }, 0);\n};\n// Helper function to calculate scroll position for a specific client index\nconst calculateScrollPositionForClient = (clients, targetIndex)=>{\n    let scrollPosition = 0;\n    for(let i = 0; i < targetIndex && i < clients.length; i++){\n        scrollPosition += calculateClientWidth(clients[i].companyname.length);\n    }\n    return scrollPosition;\n};\nconst HomeMainHero = ()=>{\n    _s();\n    const [clients, setClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startX, setStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentClientIndex, setCurrentClientIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // Track which client is currently visible\n    const [scrollLeft, setScrollLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // Start at beginning\n    const [autoScrollInterval, setAutoScrollInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            const fetchClients = {\n                \"HomeMainHero.useEffect.fetchClients\": async ()=>{\n                    try {\n                        setLoading(true);\n                        // Fetch all active clients without limit, sorted by name\n                        const response = await fetch('/api/clients?limit=0');\n                        const data = await response.json();\n                        if (data.success) {\n                            // Sort clients by company name to ensure alphabetical order\n                            const sortedClients = (data.clients || []).sort({\n                                \"HomeMainHero.useEffect.fetchClients.sortedClients\": (a, b)=>a.companyname.localeCompare(b.companyname)\n                            }[\"HomeMainHero.useEffect.fetchClients.sortedClients\"]);\n                            setClients(sortedClients);\n                            // Start with the first client fully visible\n                            setCurrentClientIndex(0);\n                            setScrollLeft(0);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching clients:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"HomeMainHero.useEffect.fetchClients\"];\n            fetchClients();\n        }\n    }[\"HomeMainHero.useEffect\"], []);\n    // Auto-scroll function - moves one client at a time as complete blocks\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            if (clients.length === 0 || isDragging) return;\n            const interval = setInterval({\n                \"HomeMainHero.useEffect.interval\": ()=>{\n                    setCurrentClientIndex({\n                        \"HomeMainHero.useEffect.interval\": (prevIndex)=>{\n                            const nextIndex = (prevIndex + 1) % clients.length;\n                            // Calculate exact scroll position for the next client\n                            const nextScrollPosition = calculateScrollPositionForClient(clients, nextIndex);\n                            setScrollLeft(nextScrollPosition);\n                            return nextIndex;\n                        }\n                    }[\"HomeMainHero.useEffect.interval\"]);\n                }\n            }[\"HomeMainHero.useEffect.interval\"], 5000);\n            setAutoScrollInterval(interval);\n            return ({\n                \"HomeMainHero.useEffect\": ()=>{\n                    if (interval) {\n                        clearInterval(interval);\n                    }\n                }\n            })[\"HomeMainHero.useEffect\"];\n        }\n    }[\"HomeMainHero.useEffect\"], [\n        clients.length,\n        isDragging\n    ]);\n    // Cleanup interval on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            return ({\n                \"HomeMainHero.useEffect\": ()=>{\n                    if (autoScrollInterval) {\n                        clearInterval(autoScrollInterval);\n                    }\n                }\n            })[\"HomeMainHero.useEffect\"];\n        }\n    }[\"HomeMainHero.useEffect\"], [\n        autoScrollInterval\n    ]);\n    const handleMouseDown = (e)=>{\n        setIsDragging(true);\n        setStartX(e.pageX);\n        // Stop auto-scroll when user starts dragging\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops dragging\n        if (clients.length > 0) {\n            let currentClientIndex = 0;\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    var _clients_currentClientIndex;\n                    const totalWidth = calculateTotalWidth(clients);\n                    // Calculate the width of the current client to move\n                    const currentClientWidth = calculateClientWidth(((_clients_currentClientIndex = clients[currentClientIndex]) === null || _clients_currentClientIndex === void 0 ? void 0 : _clients_currentClientIndex.companyname.length) || 0);\n                    let newScrollLeft = prevScrollLeft + currentClientWidth;\n                    // Move to next client\n                    currentClientIndex = (currentClientIndex + 1) % clients.length;\n                    // Reset to beginning when we've scrolled through all clients\n                    if (newScrollLeft >= totalWidth) {\n                        var _clients_;\n                        newScrollLeft = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n                        currentClientIndex = 1; // Start from second client next time\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const handleMouseLeave = ()=>{\n        setIsDragging(false);\n    };\n    const handleTouchStart = (e)=>{\n        setIsDragging(true);\n        setStartX(e.touches[0].pageX);\n        // Stop auto-scroll when user starts touching\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleTouchMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.touches[0].pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleTouchEnd = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops touching\n        if (clients.length > 0) {\n            let currentClientIndex = 0;\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    var _clients_currentClientIndex;\n                    const totalWidth = calculateTotalWidth(clients);\n                    // Calculate the width of the current client to move\n                    const currentClientWidth = calculateClientWidth(((_clients_currentClientIndex = clients[currentClientIndex]) === null || _clients_currentClientIndex === void 0 ? void 0 : _clients_currentClientIndex.companyname.length) || 0);\n                    let newScrollLeft = prevScrollLeft + currentClientWidth;\n                    // Move to next client\n                    currentClientIndex = (currentClientIndex + 1) % clients.length;\n                    // Reset to beginning when we've scrolled through all clients\n                    if (newScrollLeft >= totalWidth) {\n                        var _clients_;\n                        newScrollLeft = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n                        currentClientIndex = 1; // Start from second client next time\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const renderClientItems = ()=>{\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: clientItemStyle(7),\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            ...companyNameStyle,\n                            fontSize: '26px',\n                            fontWeight: 'bold'\n                        },\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 332,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (clients.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: clientItemStyle(12),\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            ...companyNameStyle,\n                            fontSize: '26px',\n                            fontWeight: 'bold'\n                        },\n                        children: \"No clients yet\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 343,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, client.id || index, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 11\n                    }, undefined)),\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, \"duplicate-\".concat(client.id || index), false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 11\n                    }, undefined))\n            ]\n        }, void 0, true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"hero\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid-lines\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-1\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-2\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-3\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-4\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 374,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-5\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 375,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                    lineNumber: 370,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row align-items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-content\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"section-title section-title-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"wow fadeInUp\",\n                                                    children: \"Enterprise-grade software & web development solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.2s\",\n                                                    \"data-cursor\": \"-opaque\",\n                                                    children: [\n                                                        \"Advanced digital solutions that\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"accelerate your growth\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.4s\",\n                                                    children: \"Transform your vision into scalable applications and sophisticated web platforms. Professional development services that deliver measurable results.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hero-btn wow fadeInUp\",\n                                            \"data-wow-delay\": \"0.6s\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/contact\",\n                                                    className: \"btn-default btn-highlighted\",\n                                                    children: \"Get Free Assessment\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/services\",\n                                                    className: \"btn-default\",\n                                                    children: \"View our services\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-company-slider\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"We're Trusted by more than \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"counter\",\n                                                    children: clients.length\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 46\n                                                }, undefined),\n                                                \"+ companies\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                ...containerStyle,\n                                                cursor: isDragging ? 'grabbing' : 'grab'\n                                            },\n                                            onMouseDown: handleMouseDown,\n                                            onMouseMove: handleMouseMove,\n                                            onMouseUp: handleMouseUp,\n                                            onMouseLeave: handleMouseLeave,\n                                            onTouchStart: handleTouchStart,\n                                            onTouchMove: handleTouchMove,\n                                            onTouchEnd: handleTouchEnd,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: trackStyle(isDragging, scrollLeft),\n                                                children: renderClientItems()\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                    lineNumber: 378,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n            lineNumber: 368,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(HomeMainHero, \"A4pwco2drNfheYBR/Lc5tdC5vc0=\");\n_c2 = HomeMainHero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeMainHero);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ClientLogo\");\n$RefreshReg$(_c1, \"ClientItem\");\n$RefreshReg$(_c2, \"HomeMainHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/home/<USER>/HomeMainHero.tsx\n"));

/***/ })

});