"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/main/home/<USER>/HomeVideoHero.tsx":
/*!*********************************************************!*\
  !*** ./src/components/main/home/<USER>/HomeVideoHero.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst HomeVideoHero = ()=>{\n    _s();\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [videoStatus, setVideoStatus] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('loading');\n    const [videoSrc, setVideoSrc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('/videos/Hero-video.mp4'); // Default fallback\n    const [posterImage, setPosterImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('/images/hero-bg.jpg'); // Default fallback\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [clients, setClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [clientsLoading, setClientsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startX, setStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [scrollLeft, setScrollLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [autoScrollInterval, setAutoScrollInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Style constants\n    const LOGO_SIZE = '40px';\n    const DRAG_SENSITIVITY = 0.4;\n    const TEXT_WIDTH_MULTIPLIER = 10;\n    const MIN_TEXT_WIDTH = 60;\n    const TEXT_PADDING = 20;\n    const LOGO_GAP = 0;\n    // Helper function to calculate client width\n    const calculateClientWidth = (textLength)=>{\n        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n        return parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n    };\n    // Helper function to calculate total width\n    const calculateTotalWidth = (clients)=>{\n        return clients.reduce((sum, client)=>{\n            return sum + calculateClientWidth(client.companyname.length);\n        }, 0);\n    };\n    // Logo component\n    const ClientLogo = (param)=>{\n        let { logourl } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                width: LOGO_SIZE,\n                height: LOGO_SIZE,\n                borderRadius: '50%',\n                backgroundImage: \"url(\".concat(logourl || \"/images/icon-testimonial-logo.svg\", \")\"),\n                backgroundSize: '70%',\n                backgroundRepeat: 'no-repeat',\n                backgroundPosition: 'center',\n                backgroundColor: 'transparent',\n                overflow: 'hidden',\n                background: 'linear-gradient(transparent, transparent) padding-box, linear-gradient(to left, var(--accent-color), var(--accent-secondary-color)) border-box',\n                border: '1px solid transparent',\n                position: 'relative'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'absolute',\n                    top: 0,\n                    bottom: 0,\n                    left: '-15%',\n                    right: 0,\n                    width: 0,\n                    height: '106%',\n                    background: 'linear-gradient(to left, var(--accent-color) 0%, var(--accent-secondary-color) 100%)',\n                    transform: 'skew(45deg)',\n                    transition: 'all 0.4s ease-in-out',\n                    zIndex: -1\n                }\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n            lineNumber: 41,\n            columnNumber: 5\n        }, undefined);\n    };\n    // Client item component\n    const ClientItem = (param)=>{\n        let { client, index } = param;\n        const textLength = client.companyname.length;\n        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n        const totalWidth = parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: \"\".concat(LOGO_GAP, \"px\"),\n                width: \"\".concat(totalWidth, \"px\"),\n                flexShrink: 0,\n                marginRight: '0px',\n                overflow: 'hidden'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {\n                    logourl: client.logourl\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        color: 'white',\n                        fontSize: '18px',\n                        fontWeight: 'normal',\n                        fontFamily: 'sans-serif',\n                        whiteSpace: 'nowrap',\n                        overflow: 'hidden',\n                        textOverflow: 'ellipsis',\n                        flex: 1\n                    },\n                    children: client.companyname\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, client.id || index, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, undefined);\n    };\n    // Fetch hero video settings\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            const fetchHeroVideo = {\n                \"HomeVideoHero.useEffect.fetchHeroVideo\": async ()=>{\n                    try {\n                        const response = await fetch('/api/admin/settings/hero-media');\n                        if (response.ok) {\n                            const data = await response.json();\n                            if (data.success && data.media) {\n                                if (data.media.hero_video_file) {\n                                    setVideoSrc(data.media.hero_video_file);\n                                }\n                                if (data.media.hero_video_poster) {\n                                    setPosterImage(data.media.hero_video_poster);\n                                }\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error fetching hero video:', error);\n                    // Keep default video\n                    }\n                }\n            }[\"HomeVideoHero.useEffect.fetchHeroVideo\"];\n            fetchHeroVideo();\n        }\n    }[\"HomeVideoHero.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            const fetchClients = {\n                \"HomeVideoHero.useEffect.fetchClients\": async ()=>{\n                    try {\n                        setClientsLoading(true);\n                        const response = await fetch('/api/clients?limit=6');\n                        const data = await response.json();\n                        if (data.success) {\n                            setClients(data.clients || []);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching clients:', error);\n                    } finally{\n                        setClientsLoading(false);\n                    }\n                }\n            }[\"HomeVideoHero.useEffect.fetchClients\"];\n            fetchClients();\n        }\n    }[\"HomeVideoHero.useEffect\"], []);\n    // Force video element to reload when source changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            const video = videoRef.current;\n            if (video && videoSrc !== '/videos/Hero-video.mp4') {\n                setIsPlaying(false); // Reset playing state\n                video.load(); // This forces the video to reload with the new source\n            }\n        }\n    }[\"HomeVideoHero.useEffect\"], [\n        videoSrc\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            const video = videoRef.current;\n            if (!video) return;\n            // Event handler functions\n            const handleLoadStart = {\n                \"HomeVideoHero.useEffect.handleLoadStart\": ()=>{\n                    setVideoStatus('loading');\n                    setIsPlaying(false);\n                }\n            }[\"HomeVideoHero.useEffect.handleLoadStart\"];\n            const handleLoadedData = {\n                \"HomeVideoHero.useEffect.handleLoadedData\": ()=>setVideoStatus('loaded')\n            }[\"HomeVideoHero.useEffect.handleLoadedData\"];\n            const handleCanPlay = {\n                \"HomeVideoHero.useEffect.handleCanPlay\": ()=>setVideoStatus('canplay')\n            }[\"HomeVideoHero.useEffect.handleCanPlay\"];\n            const handlePlaying = {\n                \"HomeVideoHero.useEffect.handlePlaying\": ()=>{\n                    setVideoStatus('playing');\n                    setIsPlaying(true);\n                }\n            }[\"HomeVideoHero.useEffect.handlePlaying\"];\n            const handleError = {\n                \"HomeVideoHero.useEffect.handleError\": (e)=>{\n                    console.error('Video error:', e);\n                    setVideoStatus('error');\n                    setIsPlaying(false);\n                }\n            }[\"HomeVideoHero.useEffect.handleError\"];\n            // Add event listeners\n            video.addEventListener('loadstart', handleLoadStart);\n            video.addEventListener('loadeddata', handleLoadedData);\n            video.addEventListener('canplay', handleCanPlay);\n            video.addEventListener('playing', handlePlaying);\n            video.addEventListener('error', handleError);\n            // Wait for video to be ready before playing\n            const playVideo = {\n                \"HomeVideoHero.useEffect.playVideo\": ()=>{\n                    if (isPlaying) return; // Prevent multiple play requests\n                    if (video.readyState >= 2) {\n                        setIsPlaying(true);\n                        video.play().catch({\n                            \"HomeVideoHero.useEffect.playVideo\": (error)=>{\n                                console.error('Video autoplay failed:', error);\n                                setIsPlaying(false);\n                            }\n                        }[\"HomeVideoHero.useEffect.playVideo\"]);\n                    } else {\n                        // Wait for video to load\n                        const handleCanPlayOnce = {\n                            \"HomeVideoHero.useEffect.playVideo.handleCanPlayOnce\": ()=>{\n                                if (isPlaying) return; // Prevent multiple play requests\n                                setIsPlaying(true);\n                                video.play().catch({\n                                    \"HomeVideoHero.useEffect.playVideo.handleCanPlayOnce\": (error)=>{\n                                        console.error('Video autoplay failed:', error);\n                                        setIsPlaying(false);\n                                    }\n                                }[\"HomeVideoHero.useEffect.playVideo.handleCanPlayOnce\"]);\n                            }\n                        }[\"HomeVideoHero.useEffect.playVideo.handleCanPlayOnce\"];\n                        video.addEventListener('canplay', handleCanPlayOnce, {\n                            once: true\n                        });\n                    }\n                }\n            }[\"HomeVideoHero.useEffect.playVideo\"];\n            // Small delay to prevent overlapping play requests\n            const playTimeout = setTimeout(playVideo, 100);\n            // Cleanup function\n            return ({\n                \"HomeVideoHero.useEffect\": ()=>{\n                    clearTimeout(playTimeout);\n                    video.removeEventListener('loadstart', handleLoadStart);\n                    video.removeEventListener('loadeddata', handleLoadedData);\n                    video.removeEventListener('canplay', handleCanPlay);\n                    video.removeEventListener('playing', handlePlaying);\n                    video.removeEventListener('error', handleError);\n                }\n            })[\"HomeVideoHero.useEffect\"];\n        }\n    }[\"HomeVideoHero.useEffect\"], [\n        videoSrc\n    ]); // Re-run when video source changes\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"hero hero-bg-image hero-video\",\n            style: {\n                position: 'relative',\n                overflow: 'hidden',\n                backgroundImage: 'url(/images/hero-bg.jpg)',\n                backgroundSize: 'cover',\n                backgroundPosition: 'center',\n                backgroundRepeat: 'no-repeat'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hero-bg-video\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                        ref: videoRef,\n                        autoPlay: true,\n                        muted: true,\n                        loop: true,\n                        playsInline: true,\n                        id: \"myvideo\",\n                        src: videoSrc,\n                        poster: posterImage,\n                        style: {\n                            position: 'absolute',\n                            top: 0,\n                            left: 0,\n                            width: '100%',\n                            height: '100%',\n                            objectFit: 'cover',\n                            zIndex: -1\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                src: videoSrc,\n                                type: \"video/mp4\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Your browser does not support the video tag.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row align-items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-content\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"section-title section-title-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"wow fadeInUp\",\n                                                    children: \"Enterprise-grade software & web development solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.2s\",\n                                                    \"data-cursor\": \"-opaque\",\n                                                    children: [\n                                                        \"Advanced digital solutions that\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"accelerate your growth\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.4s\",\n                                                    children: \"Transform your vision into scalable applications and sophisticated web platforms. Professional development services that deliver measurable results.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hero-btn wow fadeInUp\",\n                                            \"data-wow-delay\": \"0.6s\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/contact\",\n                                                    className: \"btn-default btn-highlighted\",\n                                                    children: \"Get Free Assessment\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/services\",\n                                                    className: \"btn-default\",\n                                                    children: \"View our services\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-company-slider\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"We're Trusted by more than \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"counter\",\n                                                    children: clients.length\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 46\n                                                }, undefined),\n                                                \"+ companies\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"swiper\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"swiper-wrapper\",\n                                                children: clientsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"swiper-slide\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"company-logo\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                color: 'white',\n                                                                fontSize: '14px'\n                                                            },\n                                                            children: \"Loading...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 23\n                                                }, undefined) : clients.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"swiper-slide\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"company-logo\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                color: 'white',\n                                                                fontSize: '14px'\n                                                            },\n                                                            children: \"No clients available\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 23\n                                                }, undefined) : clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"swiper-slide\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"company-logo\",\n                                                            style: {\n                                                                display: 'flex',\n                                                                alignItems: 'center',\n                                                                justifyContent: 'flex-start',\n                                                                gap: '12px',\n                                                                padding: '10px',\n                                                                minWidth: '200px',\n                                                                whiteSpace: 'nowrap'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        width: '70px',\n                                                                        height: '70px',\n                                                                        borderRadius: '50%',\n                                                                        overflow: 'hidden',\n                                                                        display: 'flex',\n                                                                        alignItems: 'center',\n                                                                        justifyContent: 'center',\n                                                                        flexShrink: 0,\n                                                                        backgroundColor: 'transparent'\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: client.logourl || \"/images/icon-testimonial-logo.svg\",\n                                                                        alt: client.companyname,\n                                                                        style: {\n                                                                            width: '100%',\n                                                                            height: '100%',\n                                                                            objectFit: 'cover',\n                                                                            borderRadius: '50%'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                                        lineNumber: 359,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        color: 'white',\n                                                                        fontSize: '26px',\n                                                                        fontWeight: 'bold',\n                                                                        fontFamily: 'sans-serif',\n                                                                        whiteSpace: 'nowrap',\n                                                                        overflow: 'hidden',\n                                                                        textOverflow: 'ellipsis',\n                                                                        maxWidth: '150px'\n                                                                    },\n                                                                    children: client.companyname\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                                    lineNumber: 370,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, client.id || index, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 25\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n            lineNumber: 225,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(HomeVideoHero, \"i2xf6eyC1IJjo38oBy5cqW8XxVY=\");\n_c = HomeVideoHero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeVideoHero);\nvar _c;\n$RefreshReg$(_c, \"HomeVideoHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/home/<USER>/HomeVideoHero.tsx\n"));

/***/ })

});