"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/main/home/<USER>/HomeMainHero.tsx":
/*!********************************************************!*\
  !*** ./src/components/main/home/<USER>/HomeMainHero.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n// Style constants\nconst LOGO_SIZE = '40px';\nconst DRAG_SENSITIVITY = 0.4;\nconst TEXT_WIDTH_MULTIPLIER = 10;\nconst MIN_TEXT_WIDTH = 60;\nconst TEXT_PADDING = 20;\nconst LOGO_GAP = 0;\nconst logoStyle = {\n    width: LOGO_SIZE,\n    height: LOGO_SIZE,\n    borderRadius: '50%',\n    backgroundSize: 'cover',\n    backgroundRepeat: 'no-repeat',\n    backgroundPosition: 'center',\n    backgroundColor: 'transparent',\n    overflow: 'hidden',\n    border: '1px solid transparent',\n    background: 'linear-gradient(var(--bg-color), var(--bg-color)) padding-box, linear-gradient(to left, var(--accent-color), var(--accent-secondary-color)) border-box'\n};\nconst clientItemStyle = (textLength)=>{\n    const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n    const totalWidth = parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n    return {\n        display: 'flex',\n        alignItems: 'center',\n        gap: \"\".concat(LOGO_GAP, \"px\"),\n        width: \"\".concat(totalWidth, \"px\"),\n        flexShrink: 0,\n        marginRight: '0px',\n        overflow: 'hidden'\n    };\n};\nconst companyNameStyle = {\n    color: 'white',\n    fontSize: '18px',\n    fontWeight: 'normal',\n    fontFamily: 'sans-serif',\n    whiteSpace: 'nowrap',\n    overflow: 'hidden',\n    textOverflow: 'ellipsis',\n    flex: 1\n};\nconst containerStyle = {\n    overflow: 'hidden',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '0px' // No gap between blocks\n};\nconst trackStyle = (isDragging, scrollLeft)=>({\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0px',\n        transition: isDragging ? 'none' : 'transform 2s ease-in-out',\n        transform: \"translateX(-\".concat(scrollLeft, \"px)\")\n    });\n// Logo component\nconst ClientLogo = (param)=>{\n    let { logourl } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            width: LOGO_SIZE,\n            height: LOGO_SIZE,\n            borderRadius: '50%',\n            backgroundImage: \"url(\".concat(logourl || \"/images/icon-testimonial-logo.svg\", \")\"),\n            backgroundSize: 'cover',\n            backgroundRepeat: 'no-repeat',\n            backgroundPosition: 'center',\n            backgroundColor: 'transparent',\n            overflow: 'hidden',\n            border: '2px solid',\n            borderImage: 'linear-gradient(to left, var(--accent-color), var(--accent-secondary-color)) 1'\n        }\n    }, void 0, false, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n        lineNumber: 75,\n        columnNumber: 3\n    }, undefined);\n};\n_c = ClientLogo;\n// Client item component\nconst ClientItem = (param)=>{\n    let { client, index } = param;\n    const textLength = client.companyname.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: clientItemStyle(textLength),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {\n                logourl: client.logourl\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: companyNameStyle,\n                children: client.companyname\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, client.id || index, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = ClientItem;\n// Helper function to calculate client width\nconst calculateClientWidth = (textLength)=>{\n    const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n    return parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n};\n// Helper function to calculate total width\nconst calculateTotalWidth = (clients)=>{\n    return clients.reduce((sum, client)=>{\n        return sum + calculateClientWidth(client.companyname.length);\n    }, 0);\n};\nconst HomeMainHero = ()=>{\n    _s();\n    const [clients, setClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startX, setStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [scrollLeft, setScrollLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(160); // Start with first client fully hidden\n    const [autoScrollInterval, setAutoScrollInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            const fetchClients = {\n                \"HomeMainHero.useEffect.fetchClients\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const response = await fetch('/api/clients');\n                        const data = await response.json();\n                        if (data.success) {\n                            setClients(data.clients || []);\n                            // Set initial scroll position to hide first client\n                            if (data.clients && data.clients.length > 0) {\n                                const firstClientWidth = calculateClientWidth(data.clients[0].companyname.length);\n                                setScrollLeft(firstClientWidth);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error fetching clients:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"HomeMainHero.useEffect.fetchClients\"];\n            fetchClients();\n        }\n    }[\"HomeMainHero.useEffect\"], []);\n    // Auto-scroll function\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            if (clients.length === 0 || isDragging) return;\n            const interval = setInterval({\n                \"HomeMainHero.useEffect.interval\": ()=>{\n                    setScrollLeft({\n                        \"HomeMainHero.useEffect.interval\": (prevScrollLeft)=>{\n                            const totalWidth = calculateTotalWidth(clients);\n                            const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                            let newScrollLeft = prevScrollLeft + firstClientWidth;\n                            if (newScrollLeft >= totalWidth) {\n                                newScrollLeft = firstClientWidth;\n                            }\n                            return newScrollLeft;\n                        }\n                    }[\"HomeMainHero.useEffect.interval\"]);\n                }\n            }[\"HomeMainHero.useEffect.interval\"], 5000);\n            setAutoScrollInterval(interval);\n            return ({\n                \"HomeMainHero.useEffect\": ()=>{\n                    if (interval) {\n                        clearInterval(interval);\n                    }\n                }\n            })[\"HomeMainHero.useEffect\"];\n        }\n    }[\"HomeMainHero.useEffect\"], [\n        clients.length,\n        isDragging\n    ]);\n    // Cleanup interval on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            return ({\n                \"HomeMainHero.useEffect\": ()=>{\n                    if (autoScrollInterval) {\n                        clearInterval(autoScrollInterval);\n                    }\n                }\n            })[\"HomeMainHero.useEffect\"];\n        }\n    }[\"HomeMainHero.useEffect\"], [\n        autoScrollInterval\n    ]);\n    const handleMouseDown = (e)=>{\n        setIsDragging(true);\n        setStartX(e.pageX);\n        // Stop auto-scroll when user starts dragging\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops dragging\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    const totalWidth = calculateTotalWidth(clients);\n                    const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                    let newScrollLeft = prevScrollLeft + firstClientWidth;\n                    if (newScrollLeft >= totalWidth) {\n                        newScrollLeft = firstClientWidth;\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const handleMouseLeave = ()=>{\n        setIsDragging(false);\n    };\n    const handleTouchStart = (e)=>{\n        setIsDragging(true);\n        setStartX(e.touches[0].pageX);\n        // Stop auto-scroll when user starts touching\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleTouchMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.touches[0].pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleTouchEnd = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops touching\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    const totalWidth = calculateTotalWidth(clients);\n                    const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                    let newScrollLeft = prevScrollLeft + firstClientWidth;\n                    if (newScrollLeft >= totalWidth) {\n                        newScrollLeft = firstClientWidth;\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const renderClientItems = ()=>{\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: clientItemStyle(7),\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            ...companyNameStyle,\n                            fontSize: '26px',\n                            fontWeight: 'bold'\n                        },\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 301,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (clients.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: clientItemStyle(12),\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            ...companyNameStyle,\n                            fontSize: '26px',\n                            fontWeight: 'bold'\n                        },\n                        children: \"No clients yet\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 312,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, client.id || index, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 11\n                    }, undefined)),\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, \"duplicate-\".concat(client.id || index), false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 11\n                    }, undefined))\n            ]\n        }, void 0, true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"hero\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid-lines\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-1\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-2\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-3\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-4\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-5\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row align-items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-content\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"section-title section-title-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"wow fadeInUp\",\n                                                    children: \"Enterprise-grade software & web development solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.2s\",\n                                                    \"data-cursor\": \"-opaque\",\n                                                    children: [\n                                                        \"Advanced digital solutions that\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"accelerate your growth\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.4s\",\n                                                    children: \"Transform your vision into scalable applications and sophisticated web platforms. Professional development services that deliver measurable results.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hero-btn wow fadeInUp\",\n                                            \"data-wow-delay\": \"0.6s\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/contact\",\n                                                    className: \"btn-default btn-highlighted\",\n                                                    children: \"Get Free Assessment\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/services\",\n                                                    className: \"btn-default\",\n                                                    children: \"View our services\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-company-slider\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"We're Trusted by more than \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"counter\",\n                                                    children: clients.length\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 46\n                                                }, undefined),\n                                                \"+ companies\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                ...containerStyle,\n                                                cursor: isDragging ? 'grabbing' : 'grab'\n                                            },\n                                            onMouseDown: handleMouseDown,\n                                            onMouseMove: handleMouseMove,\n                                            onMouseUp: handleMouseUp,\n                                            onMouseLeave: handleMouseLeave,\n                                            onTouchStart: handleTouchStart,\n                                            onTouchMove: handleTouchMove,\n                                            onTouchEnd: handleTouchEnd,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: trackStyle(isDragging, scrollLeft),\n                                                children: renderClientItems()\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                    lineNumber: 347,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n            lineNumber: 337,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(HomeMainHero, \"+cK3ts9BkIzmNmquLDoGqVEeSrY=\");\n_c2 = HomeMainHero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeMainHero);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ClientLogo\");\n$RefreshReg$(_c1, \"ClientItem\");\n$RefreshReg$(_c2, \"HomeMainHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/home/<USER>/HomeMainHero.tsx\n"));

/***/ })

});