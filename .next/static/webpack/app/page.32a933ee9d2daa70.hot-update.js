"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/main/home/<USER>/HomeSliderHero.tsx":
/*!**********************************************************!*\
  !*** ./src/components/main/home/<USER>/HomeSliderHero.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst HomeSliderHero = ()=>{\n    _s();\n    const [sliderImages, setSliderImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [clients, setClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [clientsLoading, setClientsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startX, setStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentClientIndex, setCurrentClientIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // Track which client is currently visible\n    const [scrollLeft, setScrollLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // Start at beginning\n    const [autoScrollInterval, setAutoScrollInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Style constants\n    const LOGO_SIZE = '60px'; // Increased from 40px to 60px\n    const DRAG_SENSITIVITY = 0.4;\n    const TEXT_WIDTH_MULTIPLIER = 10;\n    const MIN_TEXT_WIDTH = 60;\n    const TEXT_PADDING = 5; // Reduced from 20 to 5\n    const LOGO_GAP = 0; // No gap between logo and client name\n    // Helper function to calculate client width\n    const calculateClientWidth = (textLength)=>{\n        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n        return parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n    };\n    // Helper function to calculate total width\n    const calculateTotalWidth = (clients)=>{\n        return clients.reduce((sum, client)=>{\n            return sum + calculateClientWidth(client.companyname.length);\n        }, 0);\n    };\n    // Helper function to calculate scroll position for a specific client index\n    const calculateScrollPositionForClient = (clients, targetIndex)=>{\n        let scrollPosition = 0;\n        for(let i = 0; i < targetIndex && i < clients.length; i++){\n            scrollPosition += calculateClientWidth(clients[i].companyname.length);\n            // Add gap after each client (20px)\n            scrollPosition += 20; // 20px gap between client blocks\n        }\n        return scrollPosition;\n    };\n    const logoContainerStyle = {\n        width: LOGO_SIZE,\n        height: LOGO_SIZE,\n        borderRadius: '50%',\n        background: 'linear-gradient(var(--bg-color), var(--bg-color)) padding-box, linear-gradient(to left, var(--accent-color), var(--accent-secondary-color)) border-box',\n        border: '1px solid transparent',\n        padding: '2px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n    };\n    const logoStyle = {\n        width: 'calc(100% - 4px)',\n        height: 'calc(100% - 4px)',\n        borderRadius: '50%',\n        backgroundSize: 'cover',\n        backgroundRepeat: 'no-repeat',\n        backgroundPosition: 'center',\n        backgroundColor: 'transparent',\n        overflow: 'hidden'\n    };\n    // Logo component\n    const ClientLogo = (param)=>{\n        let { logourl } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: logoContainerStyle,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    ...logoStyle,\n                    backgroundImage: \"url(\".concat(logourl || \"/images/icon-testimonial-logo.svg\", \")\")\n                }\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n            lineNumber: 77,\n            columnNumber: 5\n        }, undefined);\n    };\n    const clientItemStyle = (textLength)=>{\n        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n        const totalWidth = parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n        return {\n            display: 'flex',\n            alignItems: 'center',\n            gap: \"\".concat(LOGO_GAP, \"px\"),\n            width: \"\".concat(totalWidth, \"px\"),\n            flexShrink: 0,\n            marginRight: '0px',\n            overflow: 'hidden'\n        };\n    };\n    const companyNameStyle = {\n        color: 'white',\n        fontSize: '18px',\n        fontWeight: 'normal',\n        fontFamily: 'sans-serif',\n        whiteSpace: 'nowrap',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n        flex: 1\n    };\n    const containerStyle = {\n        overflow: 'hidden',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '20px' // Consistent gap between all client blocks\n    };\n    // Client item component\n    const ClientItem = (param)=>{\n        let { client, index } = param;\n        const textLength = client.companyname.length;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: clientItemStyle(textLength),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {\n                    logourl: client.logourl\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: companyNameStyle,\n                    children: client.companyname\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, client.id || index, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, undefined);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeSliderHero.useEffect\": ()=>{\n            const fetchSliderImages = {\n                \"HomeSliderHero.useEffect.fetchSliderImages\": async ()=>{\n                    try {\n                        const response = await fetch('/api/admin/settings/hero-media');\n                        if (response.ok) {\n                            var _data_media;\n                            const data = await response.json();\n                            if (data.success && ((_data_media = data.media) === null || _data_media === void 0 ? void 0 : _data_media.hero_slider_images)) {\n                                // Parse the slider images (comma-separated URLs)\n                                const images = data.media.hero_slider_images.split(',').map({\n                                    \"HomeSliderHero.useEffect.fetchSliderImages.images\": (url)=>url.trim()\n                                }[\"HomeSliderHero.useEffect.fetchSliderImages.images\"]).filter({\n                                    \"HomeSliderHero.useEffect.fetchSliderImages.images\": (url)=>url\n                                }[\"HomeSliderHero.useEffect.fetchSliderImages.images\"]);\n                                setSliderImages(images);\n                            } else {\n                                setSliderImages([\n                                    '/images/hero-bg.jpg',\n                                    '/images/hero-bg-2.jpg'\n                                ]);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error fetching slider images:', error);\n                        setSliderImages([\n                            '/images/hero-bg.jpg',\n                            '/images/hero-bg-2.jpg'\n                        ]);\n                    }\n                }\n            }[\"HomeSliderHero.useEffect.fetchSliderImages\"];\n            fetchSliderImages();\n        }\n    }[\"HomeSliderHero.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeSliderHero.useEffect\": ()=>{\n            const fetchClients = {\n                \"HomeSliderHero.useEffect.fetchClients\": async ()=>{\n                    try {\n                        setClientsLoading(true);\n                        // Fetch all active clients without limit, sorted by name\n                        const response = await fetch('/api/clients?limit=0');\n                        const data = await response.json();\n                        if (data.success) {\n                            // Sort clients by company name to ensure alphabetical order\n                            const sortedClients = (data.clients || []).sort({\n                                \"HomeSliderHero.useEffect.fetchClients.sortedClients\": (a, b)=>a.companyname.localeCompare(b.companyname)\n                            }[\"HomeSliderHero.useEffect.fetchClients.sortedClients\"]);\n                            setClients(sortedClients);\n                            // Start with the first client fully visible\n                            setCurrentClientIndex(0);\n                            setScrollLeft(0);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching clients:', error);\n                    } finally{\n                        setClientsLoading(false);\n                    }\n                }\n            }[\"HomeSliderHero.useEffect.fetchClients\"];\n            fetchClients();\n        }\n    }[\"HomeSliderHero.useEffect\"], []);\n    // Auto-scroll function - moves one client at a time as complete blocks\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeSliderHero.useEffect\": ()=>{\n            if (clients.length === 0 || isDragging) return;\n            const interval = setInterval({\n                \"HomeSliderHero.useEffect.interval\": ()=>{\n                    setCurrentClientIndex({\n                        \"HomeSliderHero.useEffect.interval\": (prevIndex)=>{\n                            const nextIndex = (prevIndex + 1) % clients.length;\n                            // Calculate exact scroll position for the next client\n                            const nextScrollPosition = calculateScrollPositionForClient(clients, nextIndex);\n                            setScrollLeft(nextScrollPosition);\n                            return nextIndex;\n                        }\n                    }[\"HomeSliderHero.useEffect.interval\"]);\n                }\n            }[\"HomeSliderHero.useEffect.interval\"], 5000);\n            setAutoScrollInterval(interval);\n            return ({\n                \"HomeSliderHero.useEffect\": ()=>{\n                    if (interval) {\n                        clearInterval(interval);\n                    }\n                }\n            })[\"HomeSliderHero.useEffect\"];\n        }\n    }[\"HomeSliderHero.useEffect\"], [\n        clients.length,\n        isDragging\n    ]);\n    // Cleanup interval on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeSliderHero.useEffect\": ()=>{\n            return ({\n                \"HomeSliderHero.useEffect\": ()=>{\n                    if (autoScrollInterval) {\n                        clearInterval(autoScrollInterval);\n                    }\n                }\n            })[\"HomeSliderHero.useEffect\"];\n        }\n    }[\"HomeSliderHero.useEffect\"], [\n        autoScrollInterval\n    ]);\n    const handleMouseDown = (e)=>{\n        setIsDragging(true);\n        setStartX(e.pageX);\n        // Stop auto-scroll when user starts dragging\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops dragging\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setCurrentClientIndex((prevIndex)=>{\n                    const nextIndex = (prevIndex + 1) % clients.length;\n                    // Calculate exact scroll position for the next client\n                    const nextScrollPosition = calculateScrollPositionForClient(clients, nextIndex);\n                    setScrollLeft(nextScrollPosition);\n                    return nextIndex;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const handleMouseLeave = ()=>{\n        setIsDragging(false);\n    };\n    const handleTouchStart = (e)=>{\n        setIsDragging(true);\n        setStartX(e.touches[0].pageX);\n        // Stop auto-scroll when user starts touching\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleTouchMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.touches[0].pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleTouchEnd = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops touching\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setCurrentClientIndex((prevIndex)=>{\n                    const nextIndex = (prevIndex + 1) % clients.length;\n                    // Calculate exact scroll position for the next client\n                    const nextScrollPosition = calculateScrollPositionForClient(clients, nextIndex);\n                    setScrollLeft(nextScrollPosition);\n                    return nextIndex;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const renderClientItems = ()=>{\n        if (clientsLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0px',\n                    width: '140px',\n                    flexShrink: 0,\n                    marginRight: '0px',\n                    overflow: 'hidden'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            fontSize: '26px',\n                            fontWeight: 'bold',\n                            fontFamily: 'sans-serif',\n                            whiteSpace: 'nowrap',\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            flex: 1\n                        },\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                lineNumber: 325,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (clients.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0px',\n                    width: '200px',\n                    flexShrink: 0,\n                    marginRight: '0px',\n                    overflow: 'hidden'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            fontSize: '26px',\n                            fontWeight: 'bold',\n                            fontFamily: 'sans-serif',\n                            whiteSpace: 'nowrap',\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            flex: 1\n                        },\n                        children: \"No clients yet\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                lineNumber: 353,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, client.id || index, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 11\n                    }, undefined)),\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, \"duplicate-\".concat(client.id || index), false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 11\n                    }, undefined))\n            ]\n        }, void 0, true);\n    };\n    // Default slides if no images are configured\n    const defaultSlides = [\n        '/images/hero-bg.jpg',\n        '/images/hero-bg-2.jpg'\n    ];\n    const slidesToShow = sliderImages.length > 0 ? sliderImages : defaultSlides;\n    // Handle dot navigation\n    const goToSlide = (index)=>{\n        setCurrentSlide(index);\n    };\n    // Auto-advance slides - using different timing to avoid conflict with client slider\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeSliderHero.useEffect\": ()=>{\n            if (slidesToShow.length > 1) {\n                const timer = setInterval({\n                    \"HomeSliderHero.useEffect.timer\": ()=>{\n                        setCurrentSlide({\n                            \"HomeSliderHero.useEffect.timer\": (prev)=>(prev + 1) % slidesToShow.length\n                        }[\"HomeSliderHero.useEffect.timer\"]);\n                    }\n                }[\"HomeSliderHero.useEffect.timer\"], 7000); // 7 seconds instead of 5 to stagger with client slider\n                return ({\n                    \"HomeSliderHero.useEffect\": ()=>clearInterval(timer)\n                })[\"HomeSliderHero.useEffect\"];\n            }\n        }\n    }[\"HomeSliderHero.useEffect\"], [\n        slidesToShow.length\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"hero hero-bg-image hero-slider-layout\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"swiper hero-swiper\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"swiper-wrapper\",\n                        children: slidesToShow.map((imageUrl, index)=>{\n                            const isActive = index === currentSlide;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"swiper-slide\",\n                                style: {\n                                    display: isActive ? 'block' : 'none'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-slide\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hero-slider-image\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: imageUrl,\n                                                alt: \"Hero slide \".concat(index + 1)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"container\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"row align-items-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-lg-12\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"hero-content\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"section-title section-title-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"wow fadeInUp\",\n                                                                            children: \"Enterprise-grade software & web development solutions\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                            lineNumber: 444,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                            className: \"wow fadeInUp\",\n                                                                            \"data-wow-delay\": \"0.2s\",\n                                                                            \"data-cursor\": \"-opaque\",\n                                                                            children: [\n                                                                                \"Advanced digital solutions that\",\n                                                                                \" \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"accelerate your growth\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                                    lineNumber: 453,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                            lineNumber: 447,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"wow fadeInUp\",\n                                                                            \"data-wow-delay\": \"0.4s\",\n                                                                            children: \"Transform your vision into scalable applications and sophisticated web platforms. Professional development services that deliver measurable results.\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                            lineNumber: 455,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                    lineNumber: 443,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"hero-btn wow fadeInUp\",\n                                                                    \"data-wow-delay\": \"0.6s\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                            href: \"/main/contact\",\n                                                                            className: \"btn-default btn-highlighted\",\n                                                                            children: \"Get Free Assessment\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                            lineNumber: 466,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                            href: \"/main/services\",\n                                                                            className: \"btn-default\",\n                                                                            children: \"View our services\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                            lineNumber: 472,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                    lineNumber: 462,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"row\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-lg-12\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"hero-company-slider\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"We're Trusted by more than\",\n                                                                        \" \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"counter\",\n                                                                            children: clients.length\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                            lineNumber: 487,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        \"+ companies\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                    lineNumber: 485,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        ...containerStyle,\n                                                                        cursor: isDragging ? 'grabbing' : 'grab'\n                                                                    },\n                                                                    onMouseDown: handleMouseDown,\n                                                                    onMouseMove: handleMouseMove,\n                                                                    onMouseUp: handleMouseUp,\n                                                                    onMouseLeave: handleMouseLeave,\n                                                                    onTouchStart: handleTouchStart,\n                                                                    onTouchMove: handleTouchMove,\n                                                                    onTouchEnd: handleTouchEnd,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            display: 'flex',\n                                                                            alignItems: 'center',\n                                                                            gap: '20px',\n                                                                            transition: isDragging ? 'none' : 'transform 2s ease-in-out',\n                                                                            transform: \"translateX(-\".concat(scrollLeft, \"px)\")\n                                                                        },\n                                                                        children: renderClientItems()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                        lineNumber: 502,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                    lineNumber: 489,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, index, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 17\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 11\n                    }, undefined),\n                    slidesToShow.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"swiper-pagination hero-pagination\",\n                        style: {\n                            position: 'absolute',\n                            bottom: '50px',\n                            left: '50%',\n                            transform: 'translateX(-50%)',\n                            zIndex: 10,\n                            display: 'flex',\n                            justifyContent: 'center',\n                            alignItems: 'center',\n                            gap: '12px'\n                        },\n                        children: slidesToShow.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"swiper-pagination-bullet \".concat(currentSlide === index ? 'swiper-pagination-bullet-active' : ''),\n                                onClick: ()=>goToSlide(index),\n                                \"aria-label\": \"Go to slide \".concat(index + 1),\n                                style: {\n                                    width: '14px',\n                                    height: '14px',\n                                    borderRadius: '50%',\n                                    border: 'none',\n                                    cursor: 'pointer',\n                                    transition: 'all 0.3s ease',\n                                    background: currentSlide === index ? '#ffffff' : 'rgba(255, 255, 255, 0.5)',\n                                    opacity: currentSlide === index ? 1 : 0.7,\n                                    transform: currentSlide === index ? 'scale(1.2)' : 'scale(1)'\n                                }\n                            }, index, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                lineNumber: 540,\n                                columnNumber: 17\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                        lineNumber: 525,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                lineNumber: 419,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n            lineNumber: 418,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(HomeSliderHero, \"yq4GrymekdYHoSzepb9itP+IO7o=\");\n_c = HomeSliderHero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeSliderHero);\nvar _c;\n$RefreshReg$(_c, \"HomeSliderHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/home/<USER>/HomeSliderHero.tsx\n"));

/***/ })

});