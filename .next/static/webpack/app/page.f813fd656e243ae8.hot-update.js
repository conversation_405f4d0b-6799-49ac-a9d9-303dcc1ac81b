"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/main/home/<USER>/HomeMainHero.tsx":
/*!********************************************************!*\
  !*** ./src/components/main/home/<USER>/HomeMainHero.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n// Style constants\nconst LOGO_SIZE = '60px'; // Increased from 40px to 60px\nconst DRAG_SENSITIVITY = 0.4;\nconst TEXT_WIDTH_MULTIPLIER = 10;\nconst MIN_TEXT_WIDTH = 60;\nconst TEXT_PADDING = 5; // Reduced from 20 to 5\nconst LOGO_GAP = 0; // No gap between logo and client name\nconst logoContainerStyle = {\n    width: LOGO_SIZE,\n    height: LOGO_SIZE,\n    borderRadius: '50%',\n    background: 'linear-gradient(var(--bg-color), var(--bg-color)) padding-box, linear-gradient(to left, var(--accent-color), var(--accent-secondary-color)) border-box',\n    border: '1px solid transparent',\n    padding: '2px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center'\n};\nconst logoStyle = {\n    width: 'calc(100% - 4px)',\n    height: 'calc(100% - 4px)',\n    borderRadius: '50%',\n    backgroundSize: 'cover',\n    backgroundRepeat: 'no-repeat',\n    backgroundPosition: 'center',\n    backgroundColor: 'transparent',\n    overflow: 'hidden'\n};\nconst clientItemStyle = (textLength)=>{\n    const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n    const totalWidth = parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n    return {\n        display: 'flex',\n        alignItems: 'center',\n        gap: \"\".concat(LOGO_GAP, \"px\"),\n        width: \"\".concat(totalWidth, \"px\"),\n        flexShrink: 0,\n        marginRight: '0px',\n        overflow: 'hidden'\n    };\n};\nconst companyNameStyle = {\n    color: 'white',\n    fontSize: '18px',\n    fontWeight: 'normal',\n    fontFamily: 'sans-serif',\n    whiteSpace: 'nowrap',\n    overflow: 'hidden',\n    textOverflow: 'ellipsis',\n    flex: 1\n};\nconst containerStyle = {\n    overflow: 'hidden',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '20px' // Consistent gap between all client blocks\n};\nconst trackStyle = (isDragging, scrollLeft)=>({\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0px',\n        transition: isDragging ? 'none' : 'transform 2s ease-in-out',\n        transform: \"translateX(-\".concat(scrollLeft, \"px)\")\n    });\n// Logo component\nconst ClientLogo = (param)=>{\n    let { logourl } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: logoContainerStyle,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                ...logoStyle,\n                backgroundImage: \"url(\".concat(logourl || \"/images/icon-testimonial-logo.svg\", \")\")\n            }\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n            lineNumber: 86,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n        lineNumber: 85,\n        columnNumber: 3\n    }, undefined);\n};\n_c = ClientLogo;\n// Client item component\nconst ClientItem = (param)=>{\n    let { client, index } = param;\n    const textLength = client.companyname.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: clientItemStyle(textLength),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {\n                logourl: client.logourl\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: companyNameStyle,\n                children: client.companyname\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, client.id || index, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = ClientItem;\n// Helper function to calculate client width\nconst calculateClientWidth = (textLength)=>{\n    const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n    return parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n};\n// Helper function to calculate total width\nconst calculateTotalWidth = (clients)=>{\n    return clients.reduce((sum, client)=>{\n        return sum + calculateClientWidth(client.companyname.length);\n    }, 0);\n};\n// Helper function to calculate scroll position for a specific client index\nconst calculateScrollPositionForClient = (clients, targetIndex)=>{\n    let scrollPosition = 0;\n    for(let i = 0; i < targetIndex && i < clients.length; i++){\n        scrollPosition += calculateClientWidth(clients[i].companyname.length);\n    }\n    return scrollPosition;\n};\nconst HomeMainHero = ()=>{\n    _s();\n    const [clients, setClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startX, setStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentClientIndex, setCurrentClientIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // Track which client is currently visible\n    const [scrollLeft, setScrollLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // Start at beginning\n    const [autoScrollInterval, setAutoScrollInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            const fetchClients = {\n                \"HomeMainHero.useEffect.fetchClients\": async ()=>{\n                    try {\n                        setLoading(true);\n                        // Fetch all active clients without limit, sorted by name\n                        const response = await fetch('/api/clients?limit=0');\n                        const data = await response.json();\n                        if (data.success) {\n                            // Sort clients by company name to ensure alphabetical order\n                            const sortedClients = (data.clients || []).sort({\n                                \"HomeMainHero.useEffect.fetchClients.sortedClients\": (a, b)=>a.companyname.localeCompare(b.companyname)\n                            }[\"HomeMainHero.useEffect.fetchClients.sortedClients\"]);\n                            setClients(sortedClients);\n                            // Start with the first client fully visible\n                            setCurrentClientIndex(0);\n                            setScrollLeft(0);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching clients:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"HomeMainHero.useEffect.fetchClients\"];\n            fetchClients();\n        }\n    }[\"HomeMainHero.useEffect\"], []);\n    // Auto-scroll function - moves one client at a time as complete blocks\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            if (clients.length === 0 || isDragging) return;\n            const interval = setInterval({\n                \"HomeMainHero.useEffect.interval\": ()=>{\n                    setCurrentClientIndex({\n                        \"HomeMainHero.useEffect.interval\": (prevIndex)=>{\n                            const nextIndex = (prevIndex + 1) % clients.length;\n                            // Calculate exact scroll position for the next client\n                            const nextScrollPosition = calculateScrollPositionForClient(clients, nextIndex);\n                            setScrollLeft(nextScrollPosition);\n                            return nextIndex;\n                        }\n                    }[\"HomeMainHero.useEffect.interval\"]);\n                }\n            }[\"HomeMainHero.useEffect.interval\"], 5000);\n            setAutoScrollInterval(interval);\n            return ({\n                \"HomeMainHero.useEffect\": ()=>{\n                    if (interval) {\n                        clearInterval(interval);\n                    }\n                }\n            })[\"HomeMainHero.useEffect\"];\n        }\n    }[\"HomeMainHero.useEffect\"], [\n        clients.length,\n        isDragging\n    ]);\n    // Cleanup interval on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeMainHero.useEffect\": ()=>{\n            return ({\n                \"HomeMainHero.useEffect\": ()=>{\n                    if (autoScrollInterval) {\n                        clearInterval(autoScrollInterval);\n                    }\n                }\n            })[\"HomeMainHero.useEffect\"];\n        }\n    }[\"HomeMainHero.useEffect\"], [\n        autoScrollInterval\n    ]);\n    const handleMouseDown = (e)=>{\n        setIsDragging(true);\n        setStartX(e.pageX);\n        // Stop auto-scroll when user starts dragging\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops dragging\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setCurrentClientIndex((prevIndex)=>{\n                    const nextIndex = (prevIndex + 1) % clients.length;\n                    // Calculate exact scroll position for the next client\n                    const nextScrollPosition = calculateScrollPositionForClient(clients, nextIndex);\n                    setScrollLeft(nextScrollPosition);\n                    return nextIndex;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const handleMouseLeave = ()=>{\n        setIsDragging(false);\n    };\n    const handleTouchStart = (e)=>{\n        setIsDragging(true);\n        setStartX(e.touches[0].pageX);\n        // Stop auto-scroll when user starts touching\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleTouchMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.touches[0].pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleTouchEnd = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops touching\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setCurrentClientIndex((prevIndex)=>{\n                    const nextIndex = (prevIndex + 1) % clients.length;\n                    // Calculate exact scroll position for the next client\n                    const nextScrollPosition = calculateScrollPositionForClient(clients, nextIndex);\n                    setScrollLeft(nextScrollPosition);\n                    return nextIndex;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const renderClientItems = ()=>{\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: clientItemStyle(7),\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            ...companyNameStyle,\n                            fontSize: '26px',\n                            fontWeight: 'bold'\n                        },\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 308,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (clients.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: clientItemStyle(12),\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            ...companyNameStyle,\n                            fontSize: '26px',\n                            fontWeight: 'bold'\n                        },\n                        children: \"No clients yet\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                lineNumber: 319,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, client.id || index, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, undefined)),\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, \"duplicate-\".concat(client.id || index), false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 11\n                    }, undefined))\n            ]\n        }, void 0, true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"hero\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid-lines\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-1\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-2\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-3\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-4\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid-line-5\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                    lineNumber: 346,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row align-items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-content\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"section-title section-title-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"wow fadeInUp\",\n                                                    children: \"Enterprise-grade software & web development solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.2s\",\n                                                    \"data-cursor\": \"-opaque\",\n                                                    children: [\n                                                        \"Advanced digital solutions that\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"accelerate your growth\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.4s\",\n                                                    children: \"Transform your vision into scalable applications and sophisticated web platforms. Professional development services that deliver measurable results.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hero-btn wow fadeInUp\",\n                                            \"data-wow-delay\": \"0.6s\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/contact\",\n                                                    className: \"btn-default btn-highlighted\",\n                                                    children: \"Get Free Assessment\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/services\",\n                                                    className: \"btn-default\",\n                                                    children: \"View our services\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-company-slider\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"We're Trusted by more than \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"counter\",\n                                                    children: clients.length\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 46\n                                                }, undefined),\n                                                \"+ companies\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                ...containerStyle,\n                                                cursor: isDragging ? 'grabbing' : 'grab'\n                                            },\n                                            onMouseDown: handleMouseDown,\n                                            onMouseMove: handleMouseMove,\n                                            onMouseUp: handleMouseUp,\n                                            onMouseLeave: handleMouseLeave,\n                                            onTouchStart: handleTouchStart,\n                                            onTouchMove: handleTouchMove,\n                                            onTouchEnd: handleTouchEnd,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: trackStyle(isDragging, scrollLeft),\n                                                children: renderClientItems()\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n                    lineNumber: 354,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeMainHero.tsx\",\n            lineNumber: 344,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(HomeMainHero, \"A4pwco2drNfheYBR/Lc5tdC5vc0=\");\n_c2 = HomeMainHero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeMainHero);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ClientLogo\");\n$RefreshReg$(_c1, \"ClientItem\");\n$RefreshReg$(_c2, \"HomeMainHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/home/<USER>/HomeMainHero.tsx\n"));

/***/ })

});