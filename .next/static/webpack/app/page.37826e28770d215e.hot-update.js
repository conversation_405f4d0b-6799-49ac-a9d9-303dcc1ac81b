"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/main/home/<USER>/HomeVideoHero.tsx":
/*!*********************************************************!*\
  !*** ./src/components/main/home/<USER>/HomeVideoHero.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst HomeVideoHero = ()=>{\n    _s();\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [videoStatus, setVideoStatus] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('loading');\n    const [videoSrc, setVideoSrc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('/videos/Hero-video.mp4'); // Default fallback\n    const [posterImage, setPosterImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('/images/hero-bg.jpg'); // Default fallback\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [clients, setClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [clientsLoading, setClientsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startX, setStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentClientIndex, setCurrentClientIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // Track which client is currently visible\n    const [scrollLeft, setScrollLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // Start at beginning\n    const [autoScrollInterval, setAutoScrollInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Style constants\n    const LOGO_SIZE = '60px'; // Increased from 40px to 60px\n    const DRAG_SENSITIVITY = 0.4;\n    const TEXT_WIDTH_MULTIPLIER = 10;\n    const MIN_TEXT_WIDTH = 60;\n    const TEXT_PADDING = 5; // Reduced from 20 to 5\n    const LOGO_GAP = 0; // No gap between logo and client name\n    // Helper function to calculate client width\n    const calculateClientWidth = (textLength)=>{\n        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n        return parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n    };\n    // Helper function to calculate total width\n    const calculateTotalWidth = (clients)=>{\n        return clients.reduce((sum, client)=>{\n            return sum + calculateClientWidth(client.companyname.length);\n        }, 0);\n    };\n    // Helper function to calculate scroll position for a specific client index\n    const calculateScrollPositionForClient = (clients, targetIndex)=>{\n        let scrollPosition = 0;\n        for(let i = 0; i < targetIndex && i < clients.length; i++){\n            scrollPosition += calculateClientWidth(clients[i].companyname.length);\n        }\n        return scrollPosition;\n    };\n    const logoContainerStyle = {\n        width: LOGO_SIZE,\n        height: LOGO_SIZE,\n        borderRadius: '50%',\n        background: 'linear-gradient(var(--bg-color), var(--bg-color)) padding-box, linear-gradient(to left, var(--accent-color), var(--accent-secondary-color)) border-box',\n        border: '1px solid transparent',\n        padding: '2px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n    };\n    const logoStyle = {\n        width: 'calc(100% - 4px)',\n        height: 'calc(100% - 4px)',\n        borderRadius: '50%',\n        backgroundSize: 'cover',\n        backgroundRepeat: 'no-repeat',\n        backgroundPosition: 'center',\n        backgroundColor: 'transparent',\n        overflow: 'hidden'\n    };\n    // Logo component\n    const ClientLogo = (param)=>{\n        let { logourl } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: logoContainerStyle,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    ...logoStyle,\n                    backgroundImage: \"url(\".concat(logourl || \"/images/icon-testimonial-logo.svg\", \")\")\n                }\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n            lineNumber: 80,\n            columnNumber: 5\n        }, undefined);\n    };\n    const clientItemStyle = (textLength)=>{\n        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n        const totalWidth = parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n        return {\n            display: 'flex',\n            alignItems: 'center',\n            gap: \"\".concat(LOGO_GAP, \"px\"),\n            width: \"\".concat(totalWidth, \"px\"),\n            flexShrink: 0,\n            marginRight: '0px',\n            overflow: 'hidden'\n        };\n    };\n    const companyNameStyle = {\n        color: 'white',\n        fontSize: '18px',\n        fontWeight: 'normal',\n        fontFamily: 'sans-serif',\n        whiteSpace: 'nowrap',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n        flex: 1\n    };\n    const containerStyle = {\n        overflow: 'hidden',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '20px' // Consistent gap between all client blocks\n    };\n    // Client item component\n    const ClientItem = (param)=>{\n        let { client, index } = param;\n        const textLength = client.companyname.length;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: clientItemStyle(textLength),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {\n                    logourl: client.logourl\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: companyNameStyle,\n                    children: client.companyname\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, client.id || index, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, undefined);\n    };\n    // Fetch hero video settings\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            const fetchHeroVideo = {\n                \"HomeVideoHero.useEffect.fetchHeroVideo\": async ()=>{\n                    try {\n                        const response = await fetch('/api/admin/settings/hero-media');\n                        if (response.ok) {\n                            const data = await response.json();\n                            if (data.success && data.media) {\n                                if (data.media.hero_video_file) {\n                                    setVideoSrc(data.media.hero_video_file);\n                                }\n                                if (data.media.hero_video_poster) {\n                                    setPosterImage(data.media.hero_video_poster);\n                                }\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error fetching hero video:', error);\n                    // Keep default video\n                    }\n                }\n            }[\"HomeVideoHero.useEffect.fetchHeroVideo\"];\n            fetchHeroVideo();\n        }\n    }[\"HomeVideoHero.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            const fetchClients = {\n                \"HomeVideoHero.useEffect.fetchClients\": async ()=>{\n                    try {\n                        setClientsLoading(true);\n                        // Fetch all active clients without limit, sorted by name\n                        const response = await fetch('/api/clients?limit=0');\n                        const data = await response.json();\n                        if (data.success) {\n                            // Sort clients by company name to ensure alphabetical order\n                            const sortedClients = (data.clients || []).sort({\n                                \"HomeVideoHero.useEffect.fetchClients.sortedClients\": (a, b)=>a.companyname.localeCompare(b.companyname)\n                            }[\"HomeVideoHero.useEffect.fetchClients.sortedClients\"]);\n                            setClients(sortedClients);\n                            // Start with the first client fully visible\n                            setCurrentClientIndex(0);\n                            setScrollLeft(0);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching clients:', error);\n                    } finally{\n                        setClientsLoading(false);\n                    }\n                }\n            }[\"HomeVideoHero.useEffect.fetchClients\"];\n            fetchClients();\n        }\n    }[\"HomeVideoHero.useEffect\"], []);\n    // Auto-scroll function - moves one client at a time as complete blocks\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            if (clients.length === 0 || isDragging) return;\n            const interval = setInterval({\n                \"HomeVideoHero.useEffect.interval\": ()=>{\n                    setCurrentClientIndex({\n                        \"HomeVideoHero.useEffect.interval\": (prevIndex)=>{\n                            const nextIndex = (prevIndex + 1) % clients.length;\n                            // Calculate exact scroll position for the next client\n                            const nextScrollPosition = calculateScrollPositionForClient(clients, nextIndex);\n                            setScrollLeft(nextScrollPosition);\n                            return nextIndex;\n                        }\n                    }[\"HomeVideoHero.useEffect.interval\"]);\n                }\n            }[\"HomeVideoHero.useEffect.interval\"], 5000);\n            setAutoScrollInterval(interval);\n            return ({\n                \"HomeVideoHero.useEffect\": ()=>{\n                    if (interval) {\n                        clearInterval(interval);\n                    }\n                }\n            })[\"HomeVideoHero.useEffect\"];\n        }\n    }[\"HomeVideoHero.useEffect\"], [\n        clients.length,\n        isDragging\n    ]);\n    // Cleanup interval on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            return ({\n                \"HomeVideoHero.useEffect\": ()=>{\n                    if (autoScrollInterval) {\n                        clearInterval(autoScrollInterval);\n                    }\n                }\n            })[\"HomeVideoHero.useEffect\"];\n        }\n    }[\"HomeVideoHero.useEffect\"], [\n        autoScrollInterval\n    ]);\n    const handleMouseDown = (e)=>{\n        setIsDragging(true);\n        setStartX(e.pageX);\n        // Stop auto-scroll when user starts dragging\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops dragging\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setCurrentClientIndex((prevIndex)=>{\n                    const nextIndex = (prevIndex + 1) % clients.length;\n                    // Calculate exact scroll position for the next client\n                    const nextScrollPosition = calculateScrollPositionForClient(clients, nextIndex);\n                    setScrollLeft(nextScrollPosition);\n                    return nextIndex;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const handleMouseLeave = ()=>{\n        setIsDragging(false);\n    };\n    const handleTouchStart = (e)=>{\n        setIsDragging(true);\n        setStartX(e.touches[0].pageX);\n        // Stop auto-scroll when user starts touching\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleTouchMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.touches[0].pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleTouchEnd = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops touching\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setCurrentClientIndex((prevIndex)=>{\n                    const nextIndex = (prevIndex + 1) % clients.length;\n                    // Calculate exact scroll position for the next client\n                    const nextScrollPosition = calculateScrollPositionForClient(clients, nextIndex);\n                    setScrollLeft(nextScrollPosition);\n                    return nextIndex;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const renderClientItems = ()=>{\n        if (clientsLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0px',\n                    width: '140px',\n                    flexShrink: 0,\n                    marginRight: '0px',\n                    overflow: 'hidden'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            fontSize: '26px',\n                            fontWeight: 'bold',\n                            fontFamily: 'sans-serif',\n                            whiteSpace: 'nowrap',\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            flex: 1\n                        },\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                lineNumber: 330,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (clients.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0px',\n                    width: '200px',\n                    flexShrink: 0,\n                    marginRight: '0px',\n                    overflow: 'hidden'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            fontSize: '26px',\n                            fontWeight: 'bold',\n                            fontFamily: 'sans-serif',\n                            whiteSpace: 'nowrap',\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            flex: 1\n                        },\n                        children: \"No clients yet\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                lineNumber: 358,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, client.id || index, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 11\n                    }, undefined)),\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, \"duplicate-\".concat(client.id || index), false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 11\n                    }, undefined))\n            ]\n        }, void 0, true);\n    };\n    // Force video element to reload when source changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            const video = videoRef.current;\n            if (video && videoSrc !== '/videos/Hero-video.mp4') {\n                setIsPlaying(false); // Reset playing state\n                video.load(); // This forces the video to reload with the new source\n            }\n        }\n    }[\"HomeVideoHero.useEffect\"], [\n        videoSrc\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            const video = videoRef.current;\n            if (!video) return;\n            // Event handler functions\n            const handleLoadStart = {\n                \"HomeVideoHero.useEffect.handleLoadStart\": ()=>{\n                    setVideoStatus('loading');\n                    setIsPlaying(false);\n                }\n            }[\"HomeVideoHero.useEffect.handleLoadStart\"];\n            const handleLoadedData = {\n                \"HomeVideoHero.useEffect.handleLoadedData\": ()=>setVideoStatus('loaded')\n            }[\"HomeVideoHero.useEffect.handleLoadedData\"];\n            const handleCanPlay = {\n                \"HomeVideoHero.useEffect.handleCanPlay\": ()=>setVideoStatus('canplay')\n            }[\"HomeVideoHero.useEffect.handleCanPlay\"];\n            const handlePlaying = {\n                \"HomeVideoHero.useEffect.handlePlaying\": ()=>{\n                    setVideoStatus('playing');\n                    setIsPlaying(true);\n                }\n            }[\"HomeVideoHero.useEffect.handlePlaying\"];\n            const handleError = {\n                \"HomeVideoHero.useEffect.handleError\": (e)=>{\n                    console.error('Video error:', e);\n                    setVideoStatus('error');\n                    setIsPlaying(false);\n                }\n            }[\"HomeVideoHero.useEffect.handleError\"];\n            // Add event listeners\n            video.addEventListener('loadstart', handleLoadStart);\n            video.addEventListener('loadeddata', handleLoadedData);\n            video.addEventListener('canplay', handleCanPlay);\n            video.addEventListener('playing', handlePlaying);\n            video.addEventListener('error', handleError);\n            // Wait for video to be ready before playing\n            const playVideo = {\n                \"HomeVideoHero.useEffect.playVideo\": ()=>{\n                    if (isPlaying) return; // Prevent multiple play requests\n                    if (video.readyState >= 2) {\n                        setIsPlaying(true);\n                        video.play().catch({\n                            \"HomeVideoHero.useEffect.playVideo\": (error)=>{\n                                console.error('Video autoplay failed:', error);\n                                setIsPlaying(false);\n                            }\n                        }[\"HomeVideoHero.useEffect.playVideo\"]);\n                    } else {\n                        // Wait for video to load\n                        const handleCanPlayOnce = {\n                            \"HomeVideoHero.useEffect.playVideo.handleCanPlayOnce\": ()=>{\n                                if (isPlaying) return; // Prevent multiple play requests\n                                setIsPlaying(true);\n                                video.play().catch({\n                                    \"HomeVideoHero.useEffect.playVideo.handleCanPlayOnce\": (error)=>{\n                                        console.error('Video autoplay failed:', error);\n                                        setIsPlaying(false);\n                                    }\n                                }[\"HomeVideoHero.useEffect.playVideo.handleCanPlayOnce\"]);\n                            }\n                        }[\"HomeVideoHero.useEffect.playVideo.handleCanPlayOnce\"];\n                        video.addEventListener('canplay', handleCanPlayOnce, {\n                            once: true\n                        });\n                    }\n                }\n            }[\"HomeVideoHero.useEffect.playVideo\"];\n            // Small delay to prevent overlapping play requests\n            const playTimeout = setTimeout(playVideo, 100);\n            // Cleanup function\n            return ({\n                \"HomeVideoHero.useEffect\": ()=>{\n                    clearTimeout(playTimeout);\n                    video.removeEventListener('loadstart', handleLoadStart);\n                    video.removeEventListener('loadeddata', handleLoadedData);\n                    video.removeEventListener('canplay', handleCanPlay);\n                    video.removeEventListener('playing', handlePlaying);\n                    video.removeEventListener('error', handleError);\n                }\n            })[\"HomeVideoHero.useEffect\"];\n        }\n    }[\"HomeVideoHero.useEffect\"], [\n        videoSrc\n    ]); // Re-run when video source changes\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"hero hero-bg-image hero-video\",\n            style: {\n                position: 'relative',\n                overflow: 'hidden',\n                backgroundImage: 'url(/images/hero-bg.jpg)',\n                backgroundSize: 'cover',\n                backgroundPosition: 'center',\n                backgroundRepeat: 'no-repeat'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hero-bg-video\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                        ref: videoRef,\n                        autoPlay: true,\n                        muted: true,\n                        loop: true,\n                        playsInline: true,\n                        id: \"myvideo\",\n                        src: videoSrc,\n                        poster: posterImage,\n                        style: {\n                            position: 'absolute',\n                            top: 0,\n                            left: 0,\n                            width: '100%',\n                            height: '100%',\n                            objectFit: 'cover',\n                            zIndex: -1\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                src: videoSrc,\n                                type: \"video/mp4\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Your browser does not support the video tag.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 490,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                    lineNumber: 487,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row align-items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-content\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"section-title section-title-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"wow fadeInUp\",\n                                                    children: \"Enterprise-grade software & web development solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.2s\",\n                                                    \"data-cursor\": \"-opaque\",\n                                                    children: [\n                                                        \"Advanced digital solutions that\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"accelerate your growth\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                            lineNumber: 542,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.4s\",\n                                                    children: \"Transform your vision into scalable applications and sophisticated web platforms. Professional development services that deliver measurable results.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hero-btn wow fadeInUp\",\n                                            \"data-wow-delay\": \"0.6s\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/contact\",\n                                                    className: \"btn-default btn-highlighted\",\n                                                    children: \"Get Free Assessment\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/services\",\n                                                    className: \"btn-default\",\n                                                    children: \"View our services\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                lineNumber: 528,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                            lineNumber: 527,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-company-slider\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"We're Trusted by more than \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"counter\",\n                                                    children: clients.length\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 46\n                                                }, undefined),\n                                                \"+ companies\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                            lineNumber: 568,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                ...containerStyle,\n                                                cursor: isDragging ? 'grabbing' : 'grab'\n                                            },\n                                            onMouseDown: handleMouseDown,\n                                            onMouseMove: handleMouseMove,\n                                            onMouseUp: handleMouseUp,\n                                            onMouseLeave: handleMouseLeave,\n                                            onTouchStart: handleTouchStart,\n                                            onTouchMove: handleTouchMove,\n                                            onTouchEnd: handleTouchEnd,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: '0px',\n                                                    transition: isDragging ? 'none' : 'transform 2s ease-in-out',\n                                                    transform: \"translateX(-\".concat(scrollLeft, \"px)\")\n                                                },\n                                                children: renderClientItems()\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                    lineNumber: 567,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                lineNumber: 565,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                            lineNumber: 564,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                    lineNumber: 526,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n            lineNumber: 475,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(HomeVideoHero, \"C+ltSsjCuyQUglIwCaY+tcnYus0=\");\n_c = HomeVideoHero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeVideoHero);\nvar _c;\n$RefreshReg$(_c, \"HomeVideoHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/home/<USER>/HomeVideoHero.tsx\n"));

/***/ })

});