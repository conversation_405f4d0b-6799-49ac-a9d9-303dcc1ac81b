"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/main/home/<USER>/HomeVideoHero.tsx":
/*!*********************************************************!*\
  !*** ./src/components/main/home/<USER>/HomeVideoHero.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst HomeVideoHero = ()=>{\n    _s();\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [videoStatus, setVideoStatus] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('loading');\n    const [videoSrc, setVideoSrc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('/videos/Hero-video.mp4'); // Default fallback\n    const [posterImage, setPosterImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('/images/hero-bg.jpg'); // Default fallback\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [clients, setClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [clientsLoading, setClientsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startX, setStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentClientIndex, setCurrentClientIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // Track which client is currently visible\n    const [scrollLeft, setScrollLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // Start at beginning\n    const [autoScrollInterval, setAutoScrollInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Style constants\n    const LOGO_SIZE = '60px'; // Increased from 40px to 60px\n    const DRAG_SENSITIVITY = 0.4;\n    const TEXT_WIDTH_MULTIPLIER = 10;\n    const MIN_TEXT_WIDTH = 60;\n    const TEXT_PADDING = 5; // Reduced from 20 to 5\n    const LOGO_GAP = 0; // No gap between logo and client name\n    // Helper function to calculate client width\n    const calculateClientWidth = (textLength)=>{\n        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n        return parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n    };\n    // Helper function to calculate total width\n    const calculateTotalWidth = (clients)=>{\n        return clients.reduce((sum, client)=>{\n            return sum + calculateClientWidth(client.companyname.length);\n        }, 0);\n    };\n    // Helper function to calculate scroll position for a specific client index\n    const calculateScrollPositionForClient = (clients, targetIndex)=>{\n        let scrollPosition = 0;\n        for(let i = 0; i < targetIndex && i < clients.length; i++){\n            scrollPosition += calculateClientWidth(clients[i].companyname.length);\n        }\n        return scrollPosition;\n    };\n    const logoContainerStyle = {\n        width: LOGO_SIZE,\n        height: LOGO_SIZE,\n        borderRadius: '50%',\n        background: 'linear-gradient(var(--bg-color), var(--bg-color)) padding-box, linear-gradient(to left, var(--accent-color), var(--accent-secondary-color)) border-box',\n        border: '1px solid transparent',\n        padding: '2px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n    };\n    const logoStyle = {\n        width: 'calc(100% - 4px)',\n        height: 'calc(100% - 4px)',\n        borderRadius: '50%',\n        backgroundSize: 'cover',\n        backgroundRepeat: 'no-repeat',\n        backgroundPosition: 'center',\n        backgroundColor: 'transparent',\n        overflow: 'hidden'\n    };\n    // Logo component\n    const ClientLogo = (param)=>{\n        let { logourl } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: logoContainerStyle,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    ...logoStyle,\n                    backgroundImage: \"url(\".concat(logourl || \"/images/icon-testimonial-logo.svg\", \")\")\n                }\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n            lineNumber: 80,\n            columnNumber: 5\n        }, undefined);\n    };\n    const clientItemStyle = (textLength)=>{\n        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n        const totalWidth = parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n        return {\n            display: 'flex',\n            alignItems: 'center',\n            gap: \"\".concat(LOGO_GAP, \"px\"),\n            width: \"\".concat(totalWidth, \"px\"),\n            flexShrink: 0,\n            marginRight: '0px',\n            overflow: 'hidden'\n        };\n    };\n    const companyNameStyle = {\n        color: 'white',\n        fontSize: '18px',\n        fontWeight: 'normal',\n        fontFamily: 'sans-serif',\n        whiteSpace: 'nowrap',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n        flex: 1\n    };\n    const containerStyle = {\n        overflow: 'hidden',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '20px' // Consistent gap between all client blocks\n    };\n    // Client item component\n    const ClientItem = (param)=>{\n        let { client, index } = param;\n        const textLength = client.companyname.length;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: clientItemStyle(textLength),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {\n                    logourl: client.logourl\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: companyNameStyle,\n                    children: client.companyname\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, client.id || index, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, undefined);\n    };\n    // Fetch hero video settings\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            const fetchHeroVideo = {\n                \"HomeVideoHero.useEffect.fetchHeroVideo\": async ()=>{\n                    try {\n                        const response = await fetch('/api/admin/settings/hero-media');\n                        if (response.ok) {\n                            const data = await response.json();\n                            if (data.success && data.media) {\n                                if (data.media.hero_video_file) {\n                                    setVideoSrc(data.media.hero_video_file);\n                                }\n                                if (data.media.hero_video_poster) {\n                                    setPosterImage(data.media.hero_video_poster);\n                                }\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error fetching hero video:', error);\n                    // Keep default video\n                    }\n                }\n            }[\"HomeVideoHero.useEffect.fetchHeroVideo\"];\n            fetchHeroVideo();\n        }\n    }[\"HomeVideoHero.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            const fetchClients = {\n                \"HomeVideoHero.useEffect.fetchClients\": async ()=>{\n                    try {\n                        setClientsLoading(true);\n                        // Fetch all active clients without limit, sorted by name\n                        const response = await fetch('/api/clients?limit=0');\n                        const data = await response.json();\n                        if (data.success) {\n                            // Sort clients by company name to ensure alphabetical order\n                            const sortedClients = (data.clients || []).sort({\n                                \"HomeVideoHero.useEffect.fetchClients.sortedClients\": (a, b)=>a.companyname.localeCompare(b.companyname)\n                            }[\"HomeVideoHero.useEffect.fetchClients.sortedClients\"]);\n                            setClients(sortedClients);\n                            // Start with the first client fully visible\n                            setCurrentClientIndex(0);\n                            setScrollLeft(0);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching clients:', error);\n                    } finally{\n                        setClientsLoading(false);\n                    }\n                }\n            }[\"HomeVideoHero.useEffect.fetchClients\"];\n            fetchClients();\n        }\n    }[\"HomeVideoHero.useEffect\"], []);\n    // Auto-scroll function - moves one client at a time as complete blocks\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            if (clients.length === 0 || isDragging) return;\n            const interval = setInterval({\n                \"HomeVideoHero.useEffect.interval\": ()=>{\n                    setCurrentClientIndex({\n                        \"HomeVideoHero.useEffect.interval\": (prevIndex)=>{\n                            const nextIndex = (prevIndex + 1) % clients.length;\n                            // Calculate exact scroll position for the next client\n                            const nextScrollPosition = calculateScrollPositionForClient(clients, nextIndex);\n                            setScrollLeft(nextScrollPosition);\n                            return nextIndex;\n                        }\n                    }[\"HomeVideoHero.useEffect.interval\"]);\n                }\n            }[\"HomeVideoHero.useEffect.interval\"], 5000);\n            setAutoScrollInterval(interval);\n            return ({\n                \"HomeVideoHero.useEffect\": ()=>{\n                    if (interval) {\n                        clearInterval(interval);\n                    }\n                }\n            })[\"HomeVideoHero.useEffect\"];\n        }\n    }[\"HomeVideoHero.useEffect\"], [\n        clients.length,\n        isDragging\n    ]);\n    // Cleanup interval on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            return ({\n                \"HomeVideoHero.useEffect\": ()=>{\n                    if (autoScrollInterval) {\n                        clearInterval(autoScrollInterval);\n                    }\n                }\n            })[\"HomeVideoHero.useEffect\"];\n        }\n    }[\"HomeVideoHero.useEffect\"], [\n        autoScrollInterval\n    ]);\n    const handleMouseDown = (e)=>{\n        setIsDragging(true);\n        setStartX(e.pageX);\n        // Stop auto-scroll when user starts dragging\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops dragging\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setCurrentClientIndex((prevIndex)=>{\n                    const nextIndex = (prevIndex + 1) % clients.length;\n                    // Calculate exact scroll position for the next client\n                    const nextScrollPosition = calculateScrollPositionForClient(clients, nextIndex);\n                    setScrollLeft(nextScrollPosition);\n                    return nextIndex;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const handleMouseLeave = ()=>{\n        setIsDragging(false);\n    };\n    const handleTouchStart = (e)=>{\n        setIsDragging(true);\n        setStartX(e.touches[0].pageX);\n        // Stop auto-scroll when user starts touching\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleTouchMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.touches[0].pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleTouchEnd = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops touching\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    const totalWidth = calculateTotalWidth(clients);\n                    const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                    let newScrollLeft = prevScrollLeft + firstClientWidth;\n                    if (newScrollLeft >= totalWidth) {\n                        newScrollLeft = firstClientWidth;\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const renderClientItems = ()=>{\n        if (clientsLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0px',\n                    width: '140px',\n                    flexShrink: 0,\n                    marginRight: '0px',\n                    overflow: 'hidden'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            fontSize: '26px',\n                            fontWeight: 'bold',\n                            fontFamily: 'sans-serif',\n                            whiteSpace: 'nowrap',\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            flex: 1\n                        },\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                lineNumber: 333,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (clients.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0px',\n                    width: '200px',\n                    flexShrink: 0,\n                    marginRight: '0px',\n                    overflow: 'hidden'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            fontSize: '26px',\n                            fontWeight: 'bold',\n                            fontFamily: 'sans-serif',\n                            whiteSpace: 'nowrap',\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            flex: 1\n                        },\n                        children: \"No clients yet\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                lineNumber: 361,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, client.id || index, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 11\n                    }, undefined)),\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, \"duplicate-\".concat(client.id || index), false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 11\n                    }, undefined))\n            ]\n        }, void 0, true);\n    };\n    // Force video element to reload when source changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            const video = videoRef.current;\n            if (video && videoSrc !== '/videos/Hero-video.mp4') {\n                setIsPlaying(false); // Reset playing state\n                video.load(); // This forces the video to reload with the new source\n            }\n        }\n    }[\"HomeVideoHero.useEffect\"], [\n        videoSrc\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            const video = videoRef.current;\n            if (!video) return;\n            // Event handler functions\n            const handleLoadStart = {\n                \"HomeVideoHero.useEffect.handleLoadStart\": ()=>{\n                    setVideoStatus('loading');\n                    setIsPlaying(false);\n                }\n            }[\"HomeVideoHero.useEffect.handleLoadStart\"];\n            const handleLoadedData = {\n                \"HomeVideoHero.useEffect.handleLoadedData\": ()=>setVideoStatus('loaded')\n            }[\"HomeVideoHero.useEffect.handleLoadedData\"];\n            const handleCanPlay = {\n                \"HomeVideoHero.useEffect.handleCanPlay\": ()=>setVideoStatus('canplay')\n            }[\"HomeVideoHero.useEffect.handleCanPlay\"];\n            const handlePlaying = {\n                \"HomeVideoHero.useEffect.handlePlaying\": ()=>{\n                    setVideoStatus('playing');\n                    setIsPlaying(true);\n                }\n            }[\"HomeVideoHero.useEffect.handlePlaying\"];\n            const handleError = {\n                \"HomeVideoHero.useEffect.handleError\": (e)=>{\n                    console.error('Video error:', e);\n                    setVideoStatus('error');\n                    setIsPlaying(false);\n                }\n            }[\"HomeVideoHero.useEffect.handleError\"];\n            // Add event listeners\n            video.addEventListener('loadstart', handleLoadStart);\n            video.addEventListener('loadeddata', handleLoadedData);\n            video.addEventListener('canplay', handleCanPlay);\n            video.addEventListener('playing', handlePlaying);\n            video.addEventListener('error', handleError);\n            // Wait for video to be ready before playing\n            const playVideo = {\n                \"HomeVideoHero.useEffect.playVideo\": ()=>{\n                    if (isPlaying) return; // Prevent multiple play requests\n                    if (video.readyState >= 2) {\n                        setIsPlaying(true);\n                        video.play().catch({\n                            \"HomeVideoHero.useEffect.playVideo\": (error)=>{\n                                console.error('Video autoplay failed:', error);\n                                setIsPlaying(false);\n                            }\n                        }[\"HomeVideoHero.useEffect.playVideo\"]);\n                    } else {\n                        // Wait for video to load\n                        const handleCanPlayOnce = {\n                            \"HomeVideoHero.useEffect.playVideo.handleCanPlayOnce\": ()=>{\n                                if (isPlaying) return; // Prevent multiple play requests\n                                setIsPlaying(true);\n                                video.play().catch({\n                                    \"HomeVideoHero.useEffect.playVideo.handleCanPlayOnce\": (error)=>{\n                                        console.error('Video autoplay failed:', error);\n                                        setIsPlaying(false);\n                                    }\n                                }[\"HomeVideoHero.useEffect.playVideo.handleCanPlayOnce\"]);\n                            }\n                        }[\"HomeVideoHero.useEffect.playVideo.handleCanPlayOnce\"];\n                        video.addEventListener('canplay', handleCanPlayOnce, {\n                            once: true\n                        });\n                    }\n                }\n            }[\"HomeVideoHero.useEffect.playVideo\"];\n            // Small delay to prevent overlapping play requests\n            const playTimeout = setTimeout(playVideo, 100);\n            // Cleanup function\n            return ({\n                \"HomeVideoHero.useEffect\": ()=>{\n                    clearTimeout(playTimeout);\n                    video.removeEventListener('loadstart', handleLoadStart);\n                    video.removeEventListener('loadeddata', handleLoadedData);\n                    video.removeEventListener('canplay', handleCanPlay);\n                    video.removeEventListener('playing', handlePlaying);\n                    video.removeEventListener('error', handleError);\n                }\n            })[\"HomeVideoHero.useEffect\"];\n        }\n    }[\"HomeVideoHero.useEffect\"], [\n        videoSrc\n    ]); // Re-run when video source changes\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"hero hero-bg-image hero-video\",\n            style: {\n                position: 'relative',\n                overflow: 'hidden',\n                backgroundImage: 'url(/images/hero-bg.jpg)',\n                backgroundSize: 'cover',\n                backgroundPosition: 'center',\n                backgroundRepeat: 'no-repeat'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hero-bg-video\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                        ref: videoRef,\n                        autoPlay: true,\n                        muted: true,\n                        loop: true,\n                        playsInline: true,\n                        id: \"myvideo\",\n                        src: videoSrc,\n                        poster: posterImage,\n                        style: {\n                            position: 'absolute',\n                            top: 0,\n                            left: 0,\n                            width: '100%',\n                            height: '100%',\n                            objectFit: 'cover',\n                            zIndex: -1\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                src: videoSrc,\n                                type: \"video/mp4\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Your browser does not support the video tag.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 493,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                    lineNumber: 490,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row align-items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-content\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"section-title section-title-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"wow fadeInUp\",\n                                                    children: \"Enterprise-grade software & web development solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.2s\",\n                                                    \"data-cursor\": \"-opaque\",\n                                                    children: [\n                                                        \"Advanced digital solutions that\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"accelerate your growth\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.4s\",\n                                                    children: \"Transform your vision into scalable applications and sophisticated web platforms. Professional development services that deliver measurable results.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hero-btn wow fadeInUp\",\n                                            \"data-wow-delay\": \"0.6s\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/contact\",\n                                                    className: \"btn-default btn-highlighted\",\n                                                    children: \"Get Free Assessment\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/services\",\n                                                    className: \"btn-default\",\n                                                    children: \"View our services\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                            lineNumber: 554,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                lineNumber: 531,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                            lineNumber: 530,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-company-slider\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"We're Trusted by more than \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"counter\",\n                                                    children: clients.length\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 46\n                                                }, undefined),\n                                                \"+ companies\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                overflow: 'hidden',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0px',\n                                                position: 'relative',\n                                                cursor: isDragging ? 'grabbing' : 'grab'\n                                            },\n                                            onMouseDown: handleMouseDown,\n                                            onMouseMove: handleMouseMove,\n                                            onMouseUp: handleMouseUp,\n                                            onMouseLeave: handleMouseLeave,\n                                            onTouchStart: handleTouchStart,\n                                            onTouchMove: handleTouchMove,\n                                            onTouchEnd: handleTouchEnd,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: '0px',\n                                                    transition: isDragging ? 'none' : 'transform 2s ease-in-out',\n                                                    transform: \"translateX(-\".concat(scrollLeft, \"px)\")\n                                                },\n                                                children: renderClientItems()\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                lineNumber: 592,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                            lineNumber: 575,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                lineNumber: 568,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                            lineNumber: 567,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                    lineNumber: 529,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n            lineNumber: 478,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(HomeVideoHero, \"C+ltSsjCuyQUglIwCaY+tcnYus0=\");\n_c = HomeVideoHero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeVideoHero);\nvar _c;\n$RefreshReg$(_c, \"HomeVideoHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/home/<USER>/HomeVideoHero.tsx\n"));

/***/ })

});