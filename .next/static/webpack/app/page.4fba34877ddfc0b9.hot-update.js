"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/main/home/<USER>/HomeVideoHero.tsx":
/*!*********************************************************!*\
  !*** ./src/components/main/home/<USER>/HomeVideoHero.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst HomeVideoHero = ()=>{\n    _s();\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [videoStatus, setVideoStatus] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('loading');\n    const [videoSrc, setVideoSrc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('/videos/Hero-video.mp4'); // Default fallback\n    const [posterImage, setPosterImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('/images/hero-bg.jpg'); // Default fallback\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [clients, setClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [clientsLoading, setClientsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startX, setStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [scrollLeft, setScrollLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [autoScrollInterval, setAutoScrollInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Style constants\n    const LOGO_SIZE = '40px';\n    const DRAG_SENSITIVITY = 0.4;\n    const TEXT_WIDTH_MULTIPLIER = 10;\n    const MIN_TEXT_WIDTH = 60;\n    const TEXT_PADDING = 20;\n    const LOGO_GAP = 0;\n    // Helper function to calculate client width\n    const calculateClientWidth = (textLength)=>{\n        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n        return parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n    };\n    // Helper function to calculate total width\n    const calculateTotalWidth = (clients)=>{\n        return clients.reduce((sum, client)=>{\n            return sum + calculateClientWidth(client.companyname.length);\n        }, 0);\n    };\n    // Logo component\n    const ClientLogo = (param)=>{\n        let { logourl } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                width: LOGO_SIZE,\n                height: LOGO_SIZE,\n                borderRadius: '50%',\n                backgroundImage: \"url(\".concat(logourl || \"/images/icon-testimonial-logo.svg\", \")\"),\n                backgroundSize: '70%',\n                backgroundRepeat: 'no-repeat',\n                backgroundPosition: 'center',\n                backgroundColor: 'transparent',\n                overflow: 'hidden',\n                background: 'linear-gradient(transparent, transparent) padding-box, linear-gradient(to left, var(--accent-color), var(--accent-secondary-color)) border-box',\n                border: '1px solid transparent',\n                position: 'relative'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'absolute',\n                    top: 0,\n                    bottom: 0,\n                    left: '-15%',\n                    right: 0,\n                    width: 0,\n                    height: '106%',\n                    background: 'linear-gradient(to left, var(--accent-color) 0%, var(--accent-secondary-color) 100%)',\n                    transform: 'skew(45deg)',\n                    transition: 'all 0.4s ease-in-out',\n                    zIndex: -1\n                }\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n            lineNumber: 41,\n            columnNumber: 5\n        }, undefined);\n    };\n    // Client item component\n    const ClientItem = (param)=>{\n        let { client, index } = param;\n        const textLength = client.companyname.length;\n        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n        const totalWidth = parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: \"\".concat(LOGO_GAP, \"px\"),\n                width: \"\".concat(totalWidth, \"px\"),\n                flexShrink: 0,\n                marginRight: '0px',\n                overflow: 'hidden'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {\n                    logourl: client.logourl\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        color: 'white',\n                        fontSize: '18px',\n                        fontWeight: 'normal',\n                        fontFamily: 'sans-serif',\n                        whiteSpace: 'nowrap',\n                        overflow: 'hidden',\n                        textOverflow: 'ellipsis',\n                        flex: 1\n                    },\n                    children: client.companyname\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, client.id || index, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, undefined);\n    };\n    // Fetch hero video settings\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            const fetchHeroVideo = {\n                \"HomeVideoHero.useEffect.fetchHeroVideo\": async ()=>{\n                    try {\n                        const response = await fetch('/api/admin/settings/hero-media');\n                        if (response.ok) {\n                            const data = await response.json();\n                            if (data.success && data.media) {\n                                if (data.media.hero_video_file) {\n                                    setVideoSrc(data.media.hero_video_file);\n                                }\n                                if (data.media.hero_video_poster) {\n                                    setPosterImage(data.media.hero_video_poster);\n                                }\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error fetching hero video:', error);\n                    // Keep default video\n                    }\n                }\n            }[\"HomeVideoHero.useEffect.fetchHeroVideo\"];\n            fetchHeroVideo();\n        }\n    }[\"HomeVideoHero.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            const fetchClients = {\n                \"HomeVideoHero.useEffect.fetchClients\": async ()=>{\n                    try {\n                        setClientsLoading(true);\n                        const response = await fetch('/api/clients');\n                        const data = await response.json();\n                        if (data.success) {\n                            setClients(data.clients || []);\n                            // Set initial scroll position to hide first client\n                            if (data.clients && data.clients.length > 0) {\n                                const firstClientWidth = calculateClientWidth(data.clients[0].companyname.length);\n                                setScrollLeft(firstClientWidth);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error fetching clients:', error);\n                    } finally{\n                        setClientsLoading(false);\n                    }\n                }\n            }[\"HomeVideoHero.useEffect.fetchClients\"];\n            fetchClients();\n        }\n    }[\"HomeVideoHero.useEffect\"], []);\n    // Auto-scroll function\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            if (clients.length === 0 || isDragging) return;\n            const interval = setInterval({\n                \"HomeVideoHero.useEffect.interval\": ()=>{\n                    setScrollLeft({\n                        \"HomeVideoHero.useEffect.interval\": (prevScrollLeft)=>{\n                            const totalWidth = calculateTotalWidth(clients);\n                            const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                            let newScrollLeft = prevScrollLeft + firstClientWidth;\n                            if (newScrollLeft >= totalWidth) {\n                                newScrollLeft = firstClientWidth;\n                            }\n                            return newScrollLeft;\n                        }\n                    }[\"HomeVideoHero.useEffect.interval\"]);\n                }\n            }[\"HomeVideoHero.useEffect.interval\"], 5000);\n            setAutoScrollInterval(interval);\n            return ({\n                \"HomeVideoHero.useEffect\": ()=>{\n                    if (interval) {\n                        clearInterval(interval);\n                    }\n                }\n            })[\"HomeVideoHero.useEffect\"];\n        }\n    }[\"HomeVideoHero.useEffect\"], [\n        clients.length,\n        isDragging\n    ]);\n    // Cleanup interval on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            return ({\n                \"HomeVideoHero.useEffect\": ()=>{\n                    if (autoScrollInterval) {\n                        clearInterval(autoScrollInterval);\n                    }\n                }\n            })[\"HomeVideoHero.useEffect\"];\n        }\n    }[\"HomeVideoHero.useEffect\"], [\n        autoScrollInterval\n    ]);\n    const handleMouseDown = (e)=>{\n        setIsDragging(true);\n        setStartX(e.pageX);\n        // Stop auto-scroll when user starts dragging\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops dragging\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    const totalWidth = calculateTotalWidth(clients);\n                    const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                    let newScrollLeft = prevScrollLeft + firstClientWidth;\n                    if (newScrollLeft >= totalWidth) {\n                        newScrollLeft = firstClientWidth;\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const handleMouseLeave = ()=>{\n        setIsDragging(false);\n    };\n    const handleTouchStart = (e)=>{\n        setIsDragging(true);\n        setStartX(e.touches[0].pageX);\n        // Stop auto-scroll when user starts touching\n        if (autoScrollInterval) {\n            clearInterval(autoScrollInterval);\n            setAutoScrollInterval(null);\n        }\n    };\n    const handleTouchMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const x = e.touches[0].pageX;\n        const walk = (x - startX) * DRAG_SENSITIVITY;\n        let newScrollLeft = scrollLeft - walk;\n        const totalWidth = calculateTotalWidth(clients);\n        // Loop back to beginning when reaching the end\n        if (newScrollLeft >= totalWidth) {\n            var _clients_;\n            const firstClientWidth = calculateClientWidth(((_clients_ = clients[0]) === null || _clients_ === void 0 ? void 0 : _clients_.companyname.length) || 0);\n            newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;\n        } else if (newScrollLeft < 0) {\n            newScrollLeft = totalWidth + newScrollLeft;\n        }\n        setScrollLeft(newScrollLeft);\n    };\n    const handleTouchEnd = ()=>{\n        setIsDragging(false);\n        // Restart auto-scroll after user stops touching\n        if (clients.length > 0) {\n            const interval = setInterval(()=>{\n                setScrollLeft((prevScrollLeft)=>{\n                    const totalWidth = calculateTotalWidth(clients);\n                    const firstClientWidth = calculateClientWidth(clients[0].companyname.length);\n                    let newScrollLeft = prevScrollLeft + firstClientWidth;\n                    if (newScrollLeft >= totalWidth) {\n                        newScrollLeft = firstClientWidth;\n                    }\n                    return newScrollLeft;\n                });\n            }, 5000);\n            setAutoScrollInterval(interval);\n        }\n    };\n    const renderClientItems = ()=>{\n        if (clientsLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0px',\n                    width: '140px',\n                    flexShrink: 0,\n                    marginRight: '0px',\n                    overflow: 'hidden'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            fontSize: '26px',\n                            fontWeight: 'bold',\n                            fontFamily: 'sans-serif',\n                            whiteSpace: 'nowrap',\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            flex: 1\n                        },\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                lineNumber: 306,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (clients.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0px',\n                    width: '200px',\n                    flexShrink: 0,\n                    marginRight: '0px',\n                    overflow: 'hidden'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {}, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            fontSize: '26px',\n                            fontWeight: 'bold',\n                            fontFamily: 'sans-serif',\n                            whiteSpace: 'nowrap',\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            flex: 1\n                        },\n                        children: \"No clients yet\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                lineNumber: 334,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, client.id || index, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 11\n                    }, undefined)),\n                clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientItem, {\n                        client: client,\n                        index: index\n                    }, \"duplicate-\".concat(client.id || index), false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 11\n                    }, undefined))\n            ]\n        }, void 0, true);\n    };\n    // Force video element to reload when source changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            const video = videoRef.current;\n            if (video && videoSrc !== '/videos/Hero-video.mp4') {\n                setIsPlaying(false); // Reset playing state\n                video.load(); // This forces the video to reload with the new source\n            }\n        }\n    }[\"HomeVideoHero.useEffect\"], [\n        videoSrc\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeVideoHero.useEffect\": ()=>{\n            const video = videoRef.current;\n            if (!video) return;\n            // Event handler functions\n            const handleLoadStart = {\n                \"HomeVideoHero.useEffect.handleLoadStart\": ()=>{\n                    setVideoStatus('loading');\n                    setIsPlaying(false);\n                }\n            }[\"HomeVideoHero.useEffect.handleLoadStart\"];\n            const handleLoadedData = {\n                \"HomeVideoHero.useEffect.handleLoadedData\": ()=>setVideoStatus('loaded')\n            }[\"HomeVideoHero.useEffect.handleLoadedData\"];\n            const handleCanPlay = {\n                \"HomeVideoHero.useEffect.handleCanPlay\": ()=>setVideoStatus('canplay')\n            }[\"HomeVideoHero.useEffect.handleCanPlay\"];\n            const handlePlaying = {\n                \"HomeVideoHero.useEffect.handlePlaying\": ()=>{\n                    setVideoStatus('playing');\n                    setIsPlaying(true);\n                }\n            }[\"HomeVideoHero.useEffect.handlePlaying\"];\n            const handleError = {\n                \"HomeVideoHero.useEffect.handleError\": (e)=>{\n                    console.error('Video error:', e);\n                    setVideoStatus('error');\n                    setIsPlaying(false);\n                }\n            }[\"HomeVideoHero.useEffect.handleError\"];\n            // Add event listeners\n            video.addEventListener('loadstart', handleLoadStart);\n            video.addEventListener('loadeddata', handleLoadedData);\n            video.addEventListener('canplay', handleCanPlay);\n            video.addEventListener('playing', handlePlaying);\n            video.addEventListener('error', handleError);\n            // Wait for video to be ready before playing\n            const playVideo = {\n                \"HomeVideoHero.useEffect.playVideo\": ()=>{\n                    if (isPlaying) return; // Prevent multiple play requests\n                    if (video.readyState >= 2) {\n                        setIsPlaying(true);\n                        video.play().catch({\n                            \"HomeVideoHero.useEffect.playVideo\": (error)=>{\n                                console.error('Video autoplay failed:', error);\n                                setIsPlaying(false);\n                            }\n                        }[\"HomeVideoHero.useEffect.playVideo\"]);\n                    } else {\n                        // Wait for video to load\n                        const handleCanPlayOnce = {\n                            \"HomeVideoHero.useEffect.playVideo.handleCanPlayOnce\": ()=>{\n                                if (isPlaying) return; // Prevent multiple play requests\n                                setIsPlaying(true);\n                                video.play().catch({\n                                    \"HomeVideoHero.useEffect.playVideo.handleCanPlayOnce\": (error)=>{\n                                        console.error('Video autoplay failed:', error);\n                                        setIsPlaying(false);\n                                    }\n                                }[\"HomeVideoHero.useEffect.playVideo.handleCanPlayOnce\"]);\n                            }\n                        }[\"HomeVideoHero.useEffect.playVideo.handleCanPlayOnce\"];\n                        video.addEventListener('canplay', handleCanPlayOnce, {\n                            once: true\n                        });\n                    }\n                }\n            }[\"HomeVideoHero.useEffect.playVideo\"];\n            // Small delay to prevent overlapping play requests\n            const playTimeout = setTimeout(playVideo, 100);\n            // Cleanup function\n            return ({\n                \"HomeVideoHero.useEffect\": ()=>{\n                    clearTimeout(playTimeout);\n                    video.removeEventListener('loadstart', handleLoadStart);\n                    video.removeEventListener('loadeddata', handleLoadedData);\n                    video.removeEventListener('canplay', handleCanPlay);\n                    video.removeEventListener('playing', handlePlaying);\n                    video.removeEventListener('error', handleError);\n                }\n            })[\"HomeVideoHero.useEffect\"];\n        }\n    }[\"HomeVideoHero.useEffect\"], [\n        videoSrc\n    ]); // Re-run when video source changes\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"hero hero-bg-image hero-video\",\n            style: {\n                position: 'relative',\n                overflow: 'hidden',\n                backgroundImage: 'url(/images/hero-bg.jpg)',\n                backgroundSize: 'cover',\n                backgroundPosition: 'center',\n                backgroundRepeat: 'no-repeat'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hero-bg-video\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                        ref: videoRef,\n                        autoPlay: true,\n                        muted: true,\n                        loop: true,\n                        playsInline: true,\n                        id: \"myvideo\",\n                        src: videoSrc,\n                        poster: posterImage,\n                        style: {\n                            position: 'absolute',\n                            top: 0,\n                            left: 0,\n                            width: '100%',\n                            height: '100%',\n                            objectFit: 'cover',\n                            zIndex: -1\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                src: videoSrc,\n                                type: \"video/mp4\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Your browser does not support the video tag.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                    lineNumber: 463,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row align-items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-content\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"section-title section-title-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"wow fadeInUp\",\n                                                    children: \"Enterprise-grade software & web development solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.2s\",\n                                                    \"data-cursor\": \"-opaque\",\n                                                    children: [\n                                                        \"Advanced digital solutions that\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"accelerate your growth\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"wow fadeInUp\",\n                                                    \"data-wow-delay\": \"0.4s\",\n                                                    children: \"Transform your vision into scalable applications and sophisticated web platforms. Professional development services that deliver measurable results.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hero-btn wow fadeInUp\",\n                                            \"data-wow-delay\": \"0.6s\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/contact\",\n                                                    className: \"btn-default btn-highlighted\",\n                                                    children: \"Get Free Assessment\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/main/services\",\n                                                    className: \"btn-default\",\n                                                    children: \"View our services\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                lineNumber: 504,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                            lineNumber: 503,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-company-slider\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"We're Trusted by more than \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"counter\",\n                                                    children: clients.length\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 46\n                                                }, undefined),\n                                                \"+ companies\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                            lineNumber: 544,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"swiper\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"swiper-wrapper\",\n                                                children: clientsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"swiper-slide\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"company-logo\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                color: 'white',\n                                                                fontSize: '14px'\n                                                            },\n                                                            children: \"Loading...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 23\n                                                }, undefined) : clients.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"swiper-slide\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"company-logo\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                color: 'white',\n                                                                fontSize: '14px'\n                                                            },\n                                                            children: \"No clients available\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                            lineNumber: 559,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 23\n                                                }, undefined) : clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"swiper-slide\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"company-logo\",\n                                                            style: {\n                                                                display: 'flex',\n                                                                alignItems: 'center',\n                                                                justifyContent: 'flex-start',\n                                                                gap: '12px',\n                                                                padding: '10px',\n                                                                minWidth: '200px',\n                                                                whiteSpace: 'nowrap'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        width: '70px',\n                                                                        height: '70px',\n                                                                        borderRadius: '50%',\n                                                                        overflow: 'hidden',\n                                                                        display: 'flex',\n                                                                        alignItems: 'center',\n                                                                        justifyContent: 'center',\n                                                                        flexShrink: 0,\n                                                                        backgroundColor: 'transparent'\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: client.logourl || \"/images/icon-testimonial-logo.svg\",\n                                                                        alt: client.companyname,\n                                                                        style: {\n                                                                            width: '100%',\n                                                                            height: '100%',\n                                                                            objectFit: 'cover',\n                                                                            borderRadius: '50%'\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                                        lineNumber: 585,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                                    lineNumber: 574,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        color: 'white',\n                                                                        fontSize: '26px',\n                                                                        fontWeight: 'bold',\n                                                                        fontFamily: 'sans-serif',\n                                                                        whiteSpace: 'nowrap',\n                                                                        overflow: 'hidden',\n                                                                        textOverflow: 'ellipsis',\n                                                                        maxWidth: '150px'\n                                                                    },\n                                                                    children: client.companyname\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                                    lineNumber: 596,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                            lineNumber: 565,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, client.id || index, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                        lineNumber: 564,\n                                                        columnNumber: 25\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                                lineNumber: 549,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                    lineNumber: 543,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                            lineNumber: 540,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n                    lineNumber: 502,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeVideoHero.tsx\",\n            lineNumber: 451,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(HomeVideoHero, \"Zz/tb/zSSEIaeCPsoKrrYmps3bI=\");\n_c = HomeVideoHero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeVideoHero);\nvar _c;\n$RefreshReg$(_c, \"HomeVideoHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/home/<USER>/HomeVideoHero.tsx\n"));

/***/ })

});