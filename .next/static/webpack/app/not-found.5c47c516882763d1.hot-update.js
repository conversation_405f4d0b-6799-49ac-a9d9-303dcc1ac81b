"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/not-found",{

/***/ "(app-pages-browser)/./src/components/main/shared/ScrollingTicker.tsx":
/*!********************************************************!*\
  !*** ./src/components/main/shared/ScrollingTicker.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScrollingTicker)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// Fallback industries in case API fails or no featured services\nconst fallbackIndustries = [\n    'Healthcare',\n    'Finance and Banking',\n    'Legal and Law Firms',\n    'Government and Public Sector',\n    'Technology and Software'\n];\nfunction ScrollingTicker() {\n    _s();\n    const [services, setServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [displayItems, setDisplayItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(fallbackIndustries);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollingTicker.useEffect\": ()=>{\n            const fetchFeaturedServices = {\n                \"ScrollingTicker.useEffect.fetchFeaturedServices\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const response = await fetch('/api/services/featured');\n                        const data = await response.json();\n                        if (data.success && data.services && data.services.length > 0) {\n                            setServices(data.services);\n                            // Extract service names for display\n                            const serviceNames = data.services.map({\n                                \"ScrollingTicker.useEffect.fetchFeaturedServices.serviceNames\": (service)=>service.name\n                            }[\"ScrollingTicker.useEffect.fetchFeaturedServices.serviceNames\"]);\n                            setDisplayItems(serviceNames);\n                        } else {\n                            // Use fallback if no featured services\n                            setDisplayItems(fallbackIndustries);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching featured services:', error);\n                        // Use fallback on error\n                        setDisplayItems(fallbackIndustries);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"ScrollingTicker.useEffect.fetchFeaturedServices\"];\n            fetchFeaturedServices();\n        }\n    }[\"ScrollingTicker.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"our-scrolling-ticker\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"scrolling-ticker-box\",\n                style: {\n                    overflow: 'hidden'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"scrolling-content\",\n                        children: [\n                            displayItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: \"/images/star-icon.svg\",\n                                            alt: \"Star\",\n                                            width: 20,\n                                            height: 20,\n                                            style: {\n                                                display: 'inline-block',\n                                                verticalAlign: 'middle',\n                                                marginRight: '8px'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 17\n                                        }, this),\n                                        item\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 15\n                                }, this)),\n                            displayItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: \"/images/star-icon.svg\",\n                                            alt: \"Star\",\n                                            width: 20,\n                                            height: 20,\n                                            style: {\n                                                display: 'inline-block',\n                                                verticalAlign: 'middle',\n                                                marginRight: '8px'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 17\n                                        }, this),\n                                        item\n                                    ]\n                                }, \"duplicate-\".concat(index), true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"scrolling-content\",\n                        children: [\n                            displayItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: \"/images/star-icon.svg\",\n                                            alt: \"Star\",\n                                            width: 20,\n                                            height: 20,\n                                            style: {\n                                                display: 'inline-block',\n                                                verticalAlign: 'middle',\n                                                marginRight: '8px'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this),\n                                        item\n                                    ]\n                                }, \"second-\".concat(index), true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, this)),\n                            displayItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: \"/images/star-icon.svg\",\n                                            alt: \"Star\",\n                                            width: 20,\n                                            height: 20,\n                                            style: {\n                                                display: 'inline-block',\n                                                verticalAlign: 'middle',\n                                                marginRight: '8px'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 17\n                                        }, this),\n                                        item\n                                    ]\n                                }, \"second-duplicate-\".concat(index), true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/shared/ScrollingTicker.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_s(ScrollingTicker, \"ja0wsF7kOaRoZE6wPTT6QijWYyQ=\");\n_c = ScrollingTicker;\nvar _c;\n$RefreshReg$(_c, \"ScrollingTicker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/shared/ScrollingTicker.tsx\n"));

/***/ })

});