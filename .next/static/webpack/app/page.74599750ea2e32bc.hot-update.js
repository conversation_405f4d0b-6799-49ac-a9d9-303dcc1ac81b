"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/main/home/<USER>/HomeSliderHero.tsx":
/*!**********************************************************!*\
  !*** ./src/components/main/home/<USER>/HomeSliderHero.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst HomeSliderHero = ()=>{\n    _s();\n    const [sliderImages, setSliderImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [clients, setClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [clientsLoading, setClientsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startX, setStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [scrollLeft, setScrollLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [autoScrollInterval, setAutoScrollInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Style constants\n    const LOGO_SIZE = '40px';\n    const DRAG_SENSITIVITY = 0.4;\n    const TEXT_WIDTH_MULTIPLIER = 10;\n    const MIN_TEXT_WIDTH = 60;\n    const TEXT_PADDING = 20;\n    const LOGO_GAP = 0;\n    // Helper function to calculate client width\n    const calculateClientWidth = (textLength)=>{\n        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n        return parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n    };\n    // Helper function to calculate total width\n    const calculateTotalWidth = (clients)=>{\n        return clients.reduce((sum, client)=>{\n            return sum + calculateClientWidth(client.companyname.length);\n        }, 0);\n    };\n    // Logo component\n    const ClientLogo = (param)=>{\n        let { logourl } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                width: LOGO_SIZE,\n                height: LOGO_SIZE,\n                borderRadius: '50%',\n                backgroundImage: \"url(\".concat(logourl || \"/images/icon-testimonial-logo.svg\", \")\"),\n                backgroundSize: '70%',\n                backgroundRepeat: 'no-repeat',\n                backgroundPosition: 'center',\n                backgroundColor: 'transparent',\n                overflow: 'hidden',\n                background: 'linear-gradient(transparent, transparent) padding-box, linear-gradient(to left, var(--accent-color), var(--accent-secondary-color)) border-box',\n                border: '1px solid transparent',\n                position: 'relative'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'absolute',\n                    top: 0,\n                    bottom: 0,\n                    left: '-15%',\n                    right: 0,\n                    width: 0,\n                    height: '106%',\n                    background: 'linear-gradient(to left, var(--accent-color) 0%, var(--accent-secondary-color) 100%)',\n                    transform: 'skew(45deg)',\n                    transition: 'all 0.4s ease-in-out',\n                    zIndex: -1\n                }\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n            lineNumber: 36,\n            columnNumber: 5\n        }, undefined);\n    };\n    // Client item component\n    const ClientItem = (param)=>{\n        let { client, index } = param;\n        const textLength = client.companyname.length;\n        const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);\n        const totalWidth = parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: \"\".concat(LOGO_GAP, \"px\"),\n                width: \"\".concat(totalWidth, \"px\"),\n                flexShrink: 0,\n                marginRight: '0px',\n                overflow: 'hidden'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientLogo, {\n                    logourl: client.logourl\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        color: 'white',\n                        fontSize: '18px',\n                        fontWeight: 'normal',\n                        fontFamily: 'sans-serif',\n                        whiteSpace: 'nowrap',\n                        overflow: 'hidden',\n                        textOverflow: 'ellipsis',\n                        flex: 1\n                    },\n                    children: client.companyname\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, client.id || index, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n            lineNumber: 73,\n            columnNumber: 7\n        }, undefined);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeSliderHero.useEffect\": ()=>{\n            const fetchSliderImages = {\n                \"HomeSliderHero.useEffect.fetchSliderImages\": async ()=>{\n                    try {\n                        const response = await fetch('/api/admin/settings/hero-media');\n                        if (response.ok) {\n                            var _data_media;\n                            const data = await response.json();\n                            if (data.success && ((_data_media = data.media) === null || _data_media === void 0 ? void 0 : _data_media.hero_slider_images)) {\n                                // Parse the slider images (comma-separated URLs)\n                                const images = data.media.hero_slider_images.split(',').map({\n                                    \"HomeSliderHero.useEffect.fetchSliderImages.images\": (url)=>url.trim()\n                                }[\"HomeSliderHero.useEffect.fetchSliderImages.images\"]).filter({\n                                    \"HomeSliderHero.useEffect.fetchSliderImages.images\": (url)=>url\n                                }[\"HomeSliderHero.useEffect.fetchSliderImages.images\"]);\n                                setSliderImages(images);\n                            } else {\n                                setSliderImages([\n                                    '/images/hero-bg.jpg',\n                                    '/images/hero-bg-2.jpg'\n                                ]);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error fetching slider images:', error);\n                        setSliderImages([\n                            '/images/hero-bg.jpg',\n                            '/images/hero-bg-2.jpg'\n                        ]);\n                    }\n                }\n            }[\"HomeSliderHero.useEffect.fetchSliderImages\"];\n            fetchSliderImages();\n        }\n    }[\"HomeSliderHero.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeSliderHero.useEffect\": ()=>{\n            const fetchClients = {\n                \"HomeSliderHero.useEffect.fetchClients\": async ()=>{\n                    try {\n                        setClientsLoading(true);\n                        const response = await fetch('/api/clients?limit=6');\n                        const data = await response.json();\n                        if (data.success) {\n                            setClients(data.clients || []);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching clients:', error);\n                    } finally{\n                        setClientsLoading(false);\n                    }\n                }\n            }[\"HomeSliderHero.useEffect.fetchClients\"];\n            fetchClients();\n        }\n    }[\"HomeSliderHero.useEffect\"], []);\n    // Default slides if no images are configured\n    const defaultSlides = [\n        '/images/hero-bg.jpg',\n        '/images/hero-bg-2.jpg'\n    ];\n    const slidesToShow = sliderImages.length > 0 ? sliderImages : defaultSlides;\n    // Handle dot navigation\n    const goToSlide = (index)=>{\n        setCurrentSlide(index);\n    };\n    // Auto-advance slides\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomeSliderHero.useEffect\": ()=>{\n            if (slidesToShow.length > 1) {\n                const timer = setInterval({\n                    \"HomeSliderHero.useEffect.timer\": ()=>{\n                        setCurrentSlide({\n                            \"HomeSliderHero.useEffect.timer\": (prev)=>(prev + 1) % slidesToShow.length\n                        }[\"HomeSliderHero.useEffect.timer\"]);\n                    }\n                }[\"HomeSliderHero.useEffect.timer\"], 5000);\n                return ({\n                    \"HomeSliderHero.useEffect\": ()=>clearInterval(timer)\n                })[\"HomeSliderHero.useEffect\"];\n            }\n        }\n    }[\"HomeSliderHero.useEffect\"], [\n        slidesToShow.length\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"hero hero-bg-image hero-slider-layout\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"swiper hero-swiper\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"swiper-wrapper\",\n                        children: slidesToShow.map((imageUrl, index)=>{\n                            const isActive = index === currentSlide;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"swiper-slide\",\n                                style: {\n                                    display: isActive ? 'block' : 'none'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hero-slide\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hero-slider-image\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: imageUrl,\n                                                alt: \"Hero slide \".concat(index + 1)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"container\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"row align-items-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-lg-12\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"hero-content\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"section-title section-title-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"wow fadeInUp\",\n                                                                            children: \"Enterprise-grade software & web development solutions\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                            lineNumber: 192,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                            className: \"wow fadeInUp\",\n                                                                            \"data-wow-delay\": \"0.2s\",\n                                                                            \"data-cursor\": \"-opaque\",\n                                                                            children: [\n                                                                                \"Advanced digital solutions that\",\n                                                                                \" \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"accelerate your growth\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                                    lineNumber: 201,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                            lineNumber: 195,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"wow fadeInUp\",\n                                                                            \"data-wow-delay\": \"0.4s\",\n                                                                            children: \"Transform your vision into scalable applications and sophisticated web platforms. Professional development services that deliver measurable results.\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                            lineNumber: 203,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                    lineNumber: 191,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"hero-btn wow fadeInUp\",\n                                                                    \"data-wow-delay\": \"0.6s\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                            href: \"/main/contact\",\n                                                                            className: \"btn-default btn-highlighted\",\n                                                                            children: \"Get Free Assessment\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                            lineNumber: 214,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                            href: \"/main/services\",\n                                                                            className: \"btn-default\",\n                                                                            children: \"View our services\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                            lineNumber: 220,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"row\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-lg-12\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"hero-company-slider\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"We're Trusted by more than\",\n                                                                        \" \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"counter\",\n                                                                            children: clients.length\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                            lineNumber: 235,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        \"+ companies\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"swiper\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"swiper-wrapper\",\n                                                                        children: clientsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"swiper-slide\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"company-logo\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    style: {\n                                                                                        color: 'white',\n                                                                                        fontSize: '14px'\n                                                                                    },\n                                                                                    children: \"Loading...\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                                    lineNumber: 242,\n                                                                                    columnNumber: 39\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                                lineNumber: 241,\n                                                                                columnNumber: 37\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                            lineNumber: 240,\n                                                                            columnNumber: 35\n                                                                        }, undefined) : clients.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"swiper-slide\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"company-logo\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    style: {\n                                                                                        color: 'white',\n                                                                                        fontSize: '14px'\n                                                                                    },\n                                                                                    children: \"No clients available\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                                    lineNumber: 248,\n                                                                                    columnNumber: 39\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                                lineNumber: 247,\n                                                                                columnNumber: 37\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                            lineNumber: 246,\n                                                                            columnNumber: 35\n                                                                        }, undefined) : clients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"swiper-slide\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"company-logo\",\n                                                                                    style: {\n                                                                                        display: 'flex',\n                                                                                        alignItems: 'center',\n                                                                                        justifyContent: 'flex-start',\n                                                                                        gap: '12px',\n                                                                                        padding: '10px',\n                                                                                        minWidth: '200px',\n                                                                                        whiteSpace: 'nowrap'\n                                                                                    },\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            style: {\n                                                                                                width: '70px',\n                                                                                                height: '70px',\n                                                                                                borderRadius: '50%',\n                                                                                                overflow: 'hidden',\n                                                                                                display: 'flex',\n                                                                                                alignItems: 'center',\n                                                                                                justifyContent: 'center',\n                                                                                                flexShrink: 0,\n                                                                                                backgroundColor: 'transparent'\n                                                                                            },\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                                src: client.logourl || \"/images/icon-testimonial-logo.svg\",\n                                                                                                alt: client.companyname,\n                                                                                                style: {\n                                                                                                    width: '100%',\n                                                                                                    height: '100%',\n                                                                                                    objectFit: 'cover',\n                                                                                                    borderRadius: '50%'\n                                                                                                }\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                                                lineNumber: 274,\n                                                                                                columnNumber: 43\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                                            lineNumber: 263,\n                                                                                            columnNumber: 41\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            style: {\n                                                                                                color: 'white',\n                                                                                                fontSize: '26px',\n                                                                                                fontWeight: 'bold',\n                                                                                                fontFamily: 'sans-serif',\n                                                                                                whiteSpace: 'nowrap',\n                                                                                                overflow: 'hidden',\n                                                                                                textOverflow: 'ellipsis',\n                                                                                                maxWidth: '150px'\n                                                                                            },\n                                                                                            children: client.companyname\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                                            lineNumber: 285,\n                                                                                            columnNumber: 41\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                                    lineNumber: 254,\n                                                                                    columnNumber: 39\n                                                                                }, undefined)\n                                                                            }, client.id || index, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                                lineNumber: 253,\n                                                                                columnNumber: 37\n                                                                            }, undefined))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                        lineNumber: 238,\n                                                                        columnNumber: 33\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                                    lineNumber: 237,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, index, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 17\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, undefined),\n                    slidesToShow.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"swiper-pagination hero-pagination\",\n                        style: {\n                            position: 'absolute',\n                            bottom: '50px',\n                            left: '50%',\n                            transform: 'translateX(-50%)',\n                            zIndex: 10,\n                            display: 'flex',\n                            justifyContent: 'center',\n                            alignItems: 'center',\n                            gap: '12px'\n                        },\n                        children: slidesToShow.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"swiper-pagination-bullet \".concat(currentSlide === index ? 'swiper-pagination-bullet-active' : ''),\n                                onClick: ()=>goToSlide(index),\n                                \"aria-label\": \"Go to slide \".concat(index + 1),\n                                style: {\n                                    width: '14px',\n                                    height: '14px',\n                                    borderRadius: '50%',\n                                    border: 'none',\n                                    cursor: 'pointer',\n                                    transition: 'all 0.3s ease',\n                                    background: currentSlide === index ? '#ffffff' : 'rgba(255, 255, 255, 0.5)',\n                                    opacity: currentSlide === index ? 1 : 0.7,\n                                    transform: currentSlide === index ? 'scale(1.2)' : 'scale(1)'\n                                }\n                            }, index, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 17\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n                lineNumber: 167,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/main/home/<USER>/HomeSliderHero.tsx\",\n            lineNumber: 166,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(HomeSliderHero, \"YlHtYqDAJv4Dtg9xSD8h8xkCoGc=\");\n_c = HomeSliderHero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeSliderHero);\nvar _c;\n$RefreshReg$(_c, \"HomeSliderHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/main/home/<USER>/HomeSliderHero.tsx\n"));

/***/ })

});