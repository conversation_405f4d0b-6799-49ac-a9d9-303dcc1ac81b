"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@stripe";
exports.ids = ["vendor-chunks/@stripe"];
exports.modules = {

/***/ "(ssr)/./node_modules/@stripe/react-stripe-js/dist/react-stripe.esm.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/@stripe/react-stripe-js/dist/react-stripe.esm.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AddressElement: () => (/* binding */ AddressElement),\n/* harmony export */   AffirmMessageElement: () => (/* binding */ AffirmMessageElement),\n/* harmony export */   AfterpayClearpayMessageElement: () => (/* binding */ AfterpayClearpayMessageElement),\n/* harmony export */   AuBankAccountElement: () => (/* binding */ AuBankAccountElement),\n/* harmony export */   CardCvcElement: () => (/* binding */ CardCvcElement),\n/* harmony export */   CardElement: () => (/* binding */ CardElement),\n/* harmony export */   CardExpiryElement: () => (/* binding */ CardExpiryElement),\n/* harmony export */   CardNumberElement: () => (/* binding */ CardNumberElement),\n/* harmony export */   CheckoutProvider: () => (/* binding */ CheckoutProvider),\n/* harmony export */   CurrencySelectorElement: () => (/* binding */ CurrencySelectorElement),\n/* harmony export */   Elements: () => (/* binding */ Elements),\n/* harmony export */   ElementsConsumer: () => (/* binding */ ElementsConsumer),\n/* harmony export */   EmbeddedCheckout: () => (/* binding */ EmbeddedCheckout),\n/* harmony export */   EmbeddedCheckoutProvider: () => (/* binding */ EmbeddedCheckoutProvider),\n/* harmony export */   EpsBankElement: () => (/* binding */ EpsBankElement),\n/* harmony export */   ExpressCheckoutElement: () => (/* binding */ ExpressCheckoutElement),\n/* harmony export */   FpxBankElement: () => (/* binding */ FpxBankElement),\n/* harmony export */   IbanElement: () => (/* binding */ IbanElement),\n/* harmony export */   IdealBankElement: () => (/* binding */ IdealBankElement),\n/* harmony export */   LinkAuthenticationElement: () => (/* binding */ LinkAuthenticationElement),\n/* harmony export */   P24BankElement: () => (/* binding */ P24BankElement),\n/* harmony export */   PaymentElement: () => (/* binding */ PaymentElement),\n/* harmony export */   PaymentMethodMessagingElement: () => (/* binding */ PaymentMethodMessagingElement),\n/* harmony export */   PaymentRequestButtonElement: () => (/* binding */ PaymentRequestButtonElement),\n/* harmony export */   ShippingAddressElement: () => (/* binding */ ShippingAddressElement),\n/* harmony export */   TaxIdElement: () => (/* binding */ TaxIdElement),\n/* harmony export */   useCheckout: () => (/* binding */ useCheckout),\n/* harmony export */   useElements: () => (/* binding */ useElements),\n/* harmony export */   useStripe: () => (/* binding */ useStripe)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n\n\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr && (typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]);\n\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n\n  var _s, _e;\n\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar useAttachEvent = function useAttachEvent(element, event, cb) {\n  var cbDefined = !!cb;\n  var cbRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(cb); // In many integrations the callback prop changes on each render.\n  // Using a ref saves us from calling element.on/.off every render.\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    cbRef.current = cb;\n  }, [cb]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    if (!cbDefined || !element) {\n      return function () {};\n    }\n\n    var decoratedCb = function decoratedCb() {\n      if (cbRef.current) {\n        cbRef.current.apply(cbRef, arguments);\n      }\n    };\n\n    element.on(event, decoratedCb);\n    return function () {\n      element.off(event, decoratedCb);\n    };\n  }, [cbDefined, event, element, cbRef]);\n};\n\nvar usePrevious = function usePrevious(value) {\n  var ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(value);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    ref.current = value;\n  }, [value]);\n  return ref.current;\n};\n\nvar isUnknownObject = function isUnknownObject(raw) {\n  return raw !== null && _typeof(raw) === 'object';\n};\nvar isPromise = function isPromise(raw) {\n  return isUnknownObject(raw) && typeof raw.then === 'function';\n}; // We are using types to enforce the `stripe` prop in this lib,\n// but in an untyped integration `stripe` could be anything, so we need\n// to do some sanity validation to prevent type errors.\n\nvar isStripe = function isStripe(raw) {\n  return isUnknownObject(raw) && typeof raw.elements === 'function' && typeof raw.createToken === 'function' && typeof raw.createPaymentMethod === 'function' && typeof raw.confirmCardPayment === 'function';\n};\n\nvar PLAIN_OBJECT_STR = '[object Object]';\nvar isEqual = function isEqual(left, right) {\n  if (!isUnknownObject(left) || !isUnknownObject(right)) {\n    return left === right;\n  }\n\n  var leftArray = Array.isArray(left);\n  var rightArray = Array.isArray(right);\n  if (leftArray !== rightArray) return false;\n  var leftPlainObject = Object.prototype.toString.call(left) === PLAIN_OBJECT_STR;\n  var rightPlainObject = Object.prototype.toString.call(right) === PLAIN_OBJECT_STR;\n  if (leftPlainObject !== rightPlainObject) return false; // not sure what sort of special object this is (regexp is one option), so\n  // fallback to reference check.\n\n  if (!leftPlainObject && !leftArray) return left === right;\n  var leftKeys = Object.keys(left);\n  var rightKeys = Object.keys(right);\n  if (leftKeys.length !== rightKeys.length) return false;\n  var keySet = {};\n\n  for (var i = 0; i < leftKeys.length; i += 1) {\n    keySet[leftKeys[i]] = true;\n  }\n\n  for (var _i = 0; _i < rightKeys.length; _i += 1) {\n    keySet[rightKeys[_i]] = true;\n  }\n\n  var allKeys = Object.keys(keySet);\n\n  if (allKeys.length !== leftKeys.length) {\n    return false;\n  }\n\n  var l = left;\n  var r = right;\n\n  var pred = function pred(key) {\n    return isEqual(l[key], r[key]);\n  };\n\n  return allKeys.every(pred);\n};\n\nvar extractAllowedOptionsUpdates = function extractAllowedOptionsUpdates(options, prevOptions, immutableKeys) {\n  if (!isUnknownObject(options)) {\n    return null;\n  }\n\n  return Object.keys(options).reduce(function (newOptions, key) {\n    var isUpdated = !isUnknownObject(prevOptions) || !isEqual(options[key], prevOptions[key]);\n\n    if (immutableKeys.includes(key)) {\n      if (isUpdated) {\n        console.warn(\"Unsupported prop change: options.\".concat(key, \" is not a mutable property.\"));\n      }\n\n      return newOptions;\n    }\n\n    if (!isUpdated) {\n      return newOptions;\n    }\n\n    return _objectSpread2(_objectSpread2({}, newOptions || {}), {}, _defineProperty({}, key, options[key]));\n  }, null);\n};\n\nvar INVALID_STRIPE_ERROR$2 = 'Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.'; // We are using types to enforce the `stripe` prop in this lib, but in a real\n// integration `stripe` could be anything, so we need to do some sanity\n// validation to prevent type errors.\n\nvar validateStripe = function validateStripe(maybeStripe) {\n  var errorMsg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : INVALID_STRIPE_ERROR$2;\n\n  if (maybeStripe === null || isStripe(maybeStripe)) {\n    return maybeStripe;\n  }\n\n  throw new Error(errorMsg);\n};\n\nvar parseStripeProp = function parseStripeProp(raw) {\n  var errorMsg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : INVALID_STRIPE_ERROR$2;\n\n  if (isPromise(raw)) {\n    return {\n      tag: 'async',\n      stripePromise: Promise.resolve(raw).then(function (result) {\n        return validateStripe(result, errorMsg);\n      })\n    };\n  }\n\n  var stripe = validateStripe(raw, errorMsg);\n\n  if (stripe === null) {\n    return {\n      tag: 'empty'\n    };\n  }\n\n  return {\n    tag: 'sync',\n    stripe: stripe\n  };\n};\n\nvar registerWithStripeJs = function registerWithStripeJs(stripe) {\n  if (!stripe || !stripe._registerWrapper || !stripe.registerAppInfo) {\n    return;\n  }\n\n  stripe._registerWrapper({\n    name: 'react-stripe-js',\n    version: \"3.10.0\"\n  });\n\n  stripe.registerAppInfo({\n    name: 'react-stripe-js',\n    version: \"3.10.0\",\n    url: 'https://stripe.com/docs/stripe-js/react'\n  });\n};\n\nvar ElementsContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nElementsContext.displayName = 'ElementsContext';\nvar parseElementsContext = function parseElementsContext(ctx, useCase) {\n  if (!ctx) {\n    throw new Error(\"Could not find Elements context; You need to wrap the part of your app that \".concat(useCase, \" in an <Elements> provider.\"));\n  }\n\n  return ctx;\n};\n/**\n * The `Elements` provider allows you to use [Element components](https://stripe.com/docs/stripe-js/react#element-components) and access the [Stripe object](https://stripe.com/docs/js/initializing) in any nested component.\n * Render an `Elements` provider at the root of your React app so that it is available everywhere you need it.\n *\n * To use the `Elements` provider, call `loadStripe` from `@stripe/stripe-js` with your publishable key.\n * The `loadStripe` function will asynchronously load the Stripe.js script and initialize a `Stripe` object.\n * Pass the returned `Promise` to `Elements`.\n *\n * @docs https://docs.stripe.com/sdks/stripejs-react?ui=elements#elements-provider\n */\n\nvar Elements = function Elements(_ref) {\n  var rawStripeProp = _ref.stripe,\n      options = _ref.options,\n      children = _ref.children;\n  var parsed = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(function () {\n    return parseStripeProp(rawStripeProp);\n  }, [rawStripeProp]); // For a sync stripe instance, initialize into context\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0__.useState(function () {\n    return {\n      stripe: parsed.tag === 'sync' ? parsed.stripe : null,\n      elements: parsed.tag === 'sync' ? parsed.stripe.elements(options) : null\n    };\n  }),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      ctx = _React$useState2[0],\n      setContext = _React$useState2[1];\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    var isMounted = true;\n\n    var safeSetContext = function safeSetContext(stripe) {\n      setContext(function (ctx) {\n        // no-op if we already have a stripe instance (https://github.com/stripe/react-stripe-js/issues/296)\n        if (ctx.stripe) return ctx;\n        return {\n          stripe: stripe,\n          elements: stripe.elements(options)\n        };\n      });\n    }; // For an async stripePromise, store it in context once resolved\n\n\n    if (parsed.tag === 'async' && !ctx.stripe) {\n      parsed.stripePromise.then(function (stripe) {\n        if (stripe && isMounted) {\n          // Only update Elements context if the component is still mounted\n          // and stripe is not null. We allow stripe to be null to make\n          // handling SSR easier.\n          safeSetContext(stripe);\n        }\n      });\n    } else if (parsed.tag === 'sync' && !ctx.stripe) {\n      // Or, handle a sync stripe instance going from null -> populated\n      safeSetContext(parsed.stripe);\n    }\n\n    return function () {\n      isMounted = false;\n    };\n  }, [parsed, ctx, options]); // Warn on changes to stripe prop\n\n  var prevStripe = usePrevious(rawStripeProp);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    if (prevStripe !== null && prevStripe !== rawStripeProp) {\n      console.warn('Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.');\n    }\n  }, [prevStripe, rawStripeProp]); // Apply updates to elements when options prop has relevant changes\n\n  var prevOptions = usePrevious(options);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    if (!ctx.elements) {\n      return;\n    }\n\n    var updates = extractAllowedOptionsUpdates(options, prevOptions, ['clientSecret', 'fonts']);\n\n    if (updates) {\n      ctx.elements.update(updates);\n    }\n  }, [options, prevOptions, ctx.elements]); // Attach react-stripe-js version to stripe.js instance\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    registerWithStripeJs(ctx.stripe);\n  }, [ctx.stripe]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(ElementsContext.Provider, {\n    value: ctx\n  }, children);\n};\nElements.propTypes = {\n  stripe: prop_types__WEBPACK_IMPORTED_MODULE_1__.any,\n  options: prop_types__WEBPACK_IMPORTED_MODULE_1__.object\n};\nvar useElementsContextWithUseCase = function useElementsContextWithUseCase(useCaseMessage) {\n  var ctx = react__WEBPACK_IMPORTED_MODULE_0__.useContext(ElementsContext);\n  return parseElementsContext(ctx, useCaseMessage);\n};\n/**\n * @docs https://stripe.com/docs/stripe-js/react#useelements-hook\n */\n\nvar useElements = function useElements() {\n  var _useElementsContextWi = useElementsContextWithUseCase('calls useElements()'),\n      elements = _useElementsContextWi.elements;\n\n  return elements;\n};\n/**\n * @docs https://stripe.com/docs/stripe-js/react#elements-consumer\n */\n\nvar ElementsConsumer = function ElementsConsumer(_ref2) {\n  var children = _ref2.children;\n  var ctx = useElementsContextWithUseCase('mounts <ElementsConsumer>'); // Assert to satisfy the busted React.FC return type (it should be ReactNode)\n\n  return children(ctx);\n};\nElementsConsumer.propTypes = {\n  children: prop_types__WEBPACK_IMPORTED_MODULE_1__.func.isRequired\n};\n\nvar _excluded$1 = [\"on\", \"session\"];\nvar CheckoutSdkContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nCheckoutSdkContext.displayName = 'CheckoutSdkContext';\nvar parseCheckoutSdkContext = function parseCheckoutSdkContext(ctx, useCase) {\n  if (!ctx) {\n    throw new Error(\"Could not find CheckoutProvider context; You need to wrap the part of your app that \".concat(useCase, \" in an <CheckoutProvider> provider.\"));\n  }\n\n  return ctx;\n};\nvar CheckoutContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nCheckoutContext.displayName = 'CheckoutContext';\nvar extractCheckoutContextValue = function extractCheckoutContextValue(checkoutSdk, sessionState) {\n  if (!checkoutSdk) {\n    return null;\n  }\n\n  checkoutSdk.on;\n      checkoutSdk.session;\n      var actions = _objectWithoutProperties(checkoutSdk, _excluded$1);\n\n  if (!sessionState) {\n    return Object.assign(checkoutSdk.session(), actions);\n  }\n\n  return Object.assign(sessionState, actions);\n};\nvar INVALID_STRIPE_ERROR$1 = 'Invalid prop `stripe` supplied to `CheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.';\nvar CheckoutProvider = function CheckoutProvider(_ref) {\n  var rawStripeProp = _ref.stripe,\n      options = _ref.options,\n      children = _ref.children;\n  var parsed = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(function () {\n    return parseStripeProp(rawStripeProp, INVALID_STRIPE_ERROR$1);\n  }, [rawStripeProp]); // State used to trigger a re-render when sdk.session is updated\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0__.useState(null),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      session = _React$useState2[0],\n      setSession = _React$useState2[1];\n\n  var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_0__.useState(function () {\n    return {\n      stripe: parsed.tag === 'sync' ? parsed.stripe : null,\n      checkoutSdk: null\n    };\n  }),\n      _React$useState4 = _slicedToArray(_React$useState3, 2),\n      ctx = _React$useState4[0],\n      setContext = _React$useState4[1];\n\n  var safeSetContext = function safeSetContext(stripe, checkoutSdk) {\n    setContext(function (ctx) {\n      if (ctx.stripe && ctx.checkoutSdk) {\n        return ctx;\n      }\n\n      return {\n        stripe: stripe,\n        checkoutSdk: checkoutSdk\n      };\n    });\n  }; // Ref used to avoid calling initCheckout multiple times when options changes\n\n\n  var initCheckoutCalledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    var isMounted = true;\n\n    if (parsed.tag === 'async' && !ctx.stripe) {\n      parsed.stripePromise.then(function (stripe) {\n        if (stripe && isMounted && !initCheckoutCalledRef.current) {\n          // Only update context if the component is still mounted\n          // and stripe is not null. We allow stripe to be null to make\n          // handling SSR easier.\n          initCheckoutCalledRef.current = true;\n          stripe.initCheckout(options).then(function (checkoutSdk) {\n            if (checkoutSdk) {\n              safeSetContext(stripe, checkoutSdk);\n              checkoutSdk.on('change', setSession);\n            }\n          });\n        }\n      });\n    } else if (parsed.tag === 'sync' && parsed.stripe && !initCheckoutCalledRef.current) {\n      initCheckoutCalledRef.current = true;\n      parsed.stripe.initCheckout(options).then(function (checkoutSdk) {\n        if (checkoutSdk) {\n          safeSetContext(parsed.stripe, checkoutSdk);\n          checkoutSdk.on('change', setSession);\n        }\n      });\n    }\n\n    return function () {\n      isMounted = false;\n    };\n  }, [parsed, ctx, options, setSession]); // Warn on changes to stripe prop\n\n  var prevStripe = usePrevious(rawStripeProp);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    if (prevStripe !== null && prevStripe !== rawStripeProp) {\n      console.warn('Unsupported prop change on CheckoutProvider: You cannot change the `stripe` prop after setting it.');\n    }\n  }, [prevStripe, rawStripeProp]); // Apply updates to elements when options prop has relevant changes\n\n  var prevOptions = usePrevious(options);\n  var prevCheckoutSdk = usePrevious(ctx.checkoutSdk);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    var _prevOptions$elements, _options$elementsOpti, _prevOptions$elements2, _options$elementsOpti2;\n\n    // Ignore changes while checkout sdk is not initialized.\n    if (!ctx.checkoutSdk) {\n      return;\n    }\n\n    var hasSdkLoaded = Boolean(!prevCheckoutSdk && ctx.checkoutSdk); // Handle appearance changes\n\n    var previousAppearance = prevOptions === null || prevOptions === void 0 ? void 0 : (_prevOptions$elements = prevOptions.elementsOptions) === null || _prevOptions$elements === void 0 ? void 0 : _prevOptions$elements.appearance;\n    var currentAppearance = options === null || options === void 0 ? void 0 : (_options$elementsOpti = options.elementsOptions) === null || _options$elementsOpti === void 0 ? void 0 : _options$elementsOpti.appearance;\n    var hasAppearanceChanged = !isEqual(currentAppearance, previousAppearance);\n\n    if (currentAppearance && (hasAppearanceChanged || hasSdkLoaded)) {\n      ctx.checkoutSdk.changeAppearance(currentAppearance);\n    } // Handle fonts changes\n\n\n    var previousFonts = prevOptions === null || prevOptions === void 0 ? void 0 : (_prevOptions$elements2 = prevOptions.elementsOptions) === null || _prevOptions$elements2 === void 0 ? void 0 : _prevOptions$elements2.fonts;\n    var currentFonts = options === null || options === void 0 ? void 0 : (_options$elementsOpti2 = options.elementsOptions) === null || _options$elementsOpti2 === void 0 ? void 0 : _options$elementsOpti2.fonts;\n    var hasFontsChanged = !isEqual(previousFonts, currentFonts);\n\n    if (currentFonts && (hasFontsChanged || hasSdkLoaded)) {\n      ctx.checkoutSdk.loadFonts(currentFonts);\n    }\n  }, [options, prevOptions, ctx.checkoutSdk, prevCheckoutSdk]); // Attach react-stripe-js version to stripe.js instance\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    registerWithStripeJs(ctx.stripe);\n  }, [ctx.stripe]);\n  var checkoutContextValue = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(function () {\n    return extractCheckoutContextValue(ctx.checkoutSdk, session);\n  }, [ctx.checkoutSdk, session]);\n\n  if (!ctx.checkoutSdk) {\n    return null;\n  }\n\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(CheckoutSdkContext.Provider, {\n    value: ctx\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(CheckoutContext.Provider, {\n    value: checkoutContextValue\n  }, children));\n};\nCheckoutProvider.propTypes = {\n  stripe: prop_types__WEBPACK_IMPORTED_MODULE_1__.any,\n  options: prop_types__WEBPACK_IMPORTED_MODULE_1__.shape({\n    fetchClientSecret: prop_types__WEBPACK_IMPORTED_MODULE_1__.func.isRequired,\n    elementsOptions: prop_types__WEBPACK_IMPORTED_MODULE_1__.object\n  }).isRequired\n};\nvar useCheckoutSdkContextWithUseCase = function useCheckoutSdkContextWithUseCase(useCaseString) {\n  var ctx = react__WEBPACK_IMPORTED_MODULE_0__.useContext(CheckoutSdkContext);\n  return parseCheckoutSdkContext(ctx, useCaseString);\n};\nvar useElementsOrCheckoutSdkContextWithUseCase = function useElementsOrCheckoutSdkContextWithUseCase(useCaseString) {\n  var checkoutSdkContext = react__WEBPACK_IMPORTED_MODULE_0__.useContext(CheckoutSdkContext);\n  var elementsContext = react__WEBPACK_IMPORTED_MODULE_0__.useContext(ElementsContext);\n\n  if (checkoutSdkContext && elementsContext) {\n    throw new Error(\"You cannot wrap the part of your app that \".concat(useCaseString, \" in both <CheckoutProvider> and <Elements> providers.\"));\n  }\n\n  if (checkoutSdkContext) {\n    return parseCheckoutSdkContext(checkoutSdkContext, useCaseString);\n  }\n\n  return parseElementsContext(elementsContext, useCaseString);\n};\nvar useCheckout = function useCheckout() {\n  // ensure it's in CheckoutProvider\n  useCheckoutSdkContextWithUseCase('calls useCheckout()');\n  var ctx = react__WEBPACK_IMPORTED_MODULE_0__.useContext(CheckoutContext);\n\n  if (!ctx) {\n    throw new Error('Could not find Checkout Context; You need to wrap the part of your app that calls useCheckout() in an <CheckoutProvider> provider.');\n  }\n\n  return ctx;\n};\n\nvar _excluded = [\"mode\"];\n\nvar capitalized = function capitalized(str) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n};\n\nvar createElementComponent = function createElementComponent(type, isServer) {\n  var displayName = \"\".concat(capitalized(type), \"Element\");\n\n  var ClientElement = function ClientElement(_ref) {\n    var id = _ref.id,\n        className = _ref.className,\n        _ref$options = _ref.options,\n        options = _ref$options === void 0 ? {} : _ref$options,\n        onBlur = _ref.onBlur,\n        onFocus = _ref.onFocus,\n        onReady = _ref.onReady,\n        onChange = _ref.onChange,\n        onEscape = _ref.onEscape,\n        onClick = _ref.onClick,\n        onLoadError = _ref.onLoadError,\n        onLoaderStart = _ref.onLoaderStart,\n        onNetworksChange = _ref.onNetworksChange,\n        onConfirm = _ref.onConfirm,\n        onCancel = _ref.onCancel,\n        onShippingAddressChange = _ref.onShippingAddressChange,\n        onShippingRateChange = _ref.onShippingRateChange,\n        onSavedPaymentMethodRemove = _ref.onSavedPaymentMethodRemove,\n        onSavedPaymentMethodUpdate = _ref.onSavedPaymentMethodUpdate;\n    var ctx = useElementsOrCheckoutSdkContextWithUseCase(\"mounts <\".concat(displayName, \">\"));\n    var elements = 'elements' in ctx ? ctx.elements : null;\n    var checkoutSdk = 'checkoutSdk' in ctx ? ctx.checkoutSdk : null;\n\n    var _React$useState = react__WEBPACK_IMPORTED_MODULE_0__.useState(null),\n        _React$useState2 = _slicedToArray(_React$useState, 2),\n        element = _React$useState2[0],\n        setElement = _React$useState2[1];\n\n    var elementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    var domNode = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null); // For every event where the merchant provides a callback, call element.on\n    // with that callback. If the merchant ever changes the callback, removes\n    // the old callback with element.off and then call element.on with the new one.\n\n    useAttachEvent(element, 'blur', onBlur);\n    useAttachEvent(element, 'focus', onFocus);\n    useAttachEvent(element, 'escape', onEscape);\n    useAttachEvent(element, 'click', onClick);\n    useAttachEvent(element, 'loaderror', onLoadError);\n    useAttachEvent(element, 'loaderstart', onLoaderStart);\n    useAttachEvent(element, 'networkschange', onNetworksChange);\n    useAttachEvent(element, 'confirm', onConfirm);\n    useAttachEvent(element, 'cancel', onCancel);\n    useAttachEvent(element, 'shippingaddresschange', onShippingAddressChange);\n    useAttachEvent(element, 'shippingratechange', onShippingRateChange);\n    useAttachEvent(element, 'savedpaymentmethodremove', onSavedPaymentMethodRemove);\n    useAttachEvent(element, 'savedpaymentmethodupdate', onSavedPaymentMethodUpdate);\n    useAttachEvent(element, 'change', onChange);\n    var readyCallback;\n\n    if (onReady) {\n      if (type === 'expressCheckout') {\n        // Passes through the event, which includes visible PM types\n        readyCallback = onReady;\n      } else {\n        // For other Elements, pass through the Element itself.\n        readyCallback = function readyCallback() {\n          onReady(element);\n        };\n      }\n    }\n\n    useAttachEvent(element, 'ready', readyCallback);\n    react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(function () {\n      if (elementRef.current === null && domNode.current !== null && (elements || checkoutSdk)) {\n        var newElement = null;\n\n        if (checkoutSdk) {\n          switch (type) {\n            case 'payment':\n              newElement = checkoutSdk.createPaymentElement(options);\n              break;\n\n            case 'address':\n              if ('mode' in options) {\n                var mode = options.mode,\n                    restOptions = _objectWithoutProperties(options, _excluded);\n\n                if (mode === 'shipping') {\n                  newElement = checkoutSdk.createShippingAddressElement(restOptions);\n                } else if (mode === 'billing') {\n                  newElement = checkoutSdk.createBillingAddressElement(restOptions);\n                } else {\n                  throw new Error(\"Invalid options.mode. mode must be 'billing' or 'shipping'.\");\n                }\n              } else {\n                throw new Error(\"You must supply options.mode. mode must be 'billing' or 'shipping'.\");\n              }\n\n              break;\n\n            case 'expressCheckout':\n              newElement = checkoutSdk.createExpressCheckoutElement(options);\n              break;\n\n            case 'currencySelector':\n              newElement = checkoutSdk.createCurrencySelectorElement();\n              break;\n\n            case 'taxId':\n              newElement = checkoutSdk.createTaxIdElement(options);\n              break;\n\n            default:\n              throw new Error(\"Invalid Element type \".concat(displayName, \". You must use either the <PaymentElement />, <AddressElement options={{mode: 'shipping'}} />, <AddressElement options={{mode: 'billing'}} />, or <ExpressCheckoutElement />.\"));\n          }\n        } else if (elements) {\n          newElement = elements.create(type, options);\n        } // Store element in a ref to ensure it's _immediately_ available in cleanup hooks in StrictMode\n\n\n        elementRef.current = newElement; // Store element in state to facilitate event listener attachment\n\n        setElement(newElement);\n\n        if (newElement) {\n          newElement.mount(domNode.current);\n        }\n      }\n    }, [elements, checkoutSdk, options]);\n    var prevOptions = usePrevious(options);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n      if (!elementRef.current) {\n        return;\n      }\n\n      var updates = extractAllowedOptionsUpdates(options, prevOptions, ['paymentRequest']);\n\n      if (updates && 'update' in elementRef.current) {\n        elementRef.current.update(updates);\n      }\n    }, [options, prevOptions]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(function () {\n      return function () {\n        if (elementRef.current && typeof elementRef.current.destroy === 'function') {\n          try {\n            elementRef.current.destroy();\n            elementRef.current = null;\n          } catch (error) {// Do nothing\n          }\n        }\n      };\n    }, []);\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n      id: id,\n      className: className,\n      ref: domNode\n    });\n  }; // Only render the Element wrapper in a server environment.\n\n\n  var ServerElement = function ServerElement(props) {\n    useElementsOrCheckoutSdkContextWithUseCase(\"mounts <\".concat(displayName, \">\"));\n    var id = props.id,\n        className = props.className;\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n      id: id,\n      className: className\n    });\n  };\n\n  var Element = isServer ? ServerElement : ClientElement;\n  Element.propTypes = {\n    id: prop_types__WEBPACK_IMPORTED_MODULE_1__.string,\n    className: prop_types__WEBPACK_IMPORTED_MODULE_1__.string,\n    onChange: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    onBlur: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    onFocus: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    onReady: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    onEscape: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    onClick: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    onLoadError: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    onLoaderStart: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    onNetworksChange: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    onConfirm: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    onCancel: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    onShippingAddressChange: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    onShippingRateChange: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    onSavedPaymentMethodRemove: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    onSavedPaymentMethodUpdate: prop_types__WEBPACK_IMPORTED_MODULE_1__.func,\n    options: prop_types__WEBPACK_IMPORTED_MODULE_1__.object\n  };\n  Element.displayName = displayName;\n  Element.__elementType = type;\n  return Element;\n};\n\nvar isServer = typeof window === 'undefined';\n\nvar EmbeddedCheckoutContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nEmbeddedCheckoutContext.displayName = 'EmbeddedCheckoutProviderContext';\nvar useEmbeddedCheckoutContext = function useEmbeddedCheckoutContext() {\n  var ctx = react__WEBPACK_IMPORTED_MODULE_0__.useContext(EmbeddedCheckoutContext);\n\n  if (!ctx) {\n    throw new Error('<EmbeddedCheckout> must be used within <EmbeddedCheckoutProvider>');\n  }\n\n  return ctx;\n};\nvar INVALID_STRIPE_ERROR = 'Invalid prop `stripe` supplied to `EmbeddedCheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.';\nvar EmbeddedCheckoutProvider = function EmbeddedCheckoutProvider(_ref) {\n  var rawStripeProp = _ref.stripe,\n      options = _ref.options,\n      children = _ref.children;\n  var parsed = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(function () {\n    return parseStripeProp(rawStripeProp, INVALID_STRIPE_ERROR);\n  }, [rawStripeProp]);\n  var embeddedCheckoutPromise = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  var loadedStripe = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n    embeddedCheckout: null\n  }),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      ctx = _React$useState2[0],\n      setContext = _React$useState2[1];\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    // Don't support any ctx updates once embeddedCheckout or stripe is set.\n    if (loadedStripe.current || embeddedCheckoutPromise.current) {\n      return;\n    }\n\n    var setStripeAndInitEmbeddedCheckout = function setStripeAndInitEmbeddedCheckout(stripe) {\n      if (loadedStripe.current || embeddedCheckoutPromise.current) return;\n      loadedStripe.current = stripe;\n      embeddedCheckoutPromise.current = loadedStripe.current.initEmbeddedCheckout(options).then(function (embeddedCheckout) {\n        setContext({\n          embeddedCheckout: embeddedCheckout\n        });\n      });\n    }; // For an async stripePromise, store it once resolved\n\n\n    if (parsed.tag === 'async' && !loadedStripe.current && (options.clientSecret || options.fetchClientSecret)) {\n      parsed.stripePromise.then(function (stripe) {\n        if (stripe) {\n          setStripeAndInitEmbeddedCheckout(stripe);\n        }\n      });\n    } else if (parsed.tag === 'sync' && !loadedStripe.current && (options.clientSecret || options.fetchClientSecret)) {\n      // Or, handle a sync stripe instance going from null -> populated\n      setStripeAndInitEmbeddedCheckout(parsed.stripe);\n    }\n  }, [parsed, options, ctx, loadedStripe]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    // cleanup on unmount\n    return function () {\n      // If embedded checkout is fully initialized, destroy it.\n      if (ctx.embeddedCheckout) {\n        embeddedCheckoutPromise.current = null;\n        ctx.embeddedCheckout.destroy();\n      } else if (embeddedCheckoutPromise.current) {\n        // If embedded checkout is still initializing, destroy it once\n        // it's done. This could be caused by unmounting very quickly\n        // after mounting.\n        embeddedCheckoutPromise.current.then(function () {\n          embeddedCheckoutPromise.current = null;\n\n          if (ctx.embeddedCheckout) {\n            ctx.embeddedCheckout.destroy();\n          }\n        });\n      }\n    };\n  }, [ctx.embeddedCheckout]); // Attach react-stripe-js version to stripe.js instance\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    registerWithStripeJs(loadedStripe);\n  }, [loadedStripe]); // Warn on changes to stripe prop.\n  // The stripe prop value can only go from null to non-null once and\n  // can't be changed after that.\n\n  var prevStripe = usePrevious(rawStripeProp);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    if (prevStripe !== null && prevStripe !== rawStripeProp) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the `stripe` prop after setting it.');\n    }\n  }, [prevStripe, rawStripeProp]); // Warn on changes to options.\n\n  var prevOptions = usePrevious(options);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    if (prevOptions == null) {\n      return;\n    }\n\n    if (options == null) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot unset options after setting them.');\n      return;\n    }\n\n    if (options.clientSecret === undefined && options.fetchClientSecret === undefined) {\n      console.warn('Invalid props passed to EmbeddedCheckoutProvider: You must provide one of either `options.fetchClientSecret` or `options.clientSecret`.');\n    }\n\n    if (prevOptions.clientSecret != null && options.clientSecret !== prevOptions.clientSecret) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the client secret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead.');\n    }\n\n    if (prevOptions.fetchClientSecret != null && options.fetchClientSecret !== prevOptions.fetchClientSecret) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change fetchClientSecret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead.');\n    }\n\n    if (prevOptions.onComplete != null && options.onComplete !== prevOptions.onComplete) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onComplete option after setting it.');\n    }\n\n    if (prevOptions.onShippingDetailsChange != null && options.onShippingDetailsChange !== prevOptions.onShippingDetailsChange) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onShippingDetailsChange option after setting it.');\n    }\n\n    if (prevOptions.onLineItemsChange != null && options.onLineItemsChange !== prevOptions.onLineItemsChange) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onLineItemsChange option after setting it.');\n    }\n  }, [prevOptions, options]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(EmbeddedCheckoutContext.Provider, {\n    value: ctx\n  }, children);\n};\n\nvar EmbeddedCheckoutClientElement = function EmbeddedCheckoutClientElement(_ref) {\n  var id = _ref.id,\n      className = _ref.className;\n\n  var _useEmbeddedCheckoutC = useEmbeddedCheckoutContext(),\n      embeddedCheckout = _useEmbeddedCheckoutC.embeddedCheckout;\n\n  var isMounted = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  var domNode = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(function () {\n    if (!isMounted.current && embeddedCheckout && domNode.current !== null) {\n      embeddedCheckout.mount(domNode.current);\n      isMounted.current = true;\n    } // Clean up on unmount\n\n\n    return function () {\n      if (isMounted.current && embeddedCheckout) {\n        try {\n          embeddedCheckout.unmount();\n          isMounted.current = false;\n        } catch (e) {// Do nothing.\n          // Parent effects are destroyed before child effects, so\n          // in cases where both the EmbeddedCheckoutProvider and\n          // the EmbeddedCheckout component are removed at the same\n          // time, the embeddedCheckout instance will be destroyed,\n          // which causes an error when calling unmount.\n        }\n      }\n    };\n  }, [embeddedCheckout]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n    ref: domNode,\n    id: id,\n    className: className\n  });\n}; // Only render the wrapper in a server environment.\n\n\nvar EmbeddedCheckoutServerElement = function EmbeddedCheckoutServerElement(_ref2) {\n  var id = _ref2.id,\n      className = _ref2.className;\n  // Validate that we are in the right context by calling useEmbeddedCheckoutContext.\n  useEmbeddedCheckoutContext();\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n    id: id,\n    className: className\n  });\n};\n\nvar EmbeddedCheckout = isServer ? EmbeddedCheckoutServerElement : EmbeddedCheckoutClientElement;\n\n/**\n * @docs https://stripe.com/docs/stripe-js/react#usestripe-hook\n */\n\nvar useStripe = function useStripe() {\n  var _useElementsOrCheckou = useElementsOrCheckoutSdkContextWithUseCase('calls useStripe()'),\n      stripe = _useElementsOrCheckou.stripe;\n\n  return stripe;\n};\n\n/**\n * Requires beta access:\n * Contact [Stripe support](https://support.stripe.com/) for more information.\n *\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar AuBankAccountElement = createElementComponent('auBankAccount', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar CardElement = createElementComponent('card', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar CardNumberElement = createElementComponent('cardNumber', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar CardExpiryElement = createElementComponent('cardExpiry', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar CardCvcElement = createElementComponent('cardCvc', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar FpxBankElement = createElementComponent('fpxBank', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar IbanElement = createElementComponent('iban', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar IdealBankElement = createElementComponent('idealBank', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar P24BankElement = createElementComponent('p24Bank', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar EpsBankElement = createElementComponent('epsBank', isServer);\nvar PaymentElement = createElementComponent('payment', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar ExpressCheckoutElement = createElementComponent('expressCheckout', isServer);\n/**\n * Requires beta access:\n * Contact [Stripe support](https://support.stripe.com/) for more information.\n */\n\nvar CurrencySelectorElement = createElementComponent('currencySelector', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar PaymentRequestButtonElement = createElementComponent('paymentRequestButton', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar LinkAuthenticationElement = createElementComponent('linkAuthentication', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar AddressElement = createElementComponent('address', isServer);\n/**\n * @deprecated\n * Use `AddressElement` instead.\n *\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar ShippingAddressElement = createElementComponent('shippingAddress', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar PaymentMethodMessagingElement = createElementComponent('paymentMethodMessaging', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar AffirmMessageElement = createElementComponent('affirmMessage', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar AfterpayClearpayMessageElement = createElementComponent('afterpayClearpayMessage', isServer);\n/**\n * Requires beta access:\n * Contact [Stripe support](https://support.stripe.com/) for more information.\n */\n\nvar TaxIdElement = createElementComponent('taxId', isServer);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stripe/react-stripe-js/dist/react-stripe.esm.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stripe/stripe-js/dist/index.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/@stripe/stripe-js/dist/index.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadStripe: () => (/* binding */ loadStripe)\n/* harmony export */ });\nvar RELEASE_TRAIN = 'basil';\n\nvar runtimeVersionToUrlVersion = function runtimeVersionToUrlVersion(version) {\n  return version === 3 ? 'v3' : version;\n};\n\nvar ORIGIN = 'https://js.stripe.com';\nvar STRIPE_JS_URL = \"\".concat(ORIGIN, \"/\").concat(RELEASE_TRAIN, \"/stripe.js\");\nvar V3_URL_REGEX = /^https:\\/\\/js\\.stripe\\.com\\/v3\\/?(\\?.*)?$/;\nvar STRIPE_JS_URL_REGEX = /^https:\\/\\/js\\.stripe\\.com\\/(v3|[a-z]+)\\/stripe\\.js(\\?.*)?$/;\nvar EXISTING_SCRIPT_MESSAGE = 'loadStripe.setLoadParameters was called but an existing Stripe.js script already exists in the document; existing script parameters will be used';\n\nvar isStripeJSURL = function isStripeJSURL(url) {\n  return V3_URL_REGEX.test(url) || STRIPE_JS_URL_REGEX.test(url);\n};\n\nvar findScript = function findScript() {\n  var scripts = document.querySelectorAll(\"script[src^=\\\"\".concat(ORIGIN, \"\\\"]\"));\n\n  for (var i = 0; i < scripts.length; i++) {\n    var script = scripts[i];\n\n    if (!isStripeJSURL(script.src)) {\n      continue;\n    }\n\n    return script;\n  }\n\n  return null;\n};\n\nvar injectScript = function injectScript(params) {\n  var queryString = params && !params.advancedFraudSignals ? '?advancedFraudSignals=false' : '';\n  var script = document.createElement('script');\n  script.src = \"\".concat(STRIPE_JS_URL).concat(queryString);\n  var headOrBody = document.head || document.body;\n\n  if (!headOrBody) {\n    throw new Error('Expected document.body not to be null. Stripe.js requires a <body> element.');\n  }\n\n  headOrBody.appendChild(script);\n  return script;\n};\n\nvar registerWrapper = function registerWrapper(stripe, startTime) {\n  if (!stripe || !stripe._registerWrapper) {\n    return;\n  }\n\n  stripe._registerWrapper({\n    name: 'stripe-js',\n    version: \"7.9.0\",\n    startTime: startTime\n  });\n};\n\nvar stripePromise$1 = null;\nvar onErrorListener = null;\nvar onLoadListener = null;\n\nvar onError = function onError(reject) {\n  return function (cause) {\n    reject(new Error('Failed to load Stripe.js', {\n      cause: cause\n    }));\n  };\n};\n\nvar onLoad = function onLoad(resolve, reject) {\n  return function () {\n    if (window.Stripe) {\n      resolve(window.Stripe);\n    } else {\n      reject(new Error('Stripe.js not available'));\n    }\n  };\n};\n\nvar loadScript = function loadScript(params) {\n  // Ensure that we only attempt to load Stripe.js at most once\n  if (stripePromise$1 !== null) {\n    return stripePromise$1;\n  }\n\n  stripePromise$1 = new Promise(function (resolve, reject) {\n    if (typeof window === 'undefined' || typeof document === 'undefined') {\n      // Resolve to null when imported server side. This makes the module\n      // safe to import in an isomorphic code base.\n      resolve(null);\n      return;\n    }\n\n    if (window.Stripe && params) {\n      console.warn(EXISTING_SCRIPT_MESSAGE);\n    }\n\n    if (window.Stripe) {\n      resolve(window.Stripe);\n      return;\n    }\n\n    try {\n      var script = findScript();\n\n      if (script && params) {\n        console.warn(EXISTING_SCRIPT_MESSAGE);\n      } else if (!script) {\n        script = injectScript(params);\n      } else if (script && onLoadListener !== null && onErrorListener !== null) {\n        var _script$parentNode;\n\n        // remove event listeners\n        script.removeEventListener('load', onLoadListener);\n        script.removeEventListener('error', onErrorListener); // if script exists, but we are reloading due to an error,\n        // reload script to trigger 'load' event\n\n        (_script$parentNode = script.parentNode) === null || _script$parentNode === void 0 ? void 0 : _script$parentNode.removeChild(script);\n        script = injectScript(params);\n      }\n\n      onLoadListener = onLoad(resolve, reject);\n      onErrorListener = onError(reject);\n      script.addEventListener('load', onLoadListener);\n      script.addEventListener('error', onErrorListener);\n    } catch (error) {\n      reject(error);\n      return;\n    }\n  }); // Resets stripePromise on error\n\n  return stripePromise$1[\"catch\"](function (error) {\n    stripePromise$1 = null;\n    return Promise.reject(error);\n  });\n};\nvar initStripe = function initStripe(maybeStripe, args, startTime) {\n  if (maybeStripe === null) {\n    return null;\n  }\n\n  var pk = args[0];\n  var isTestKey = pk.match(/^pk_test/); // @ts-expect-error this is not publicly typed\n\n  var version = runtimeVersionToUrlVersion(maybeStripe.version);\n  var expectedVersion = RELEASE_TRAIN;\n\n  if (isTestKey && version !== expectedVersion) {\n    console.warn(\"Stripe.js@\".concat(version, \" was loaded on the page, but @stripe/stripe-js@\").concat(\"7.9.0\", \" expected Stripe.js@\").concat(expectedVersion, \". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning\"));\n  }\n\n  var stripe = maybeStripe.apply(undefined, args);\n  registerWrapper(stripe, startTime);\n  return stripe;\n}; // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n\nvar stripePromise;\nvar loadCalled = false;\n\nvar getStripePromise = function getStripePromise() {\n  if (stripePromise) {\n    return stripePromise;\n  }\n\n  stripePromise = loadScript(null)[\"catch\"](function (error) {\n    // clear cache on error\n    stripePromise = null;\n    return Promise.reject(error);\n  });\n  return stripePromise;\n}; // Execute our own script injection after a tick to give users time to do their\n// own script injection.\n\n\nPromise.resolve().then(function () {\n  return getStripePromise();\n})[\"catch\"](function (error) {\n  if (!loadCalled) {\n    console.warn(error);\n  }\n});\nvar loadStripe = function loadStripe() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  loadCalled = true;\n  var startTime = Date.now(); // if previous attempts are unsuccessful, will re-load script\n\n  return getStripePromise().then(function (maybeStripe) {\n    return initStripe(maybeStripe, args, startTime);\n  });\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stripe/stripe-js/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stripe/stripe-js/lib/index.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@stripe/stripe-js/lib/index.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadStripe: () => (/* reexport safe */ _dist_index_mjs__WEBPACK_IMPORTED_MODULE_0__.loadStripe)\n/* harmony export */ });\n/* harmony import */ var _dist_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../dist/index.mjs */ \"(ssr)/./node_modules/@stripe/stripe-js/dist/index.mjs\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHN0cmlwZS9zdHJpcGUtanMvbGliL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFrQyIsInNvdXJjZXMiOlsiL1ZvbHVtZXMvRmlsZXMvVGVjaG5vbG93YXktTmV3LVdlYnNpdGUvVGVjaG5vbG93YXkvbm9kZV9tb2R1bGVzL0BzdHJpcGUvc3RyaXBlLWpzL2xpYi9pbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi4vZGlzdC9pbmRleC5tanMnO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stripe/stripe-js/lib/index.mjs\n");

/***/ })

};
;