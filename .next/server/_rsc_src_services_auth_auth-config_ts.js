"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_services_auth_auth-config_ts";
exports.ids = ["_rsc_src_services_auth_auth-config_ts"];
exports.modules = {

/***/ "(rsc)/./src/services/auth/auth-config.ts":
/*!******************************************!*\
  !*** ./src/services/auth/auth-config.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _config_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config/prisma */ \"(rsc)/./src/config/prisma.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n\n\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    console.log('Missing credentials');\n                    return null;\n                }\n                try {\n                    console.log('Attempting auth for:', credentials.email);\n                    // Test database connection first\n                    await _config_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$connect();\n                    console.log('Database connected');\n                    const user = await _config_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.users.findFirst({\n                        where: {\n                            email: credentials.email.trim().toLowerCase(),\n                            isactive: true // Only allow active users to login\n                        }\n                    });\n                    console.log('User lookup result:', user ? {\n                        id: user.id,\n                        email: user.email,\n                        role: user.role,\n                        isactive: user.isactive\n                    } : 'Not found');\n                    if (!user || !user.password) {\n                        console.log('User not found, no password, or user is inactive');\n                        // await AuditLogger.logAuth(\n                        //   'LOGIN_FAILED',\n                        //   undefined,\n                        //   { email: credentials.email, reason: 'User not found, no password, user is inactive, or email not verified' }\n                        // )\n                        return null;\n                    }\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"].compare(credentials.password, user.password);\n                    console.log('Password validation result:', isPasswordValid);\n                    if (!isPasswordValid) {\n                        console.log('Invalid password');\n                        // await AuditLogger.logAuth(\n                        //   'LOGIN_FAILED',\n                        //   user.id.toString(),\n                        //   { email: credentials.email, reason: 'Invalid password' }\n                        // )\n                        return null;\n                    }\n                    console.log('Authentication successful');\n                    // await AuditLogger.logAuth(\n                    //   'LOGIN_SUCCESS',\n                    //   user.id.toString(),\n                    //   { email: credentials.email }\n                    // )\n                    return {\n                        id: user.id.toString(),\n                        email: user.email,\n                        name: `${user.firstname} ${user.lastname}`.trim(),\n                        role: user.role,\n                        imageurl: user.imageurl\n                    };\n                } catch (error) {\n                    console.error('Auth error details:', {\n                        error: error instanceof Error ? error.message : 'Unknown error',\n                        stack: error instanceof Error ? error.stack : undefined,\n                        email: credentials.email\n                    });\n                    // Try to log the error, but don't fail if audit logging fails\n                    // try {\n                    //   await AuditLogger.logAuth(\n                    //     'LOGIN_FAILED',\n                    //     undefined,\n                    //     { email: credentials.email, error: error instanceof Error ? error.message : 'Unknown error' }\n                    //   )\n                    // } catch (auditError) {\n                    //   console.error('Audit logging failed:', auditError)\n                    // }\n                    return null;\n                }\n            }\n        })\n    ],\n    session: {\n        strategy: 'jwt',\n        maxAge: 30 * 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 30 * 24 * 60 * 60\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n                token.id = user.id;\n                token.imageurl = user.imageurl;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                session.user.imageurl = token.imageurl;\n            }\n            return session;\n        }\n    },\n    debug: false,\n    logger: {\n        error (code, metadata) {\n            console.error('NextAuth Error:', {\n                code,\n                metadata\n            });\n        },\n        warn (code) {\n            console.warn('NextAuth Warning:', code);\n        },\n        debug (code, metadata) {\n            if (true) {\n                console.log('NextAuth Debug:', {\n                    code,\n                    metadata\n                });\n            }\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/auth/auth-config.ts\n");

/***/ })

};
;