/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/settings/essential-status/route";
exports.ids = ["app/api/admin/settings/essential-status/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fsettings%2Fessential-status%2Froute&page=%2Fapi%2Fadmin%2Fsettings%2Fessential-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fsettings%2Fessential-status%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fsettings%2Fessential-status%2Froute&page=%2Fapi%2Fadmin%2Fsettings%2Fessential-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fsettings%2Fessential-status%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _Volumes_Files_Technoloway_New_Website_Technoloway_src_app_api_admin_settings_essential_status_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/admin/settings/essential-status/route.ts */ \"(rsc)/./src/app/api/admin/settings/essential-status/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/settings/essential-status/route\",\n        pathname: \"/api/admin/settings/essential-status\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/settings/essential-status/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/app/api/admin/settings/essential-status/route.ts\",\n    nextConfigOutput,\n    userland: _Volumes_Files_Technoloway_New_Website_Technoloway_src_app_api_admin_settings_essential_status_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/admin/settings/essential-status/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fsettings%2Fessential-status%2Froute&page=%2Fapi%2Fadmin%2Fsettings%2Fessential-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fsettings%2Fessential-status%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/settings/essential-status/route.ts":
/*!**************************************************************!*\
  !*** ./src/app/api/admin/settings/essential-status/route.ts ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_settings_loader__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/settings-loader */ \"(rsc)/./src/lib/settings-loader.ts\");\n\n\nasync function GET() {\n    try {\n        const result = await (0,_lib_settings_loader__WEBPACK_IMPORTED_MODULE_1__.getEssentialSettingsStatus)();\n        if (result.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: result.error\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error('Error getting essential settings status:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to get essential settings status'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hZG1pbi9zZXR0aW5ncy9lc3NlbnRpYWwtc3RhdHVzL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF3RDtBQUNXO0FBRTVELGVBQWVFO0lBQ3BCLElBQUk7UUFDRixNQUFNQyxTQUFTLE1BQU1GLGdGQUEwQkE7UUFFL0MsSUFBSUUsT0FBT0MsT0FBTyxFQUFFO1lBQ2xCLE9BQU9KLHFEQUFZQSxDQUFDSyxJQUFJLENBQUNGO1FBQzNCLE9BQU87WUFDTCxPQUFPSCxxREFBWUEsQ0FBQ0ssSUFBSSxDQUFDO2dCQUN2QkQsU0FBUztnQkFDVEUsT0FBT0gsT0FBT0csS0FBSztZQUNyQixHQUFHO2dCQUFFQyxRQUFRO1lBQUk7UUFDbkI7SUFDRixFQUFFLE9BQU9ELE9BQU87UUFDZEUsUUFBUUYsS0FBSyxDQUFDLDRDQUE0Q0E7UUFDMUQsT0FBT04scURBQVlBLENBQUNLLElBQUksQ0FBQztZQUN2QkQsU0FBUztZQUNURSxPQUFPO1FBQ1QsR0FBRztZQUFFQyxRQUFRO1FBQUk7SUFDbkI7QUFDRiIsInNvdXJjZXMiOlsiL1ZvbHVtZXMvRmlsZXMvVGVjaG5vbG93YXktTmV3LVdlYnNpdGUvVGVjaG5vbG93YXkvc3JjL2FwcC9hcGkvYWRtaW4vc2V0dGluZ3MvZXNzZW50aWFsLXN0YXR1cy9yb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgZ2V0RXNzZW50aWFsU2V0dGluZ3NTdGF0dXMgfSBmcm9tICdAL2xpYi9zZXR0aW5ncy1sb2FkZXInO1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKCkge1xuICB0cnkge1xuICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGdldEVzc2VudGlhbFNldHRpbmdzU3RhdHVzKCk7XG4gICAgXG4gICAgaWYgKHJlc3VsdC5zdWNjZXNzKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24ocmVzdWx0KTtcbiAgICB9IGVsc2Uge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIGVycm9yOiByZXN1bHQuZXJyb3JcbiAgICAgIH0sIHsgc3RhdHVzOiA1MDAgfSk7XG4gICAgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdldHRpbmcgZXNzZW50aWFsIHNldHRpbmdzIHN0YXR1czonLCBlcnJvcik7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgZXJyb3I6ICdGYWlsZWQgdG8gZ2V0IGVzc2VudGlhbCBzZXR0aW5ncyBzdGF0dXMnXG4gICAgfSwgeyBzdGF0dXM6IDUwMCB9KTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsImdldEVzc2VudGlhbFNldHRpbmdzU3RhdHVzIiwiR0VUIiwicmVzdWx0Iiwic3VjY2VzcyIsImpzb24iLCJlcnJvciIsInN0YXR1cyIsImNvbnNvbGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/settings/essential-status/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/settings-loader.ts":
/*!************************************!*\
  !*** ./src/lib/settings-loader.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bulkMarkForSync: () => (/* binding */ bulkMarkForSync),\n/* harmony export */   disconnect: () => (/* binding */ disconnect),\n/* harmony export */   ensureEssentialSettings: () => (/* binding */ ensureEssentialSettings),\n/* harmony export */   getEssentialSettingsStatus: () => (/* binding */ getEssentialSettingsStatus),\n/* harmony export */   loadEssentialFromConfig: () => (/* binding */ loadEssentialFromConfig),\n/* harmony export */   markSettingForSync: () => (/* binding */ markSettingForSync),\n/* harmony export */   syncConfigToDatabase: () => (/* binding */ syncConfigToDatabase),\n/* harmony export */   syncEssentialToConfig: () => (/* binding */ syncEssentialToConfig)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\n/**\n * Load essential settings from JSON config file\n */ async function loadEssentialFromConfig() {\n    try {\n        const configPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'src/config/essential-settings.json');\n        const configData = await fs_promises__WEBPACK_IMPORTED_MODULE_1___default().readFile(configPath, 'utf-8');\n        const config = JSON.parse(configData);\n        console.log('🌱 Loading essential settings from config...');\n        for (const setting of config.settings){\n            try {\n                // Check if setting exists\n                const existing = await prisma.sitesettings.findFirst({\n                    where: {\n                        key: setting.key\n                    }\n                });\n                if (!existing) {\n                    // Create new essential setting\n                    await prisma.sitesettings.create({\n                        data: {\n                            key: setting.key,\n                            value: setting.value,\n                            description: setting.description,\n                            category: setting.category,\n                            fieldtype: setting.fieldtype,\n                            options: setting.options,\n                            isactive: setting.isactive,\n                            ispublic: setting.ispublic,\n                            is_protected: setting.is_protected,\n                            is_essential: setting.is_essential,\n                            sync_to_config: true,\n                            config_priority: setting.config_priority,\n                            createdat: new Date(),\n                            updatedat: new Date()\n                        }\n                    });\n                    console.log(`✅ Created essential setting: ${setting.key}`);\n                } else {\n                    // Update existing setting to be protected and essential\n                    await prisma.sitesettings.update({\n                        where: {\n                            id: existing.id\n                        },\n                        data: {\n                            is_protected: true,\n                            is_essential: true,\n                            sync_to_config: true,\n                            config_priority: setting.config_priority || 0,\n                            updatedat: new Date()\n                        }\n                    });\n                    console.log(`🔒 Protected existing setting: ${setting.key}`);\n                }\n            } catch (error) {\n                console.error(`❌ Error with setting ${setting.key}:`, error);\n            }\n        }\n        console.log('🎉 Essential settings loading completed!');\n        return {\n            success: true,\n            message: 'Essential settings loaded successfully'\n        };\n    } catch (error) {\n        console.error('❌ Error loading essential settings:', error);\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : 'Unknown error'\n        };\n    }\n}\n/**\n * Ensure essential settings exist in database\n */ async function ensureEssentialSettings() {\n    try {\n        // Load from config file first\n        await loadEssentialFromConfig();\n        // Get all essential settings from database\n        const essentialSettings = await prisma.sitesettings.findMany({\n            where: {\n                is_essential: true\n            },\n            orderBy: {\n                config_priority: 'asc'\n            }\n        });\n        console.log(`✅ Found ${essentialSettings.length} essential settings in database`);\n        return {\n            success: true,\n            count: essentialSettings.length\n        };\n    } catch (error) {\n        console.error('❌ Error ensuring essential settings:', error);\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : 'Unknown error'\n        };\n    }\n}\n/**\n * Sync essential settings from database to config file\n */ async function syncEssentialToConfig() {\n    try {\n        console.log('🔄 Syncing essential settings to config file...');\n        // Get all essential settings from database\n        const essentialSettings = await prisma.sitesettings.findMany({\n            where: {\n                is_essential: true,\n                sync_to_config: true\n            },\n            orderBy: {\n                config_priority: 'asc'\n            }\n        });\n        // Generate config file content\n        const configContent = {\n            version: \"1.0.0\",\n            last_updated: new Date().toISOString(),\n            description: \"Essential settings for Technoloway website - Auto-generated from database\",\n            settings: essentialSettings.map((setting)=>({\n                    key: setting.key,\n                    value: setting.value,\n                    description: setting.description,\n                    category: setting.category,\n                    fieldtype: setting.fieldtype,\n                    options: setting.options,\n                    isactive: setting.isactive,\n                    ispublic: setting.ispublic,\n                    is_protected: setting.is_protected,\n                    is_essential: setting.is_essential,\n                    config_priority: setting.config_priority\n                }))\n        };\n        // Write to config file\n        const configPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'src/config/essential-settings.json');\n        await fs_promises__WEBPACK_IMPORTED_MODULE_1___default().writeFile(configPath, JSON.stringify(configContent, null, 2));\n        // Update sync timestamp\n        await prisma.sitesettings.updateMany({\n            where: {\n                is_essential: true,\n                sync_to_config: true\n            },\n            data: {\n                last_synced: new Date()\n            }\n        });\n        console.log(`✅ Essential settings synced to config file (${essentialSettings.length} settings)`);\n        return {\n            success: true,\n            message: 'Essential settings synced to config file',\n            count: essentialSettings.length\n        };\n    } catch (error) {\n        console.error('❌ Error syncing essential settings:', error);\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : 'Unknown error'\n        };\n    }\n}\n/**\n * Mark setting for config sync\n */ async function markSettingForSync(settingId) {\n    try {\n        await prisma.sitesettings.update({\n            where: {\n                id: settingId\n            },\n            data: {\n                sync_to_config: true,\n                config_priority: Date.now(),\n                updatedat: new Date()\n            }\n        });\n        return {\n            success: true,\n            message: 'Setting marked for config sync'\n        };\n    } catch (error) {\n        console.error('❌ Error marking setting for sync:', error);\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : 'Unknown error'\n        };\n    }\n}\n/**\n * Bulk mark settings for sync\n */ async function bulkMarkForSync(settingIds) {\n    try {\n        await prisma.sitesettings.updateMany({\n            where: {\n                id: {\n                    in: settingIds\n                }\n            },\n            data: {\n                sync_to_config: true,\n                config_priority: Date.now(),\n                updatedat: new Date()\n            }\n        });\n        return {\n            success: true,\n            message: `${settingIds.length} settings marked for sync`\n        };\n    } catch (error) {\n        console.error('❌ Error bulk marking settings for sync:', error);\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : 'Unknown error'\n        };\n    }\n}\n/**\n * Get essential settings status\n */ async function getEssentialSettingsStatus() {\n    try {\n        const totalEssential = await prisma.sitesettings.count({\n            where: {\n                is_essential: true\n            }\n        });\n        const syncedEssential = await prisma.sitesettings.count({\n            where: {\n                is_essential: true,\n                sync_to_config: true\n            }\n        });\n        const lastSync = await prisma.sitesettings.findFirst({\n            where: {\n                is_essential: true,\n                sync_to_config: true,\n                last_synced: {\n                    not: null\n                }\n            },\n            orderBy: {\n                last_synced: 'desc'\n            },\n            select: {\n                last_synced: true\n            }\n        });\n        return {\n            success: true,\n            totalEssential,\n            syncedEssential,\n            lastSync: lastSync?.last_synced || null,\n            syncPercentage: totalEssential > 0 ? Math.round(syncedEssential / totalEssential * 100) : 0\n        };\n    } catch (error) {\n        console.error('❌ Error getting essential settings status:', error);\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : 'Unknown error'\n        };\n    }\n}\n/**\n * Sync values from config file to database (reverse sync)\n */ async function syncConfigToDatabase() {\n    try {\n        console.log('🔄 Syncing config values to database...');\n        const configPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'src/config/essential-settings.json');\n        const configData = await fs_promises__WEBPACK_IMPORTED_MODULE_1___default().readFile(configPath, 'utf-8');\n        const config = JSON.parse(configData);\n        let updatedCount = 0;\n        for (const setting of config.settings){\n            try {\n                // Find existing setting\n                const existing = await prisma.sitesettings.findFirst({\n                    where: {\n                        key: setting.key\n                    }\n                });\n                if (existing) {\n                    // Update the setting with values from config\n                    await prisma.sitesettings.update({\n                        where: {\n                            id: existing.id\n                        },\n                        data: {\n                            value: setting.value,\n                            description: setting.description,\n                            category: setting.category,\n                            fieldtype: setting.fieldtype,\n                            options: setting.options,\n                            isactive: setting.isactive,\n                            ispublic: setting.ispublic,\n                            is_protected: setting.is_protected,\n                            is_essential: setting.is_essential,\n                            config_priority: setting.config_priority,\n                            updatedat: new Date()\n                        }\n                    });\n                    console.log(`✅ Updated setting from config: ${setting.key}`);\n                    updatedCount++;\n                } else {\n                    // Create new setting if it doesn't exist\n                    await prisma.sitesettings.create({\n                        data: {\n                            key: setting.key,\n                            value: setting.value,\n                            description: setting.description,\n                            category: setting.category,\n                            fieldtype: setting.fieldtype,\n                            options: setting.options,\n                            isactive: setting.isactive,\n                            ispublic: setting.ispublic,\n                            is_protected: setting.is_protected,\n                            is_essential: setting.is_essential,\n                            sync_to_config: true,\n                            config_priority: setting.config_priority,\n                            createdat: new Date(),\n                            updatedat: new Date()\n                        }\n                    });\n                    console.log(`✅ Created setting from config: ${setting.key}`);\n                    updatedCount++;\n                }\n            } catch (error) {\n                console.error(`❌ Error updating setting ${setting.key}:`, error);\n            }\n        }\n        console.log(`✅ Synced ${updatedCount} settings from config to database`);\n        return {\n            success: true,\n            updatedCount\n        };\n    } catch (error) {\n        console.error('❌ Error syncing config to database:', error);\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : 'Unknown error'\n        };\n    }\n}\n/**\n * Cleanup function\n */ async function disconnect() {\n    await prisma.$disconnect();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/settings-loader.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fsettings%2Fessential-status%2Froute&page=%2Fapi%2Fadmin%2Fsettings%2Fessential-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fsettings%2Fessential-status%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();