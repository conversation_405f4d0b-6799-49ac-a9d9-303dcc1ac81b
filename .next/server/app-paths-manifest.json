{"/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/admin/settings/categories/route": "app/api/admin/settings/categories/route.js", "/api/admin/settings/essential-status/route": "app/api/admin/settings/essential-status/route.js", "/api/admin/settings/route": "app/api/admin/settings/route.js", "/api/admin/categories/route": "app/api/admin/categories/route.js", "/api/admin/services/route": "app/api/admin/services/route.js", "/page": "app/page.js", "/admin-dashboard/page": "app/admin-dashboard/page.js", "/admin-dashboard/settings/page": "app/admin-dashboard/settings/page.js", "/admin-dashboard/clients-manager/page": "app/admin-dashboard/clients-manager/page.js", "/admin-dashboard/services/page": "app/admin-dashboard/services/page.js"}