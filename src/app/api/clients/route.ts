import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/config/prisma';

/**
 * GET /api/clients
 * Public endpoint to retrieve clients for hero sections
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '0'); // 0 means no limit
    const featured = searchParams.get('featured');

    // Build where clause
    const where: any = {
      isactive: true // Only active clients
    };

    // If featured parameter is provided, we could add logic here
    // For now, we'll get all active clients

    // Get clients with basic information needed for hero sections
    const clients = await prisma.clients.findMany({
      where,
      select: {
        id: true,
        companyname: true,
        logourl: true,
        contactname: true,
        contactemail: true,
        city: true,
        state: true,
        country: true,
        createdat: true,
      },
      orderBy: {
        companyname: 'asc', // Sort by company name alphabetically
      },
      ...(limit > 0 && { take: limit }), // Only apply limit if greater than 0
    });

    // Transform the data for frontend
    const transformedClients = clients.map(client => ({
      id: Number(client.id),
      companyname: client.companyname,
      logourl: client.logourl,
      contactname: client.contactname,
      contactemail: client.contactemail,
      city: client.city,
      state: client.state,
      country: client.country,
      createdat: client.createdat.toISOString(),
    }));

    return NextResponse.json({
      success: true,
      clients: transformedClients,
      count: transformedClients.length
    });

  } catch (error) {
    console.error('Error fetching clients:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch clients',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}
