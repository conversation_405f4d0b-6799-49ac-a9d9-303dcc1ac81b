'use client';

import React, { useState, useEffect } from 'react';

interface Client {
  id: string;
  companyname: string;
  logourl?: string;
}

// Style constants
const LOGO_SIZE = '60px'; // Increased from 40px to 60px
const DRAG_SENSITIVITY = 0.4;
const TEXT_WIDTH_MULTIPLIER = 10;
const MIN_TEXT_WIDTH = 60;
const TEXT_PADDING = 5; // Reduced from 20 to 5
const LOGO_GAP = 0; // No gap between logo and client name

const logoContainerStyle = {
  width: LOGO_SIZE,
  height: LOGO_SIZE,
  borderRadius: '50%',
  background: 'linear-gradient(var(--bg-color), var(--bg-color)) padding-box, linear-gradient(to left, var(--accent-color), var(--accent-secondary-color)) border-box',
  border: '1px solid transparent',
  padding: '2px', // Space for the border
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center'
};

const logoStyle = {
  width: 'calc(100% - 4px)', // Slightly smaller to account for border
  height: 'calc(100% - 4px)',
  borderRadius: '50%',
  backgroundSize: 'cover',
  backgroundRepeat: 'no-repeat',
  backgroundPosition: 'center',
  backgroundColor: 'transparent',
  overflow: 'hidden'
};

const clientItemStyle = (textLength: number) => {
  const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);
  const totalWidth = parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;

  return {
    display: 'flex',
    alignItems: 'center',
    gap: `${LOGO_GAP}px`,
    width: `${totalWidth}px`,
    flexShrink: 0,
    marginRight: '0px', // No margin since container handles spacing
    overflow: 'hidden'
  };
};

const companyNameStyle = {
  color: 'white',
  fontSize: '18px',
  fontWeight: 'normal',
  fontFamily: 'sans-serif',
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  flex: 1
};

const containerStyle = {
  overflow: 'hidden',
  display: 'flex',
  alignItems: 'center',
  gap: '20px' // Consistent gap between all client blocks
};

const trackStyle = (isDragging: boolean, scrollLeft: number) => ({
  display: 'flex',
  alignItems: 'center',
  gap: '0px',
  transition: isDragging ? 'none' : 'transform 2s ease-in-out',
  transform: `translateX(-${scrollLeft}px)`
});

// Logo component
const ClientLogo: React.FC<{ logourl?: string }> = ({ logourl }) => (
  <div style={logoContainerStyle}>
    <div style={{
      ...logoStyle,
      backgroundImage: `url(${logourl || "/images/icon-testimonial-logo.svg"})`
    }} />
  </div>
);

// Client item component
const ClientItem: React.FC<{ client: Client; index: number }> = ({ client, index }) => {
  const textLength = client.companyname.length;
  return (
    <div key={client.id || index} style={clientItemStyle(textLength)}>
      <ClientLogo logourl={client.logourl} />
      <div style={companyNameStyle}>
        {client.companyname}
      </div>
    </div>
  );
};

// Helper function to calculate client width
const calculateClientWidth = (textLength: number): number => {
  const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);
  return parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;
};

// Helper function to calculate total width
const calculateTotalWidth = (clients: Client[]): number => {
  return clients.reduce((sum, client) => {
    return sum + calculateClientWidth(client.companyname.length);
  }, 0);
};

const HomeMainHero: React.FC = () => {
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(160); // Start with first client fully hidden
  const [autoScrollInterval, setAutoScrollInterval] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const fetchClients = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/clients');
        const data = await response.json();
        if (data.success) {
          setClients(data.clients || []);
          // Set initial scroll position to hide first client
          if (data.clients && data.clients.length > 0) {
            const firstClientWidth = calculateClientWidth(data.clients[0].companyname.length);
            setScrollLeft(firstClientWidth);
          }
        }
      } catch (error) {
        console.error('Error fetching clients:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchClients();
  }, []);

  // Auto-scroll function
  useEffect(() => {
    if (clients.length === 0 || isDragging) return;

    const interval = setInterval(() => {
      setScrollLeft(prevScrollLeft => {
        const totalWidth = calculateTotalWidth(clients);
        const firstClientWidth = calculateClientWidth(clients[0].companyname.length);
        
        let newScrollLeft = prevScrollLeft + firstClientWidth;
        
        if (newScrollLeft >= totalWidth) {
          newScrollLeft = firstClientWidth;
        }
        
        return newScrollLeft;
      });
    }, 5000);

    setAutoScrollInterval(interval);

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [clients.length, isDragging]);

  // Cleanup interval on unmount
  useEffect(() => {
    return () => {
      if (autoScrollInterval) {
        clearInterval(autoScrollInterval);
      }
    };
  }, [autoScrollInterval]);

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setStartX(e.pageX);
    // Stop auto-scroll when user starts dragging
    if (autoScrollInterval) {
      clearInterval(autoScrollInterval);
      setAutoScrollInterval(null);
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return;
    e.preventDefault();
    const x = e.pageX;
    const walk = (x - startX) * DRAG_SENSITIVITY;
    let newScrollLeft = scrollLeft - walk;
    
    const totalWidth = calculateTotalWidth(clients);
    
    // Loop back to beginning when reaching the end
    if (newScrollLeft >= totalWidth) {
      const firstClientWidth = calculateClientWidth(clients[0]?.companyname.length || 0);
      newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;
    }
    // Loop back to end when going before beginning
    else if (newScrollLeft < 0) {
      newScrollLeft = totalWidth + newScrollLeft;
    }
    
    setScrollLeft(newScrollLeft);
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    // Restart auto-scroll after user stops dragging
    if (clients.length > 0) {
      const interval = setInterval(() => {
        setScrollLeft(prevScrollLeft => {
          const totalWidth = calculateTotalWidth(clients);
          const firstClientWidth = calculateClientWidth(clients[0].companyname.length);
          
          let newScrollLeft = prevScrollLeft + firstClientWidth;
          
          if (newScrollLeft >= totalWidth) {
            newScrollLeft = firstClientWidth;
          }
          
          return newScrollLeft;
        });
      }, 5000);
      
      setAutoScrollInterval(interval);
    }
  };

  const handleMouseLeave = () => {
    setIsDragging(false);
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    setIsDragging(true);
    setStartX(e.touches[0].pageX);
    // Stop auto-scroll when user starts touching
    if (autoScrollInterval) {
      clearInterval(autoScrollInterval);
      setAutoScrollInterval(null);
    }
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging) return;
    e.preventDefault();
    const x = e.touches[0].pageX;
    const walk = (x - startX) * DRAG_SENSITIVITY;
    let newScrollLeft = scrollLeft - walk;
    
    const totalWidth = calculateTotalWidth(clients);
    
    // Loop back to beginning when reaching the end
    if (newScrollLeft >= totalWidth) {
      const firstClientWidth = calculateClientWidth(clients[0]?.companyname.length || 0);
      newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;
    }
    // Loop back to end when going before beginning
    else if (newScrollLeft < 0) {
      newScrollLeft = totalWidth + newScrollLeft;
    }
    
    setScrollLeft(newScrollLeft);
  };

  const handleTouchEnd = () => {
    setIsDragging(false);
    // Restart auto-scroll after user stops touching
    if (clients.length > 0) {
      const interval = setInterval(() => {
        setScrollLeft(prevScrollLeft => {
          const totalWidth = calculateTotalWidth(clients);
          const firstClientWidth = calculateClientWidth(clients[0].companyname.length);
          
          let newScrollLeft = prevScrollLeft + firstClientWidth;
          
          if (newScrollLeft >= totalWidth) {
            newScrollLeft = firstClientWidth;
          }
          
          return newScrollLeft;
        });
      }, 5000);
      
      setAutoScrollInterval(interval);
    }
  };

  const renderClientItems = () => {
    if (loading) {
      return (
        <div style={clientItemStyle(7)}> {/* "Loading" is 7 characters */}
          <ClientLogo />
          <div style={{ ...companyNameStyle, fontSize: '26px', fontWeight: 'bold' }}>
            Loading...
          </div>
        </div>
      );
    }

    if (clients.length === 0) {
      return (
        <div style={clientItemStyle(12)}> {/* "No clients yet" is 12 characters */}
          <ClientLogo />
          <div style={{ ...companyNameStyle, fontSize: '26px', fontWeight: 'bold' }}>
            No clients yet
          </div>
        </div>
      );
    }

    return (
      <>
        {clients.map((client, index) => (
          <ClientItem key={client.id || index} client={client} index={index} />
        ))}
        {/* Duplicate for seamless loop */}
        {clients.map((client, index) => (
          <ClientItem key={`duplicate-${client.id || index}`} client={client} index={index} />
        ))}
      </>
    );
  };

  return (
    <>
      {/* Hero Section Start */}
      <div className="hero">
        {/* Grid Lines Start */}
        <div className="grid-lines">
          <div className="grid-line-1" />
          <div className="grid-line-2" />
          <div className="grid-line-3" />
          <div className="grid-line-4" />
          <div className="grid-line-5" />
        </div>
        {/* Grid Lines End */}
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-12">
              {/* Hero Content Start */}
              <div className="hero-content">
                {/* Section Title Start */}
                <div className="section-title section-title-center">
                  <h3 className="wow fadeInUp">
                    Enterprise-grade software & web development solutions
                  </h3>
                  <h1
                    className="wow fadeInUp"
                    data-wow-delay="0.2s"
                    data-cursor="-opaque"
                  >
                    Advanced digital solutions that{" "}
                    <span>accelerate your growth</span>
                  </h1>
                  <p className="wow fadeInUp" data-wow-delay="0.4s">
                    Transform your vision into scalable applications and sophisticated web platforms.
                    Professional development services that deliver measurable results.
                  </p>
                </div>
                {/* Section Title End */}
                {/* Hero Button Start */}
                <div className="hero-btn wow fadeInUp" data-wow-delay="0.6s">
                  <a href="/main/contact" className="btn-default btn-highlighted">
                    Get Free Assessment
                  </a>
                  <a href="/main/services" className="btn-default">
                    View our services
                  </a>
                </div>
                {/* Hero Button End */}
              </div>
              {/* Hero Content End */}
            </div>
          </div>
          <div className="row">
            <div className="col-lg-12">
              {/* Hero Company Slider Start */}
              <div className="hero-company-slider">
                <p>
                  We're Trusted by more than <span className="counter">{clients.length}</span>+
                  companies
                </p>
                <div 
                  style={{ 
                    ...containerStyle,
                    cursor: isDragging ? 'grabbing' : 'grab'
                  }}
                  onMouseDown={handleMouseDown}
                  onMouseMove={handleMouseMove}
                  onMouseUp={handleMouseUp}
                  onMouseLeave={handleMouseLeave}
                  onTouchStart={handleTouchStart}
                  onTouchMove={handleTouchMove}
                  onTouchEnd={handleTouchEnd}
                >
                  <div style={trackStyle(isDragging, scrollLeft)}>
                    {renderClientItems()}
                  </div>
                </div>
              </div>
              {/* Hero Company Slider End */}
            </div>
          </div>
        </div>
      </div>
      {/* Hero Section End */}
    </>
  );
};

export default HomeMainHero;
