import React, { useState, useEffect } from 'react';

interface Client {
  id: string;
  companyname: string;
  logourl?: string;
}

const HomeImageHero: React.FC = () => {
  const [bgImage, setBgImage] = useState('/images/hero-bg.jpg'); // Default fallback
  const [clients, setClients] = useState<Client[]>([]);
  const [clientsLoading, setClientsLoading] = useState(true);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [currentClientIndex, setCurrentClientIndex] = useState(0); // Track which client is currently visible
  const [scrollLeft, setScrollLeft] = useState(0); // Start at beginning
  const [autoScrollInterval, setAutoScrollInterval] = useState<NodeJS.Timeout | null>(null);

  // Style constants
  const LOGO_SIZE = '60px'; // Increased from 40px to 60px
  const DRAG_SENSITIVITY = 0.4;
  const TEXT_WIDTH_MULTIPLIER = 10;
  const MIN_TEXT_WIDTH = 60;
  const TEXT_PADDING = 5; // Reduced from 20 to 5
  const LOGO_GAP = 0; // No gap between logo and client name

  // Helper function to calculate client width
  const calculateClientWidth = (textLength: number): number => {
    const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);
    return parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;
  };

  // Helper function to calculate total width
  const calculateTotalWidth = (clients: Client[]): number => {
    return clients.reduce((sum, client) => {
      return sum + calculateClientWidth(client.companyname.length);
    }, 0);
  };

  // Helper function to calculate scroll position for a specific client index
  const calculateScrollPositionForClient = (clients: Client[], targetIndex: number): number => {
    let scrollPosition = 0;
    for (let i = 0; i < targetIndex && i < clients.length; i++) {
      scrollPosition += calculateClientWidth(clients[i].companyname.length);
      // Add gap after each client (20px)
      scrollPosition += 20; // 20px gap between client blocks
    }
    return scrollPosition;
  };

  const logoContainerStyle = {
    width: LOGO_SIZE,
    height: LOGO_SIZE,
    borderRadius: '50%',
    background: 'linear-gradient(var(--bg-color), var(--bg-color)) padding-box, linear-gradient(to left, var(--accent-color), var(--accent-secondary-color)) border-box',
    border: '1px solid transparent',
    padding: '2px', // Space for the border
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  };

  const logoStyle = {
    width: 'calc(100% - 4px)', // Slightly smaller to account for border
    height: 'calc(100% - 4px)',
    borderRadius: '50%',
    backgroundSize: 'cover',
    backgroundRepeat: 'no-repeat',
    backgroundPosition: 'center',
    backgroundColor: 'transparent',
    overflow: 'hidden'
  };

  // Logo component
  const ClientLogo: React.FC<{ logourl?: string }> = ({ logourl }) => (
    <div style={logoContainerStyle}>
      <div style={{
        ...logoStyle,
        backgroundImage: `url(${logourl || "/images/icon-testimonial-logo.svg"})`
      }} />
    </div>
  );

  const clientItemStyle = (textLength: number) => {
    const textWidth = Math.max(textLength * TEXT_WIDTH_MULTIPLIER, MIN_TEXT_WIDTH);
    const totalWidth = parseInt(LOGO_SIZE) + LOGO_GAP + textWidth + TEXT_PADDING;

    return {
      display: 'flex',
      alignItems: 'center',
      gap: `${LOGO_GAP}px`,
      width: `${totalWidth}px`,
      flexShrink: 0,
      marginRight: '0px', // No margin since container handles spacing
      overflow: 'hidden'
    };
  };

  const companyNameStyle = {
    color: 'white',
    fontSize: '18px',
    fontWeight: 'normal',
    fontFamily: 'sans-serif',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    flex: 1
  };

  const containerStyle = {
    overflow: 'hidden',
    display: 'flex',
    alignItems: 'center',
    gap: '20px' // Consistent gap between all client blocks
  };

  // Client item component
  const ClientItem: React.FC<{ client: Client; index: number }> = ({ client, index }) => {
    const textLength = client.companyname.length;
    return (
      <div key={client.id || index} style={clientItemStyle(textLength)}>
        <ClientLogo logourl={client.logourl} />
        <div style={companyNameStyle}>
          {client.companyname}
        </div>
      </div>
    );
  };

  useEffect(() => {
    const fetchHeroImage = async () => {
      try {
        const response = await fetch('/api/admin/settings/hero-media');
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.media?.hero_main_bg_image) {
            setBgImage(data.media.hero_main_bg_image);
          }
        }
      } catch (error) {
        console.error('Error fetching hero image:', error);
        // Keep default image
      }
    };

    fetchHeroImage();
  }, []);

  useEffect(() => {
    const fetchClients = async () => {
      try {
        setClientsLoading(true);
        // Fetch all active clients without limit, sorted by name
        const response = await fetch('/api/clients?limit=0');
        const data = await response.json();
        if (data.success) {
          // Sort clients by company name to ensure alphabetical order
          const sortedClients = (data.clients || []).sort((a: Client, b: Client) =>
            a.companyname.localeCompare(b.companyname)
          );
          setClients(sortedClients);
          // Start with the first client fully visible
          setCurrentClientIndex(0);
          setScrollLeft(0);
        }
      } catch (error) {
        console.error('Error fetching clients:', error);
      } finally {
        setClientsLoading(false);
      }
    };
    fetchClients();
  }, []);

  // Auto-scroll function - moves one client at a time as complete blocks
  useEffect(() => {
    if (clients.length === 0 || isDragging) return;

    const interval = setInterval(() => {
      setCurrentClientIndex(prevIndex => {
        const nextIndex = (prevIndex + 1) % clients.length;

        // Calculate exact scroll position for the next client
        const nextScrollPosition = calculateScrollPositionForClient(clients, nextIndex);
        setScrollLeft(nextScrollPosition);

        return nextIndex;
      });
    }, 5000);

    setAutoScrollInterval(interval);

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [clients.length, isDragging]);

  // Cleanup interval on unmount
  useEffect(() => {
    return () => {
      if (autoScrollInterval) {
        clearInterval(autoScrollInterval);
      }
    };
  }, [autoScrollInterval]);

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setStartX(e.pageX);
    // Stop auto-scroll when user starts dragging
    if (autoScrollInterval) {
      clearInterval(autoScrollInterval);
      setAutoScrollInterval(null);
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return;
    e.preventDefault();
    const x = e.pageX;
    const walk = (x - startX) * DRAG_SENSITIVITY;
    let newScrollLeft = scrollLeft - walk;
    
    const totalWidth = calculateTotalWidth(clients);
    
    // Loop back to beginning when reaching the end
    if (newScrollLeft >= totalWidth) {
      const firstClientWidth = calculateClientWidth(clients[0]?.companyname.length || 0);
      newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;
    }
    // Loop back to end when going before beginning
    else if (newScrollLeft < 0) {
      newScrollLeft = totalWidth + newScrollLeft;
    }
    
    setScrollLeft(newScrollLeft);
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    // Restart auto-scroll after user stops dragging
    if (clients.length > 0) {
      const interval = setInterval(() => {
        setCurrentClientIndex(prevIndex => {
          const nextIndex = (prevIndex + 1) % clients.length;

          // Calculate exact scroll position for the next client
          const nextScrollPosition = calculateScrollPositionForClient(clients, nextIndex);
          setScrollLeft(nextScrollPosition);

          return nextIndex;
        });
      }, 5000);

      setAutoScrollInterval(interval);
    }
  };

  const handleMouseLeave = () => {
    setIsDragging(false);
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    setIsDragging(true);
    setStartX(e.touches[0].pageX);
    // Stop auto-scroll when user starts touching
    if (autoScrollInterval) {
      clearInterval(autoScrollInterval);
      setAutoScrollInterval(null);
    }
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging) return;
    e.preventDefault();
    const x = e.touches[0].pageX;
    const walk = (x - startX) * DRAG_SENSITIVITY;
    let newScrollLeft = scrollLeft - walk;
    
    const totalWidth = calculateTotalWidth(clients);
    
    // Loop back to beginning when reaching the end
    if (newScrollLeft >= totalWidth) {
      const firstClientWidth = calculateClientWidth(clients[0]?.companyname.length || 0);
      newScrollLeft = newScrollLeft - totalWidth + firstClientWidth;
    }
    // Loop back to end when going before beginning
    else if (newScrollLeft < 0) {
      newScrollLeft = totalWidth + newScrollLeft;
    }
    
    setScrollLeft(newScrollLeft);
  };

  const handleTouchEnd = () => {
    setIsDragging(false);
    // Restart auto-scroll after user stops touching
    if (clients.length > 0) {
      const interval = setInterval(() => {
        setCurrentClientIndex(prevIndex => {
          const nextIndex = (prevIndex + 1) % clients.length;

          // Calculate exact scroll position for the next client
          const nextScrollPosition = calculateScrollPositionForClient(clients, nextIndex);
          setScrollLeft(nextScrollPosition);

          return nextIndex;
        });
      }, 5000);

      setAutoScrollInterval(interval);
    }
  };

  const renderClientItems = () => {
    if (clientsLoading) {
      return (
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0px',
          width: '140px',
          flexShrink: 0,
          marginRight: '0px',
          overflow: 'hidden'
        }}>
          <ClientLogo />
          <div style={{
            color: 'white',
            fontSize: '26px',
            fontWeight: 'bold',
            fontFamily: 'sans-serif',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            flex: 1
          }}>
            Loading...
          </div>
        </div>
      );
    }

    if (clients.length === 0) {
      return (
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0px',
          width: '200px',
          flexShrink: 0,
          marginRight: '0px',
          overflow: 'hidden'
        }}>
          <ClientLogo />
          <div style={{
            color: 'white',
            fontSize: '26px',
            fontWeight: 'bold',
            fontFamily: 'sans-serif',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            flex: 1
          }}>
            No clients yet
          </div>
        </div>
      );
    }

    return (
      <>
        {clients.map((client, index) => (
          <ClientItem key={client.id || index} client={client} index={index} />
        ))}
        {/* Duplicate for seamless loop */}
        {clients.map((client, index) => (
          <ClientItem key={`duplicate-${client.id || index}`} client={client} index={index} />
        ))}
      </>
    );
  };

  return (
    <>
      {/* Hero Section Start */}
      <div 
        className="hero hero-bg-image parallaxie"
        style={{
          backgroundImage: `url(${bgImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      >
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-12">
              {/* Hero Content Start */}
              <div className="hero-content">
                {/* Section Title Start */}
                <div className="section-title section-title-center">
                  <h3 className="wow fadeInUp">
                    Enterprise-grade software & web development solutions
                  </h3>
                  <h1
                    className="wow fadeInUp"
                    data-wow-delay="0.2s"
                    data-cursor="-opaque"
                  >
                    Advanced digital solutions that{" "}
                    <span>accelerate your growth</span>
                  </h1>
                  <p className="wow fadeInUp" data-wow-delay="0.4s">
                    Transform your vision into scalable applications and sophisticated web platforms.
                    Professional development services that deliver measurable results.
                  </p>
                </div>
                {/* Section Title End */}
                {/* Hero Button Start */}
                <div className="hero-btn wow fadeInUp" data-wow-delay="0.6s">
                  <a href="/main/contact" className="btn-default btn-highlighted">
                    Get Free Assessment
                  </a>
                  <a href="/main/services" className="btn-default">
                    View our services
                  </a>
                </div>
                {/* Hero Button End */}
              </div>
              {/* Hero Content End */}
            </div>
          </div>
          <div className="row">
            <div className="col-lg-12">
              {/* Hero Company Slider Start */}
              <div className="hero-company-slider">
                <p>
                  We're Trusted by more than <span className="counter">{clients.length}</span>+
                  companies
                </p>
                <div
                  style={{
                    ...containerStyle,
                    cursor: isDragging ? 'grabbing' : 'grab'
                  }}
                  onMouseDown={handleMouseDown}
                  onMouseMove={handleMouseMove}
                  onMouseUp={handleMouseUp}
                  onMouseLeave={handleMouseLeave}
                  onTouchStart={handleTouchStart}
                  onTouchMove={handleTouchMove}
                  onTouchEnd={handleTouchEnd}
                >
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '20px',
                    transition: isDragging ? 'none' : 'transform 2s ease-in-out',
                    transform: `translateX(-${scrollLeft}px)`
                  }}>
                    {renderClientItems()}
                  </div>
                </div>
              </div>
              {/* Hero Company Slider End */}
            </div>
          </div>
        </div>
      </div>
      {/* Hero Section End */}
    </>
  );
};

export default HomeImageHero;
