'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';

interface Service {
  id: string;
  name: string;
  description: string;
  iconClass: string;
  price: number;
  featured: boolean;
  category: {
    id: string;
    name: string;
  } | null;
}

// Fallback industries in case API fails or no featured services
const fallbackIndustries = [
  'Healthcare',
  'Finance and Banking',
  'Legal and Law Firms',
  'Government and Public Sector',
  'Technology and Software'
];

export default function ScrollingTicker() {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [displayItems, setDisplayItems] = useState<string[]>(fallbackIndustries);

  useEffect(() => {
    const fetchFeaturedServices = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/services/featured');
        const data = await response.json();

        if (data.success && data.services && data.services.length > 0) {
          setServices(data.services);
          // Extract service names for display
          const serviceNames = data.services.map((service: Service) => service.name);
          setDisplayItems(serviceNames);
        } else {
          // Use fallback if no featured services
          setDisplayItems(fallbackIndustries);
        }
      } catch (error) {
        console.error('Error fetching featured services:', error);
        // Use fallback on error
        setDisplayItems(fallbackIndustries);
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedServices();
  }, []);

  return (
    <>
      {/* Scrolling Ticker Section Start */}
      <div className="our-scrolling-ticker">
        {/* Scrolling Ticker Start */}
        <div className="scrolling-ticker-box" style={{ overflow: 'hidden' }}>
          <div className="scrolling-content">
            {displayItems.map((item, index) => (
              <span key={index}>
                <Image
                  src="/images/star-icon.svg"
                  alt="Star"
                  width={20}
                  height={20}
                  style={{ display: 'inline-block', verticalAlign: 'middle', marginRight: '8px' }}
                />
                {item}
              </span>
            ))}
            {/* Duplicate for seamless loop */}
            {displayItems.map((item, index) => (
              <span key={`duplicate-${index}`}>
                <Image
                  src="/images/star-icon.svg"
                  alt="Star"
                  width={20}
                  height={20}
                  style={{ display: 'inline-block', verticalAlign: 'middle', marginRight: '8px' }}
                />
                {item}
              </span>
            ))}
          </div>
          <div className="scrolling-content">
            {displayItems.map((item, index) => (
              <span key={`second-${index}`}>
                <Image
                  src="/images/star-icon.svg"
                  alt="Star"
                  width={20}
                  height={20}
                  style={{ display: 'inline-block', verticalAlign: 'middle', marginRight: '8px' }}
                />
                {item}
              </span>
            ))}
            {/* Duplicate for seamless loop */}
            {displayItems.map((item, index) => (
              <span key={`second-duplicate-${index}`}>
                <Image
                  src="/images/star-icon.svg"
                  alt="Star"
                  width={20}
                  height={20}
                  style={{ display: 'inline-block', verticalAlign: 'middle', marginRight: '8px' }}
                />
                {item}
              </span>
            ))}
          </div>
        </div>
        {/* Scrolling Ticker End */}
      </div>
      {/* Scrolling Ticker Section End */}
    </>
  );
}
